/**
 * AI Services Integration
 * Production-ready implementations for DeepSeek R1, Llama, and other AI services
 */

import { logger } from '../utils/logger';

// AI Service Types
export interface AIServiceConfig {
  endpoint: string;
  key: string;
  model: string;
  enabled: boolean;
}

export interface AIRequest {
  prompt: string;
  systemPrompt?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  context?: string[];
  metadata?: any;
}

export interface AIResponse {
  content: string;
  reasoning?: string;
  confidence: number;
  tokensUsed: number;
  model: string;
  processingTime: number;
}

export interface EmbeddingRequest {
  text: string;
  model?: string;
}

export interface EmbeddingResponse {
  embedding: number[];
  dimensions: number;
  model: string;
  tokensUsed: number;
}

// DeepSeek R1 Service - Advanced Reasoning AI
export class DeepSeekR1Service {
  private config: AIServiceConfig;
  private client: any;

  constructor() {
    this.config = {
      endpoint: process.env.AI_DEEPSEEK_R1_ENDPOINT || '',
      key: process.env.AI_DEEPSEEK_R1_KEY || '',
      model: process.env.AI_DEEPSEEK_R1_DEFAULT_MODEL || 'deepseek-r1-chat',
      enabled: process.env.AI_DEEPSEEK_R1_ENABLED === 'true'
    };
  }

  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      logger.warn('DeepSeek R1 service is disabled');
      return;
    }

    try {
      // Initialize Azure OpenAI client for DeepSeek R1
      const { OpenAIClient, AzureKeyCredential } = require('@azure/openai');
      this.client = new OpenAIClient(this.config.endpoint, new AzureKeyCredential(this.config.key));
      logger.info('DeepSeek R1 service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize DeepSeek R1 service', { error });
      throw error;
    }
  }

  async reason(request: AIRequest): Promise<AIResponse> {
    if (!this.client) {
      await this.initialize();
    }

    const startTime = Date.now();

    try {
      const messages = [
        {
          role: 'system',
          content: request.systemPrompt || 'You are DeepSeek R1, an advanced reasoning AI. Think step by step and provide detailed reasoning for your conclusions.'
        },
        {
          role: 'user',
          content: request.prompt
        }
      ];

      // Add context if provided
      if (request.context && request.context.length > 0) {
        messages.splice(1, 0, {
          role: 'system',
          content: `Context information:\n${request.context.join('\n\n')}`
        });
      }

      const response = await this.client.getChatCompletions(this.config.model, messages, {
        maxTokens: request.maxTokens || 4000,
        temperature: request.temperature || 0.7,
        topP: request.topP || 0.9
      });

      const choice = response.choices[0];
      const processingTime = Date.now() - startTime;

      return {
        content: choice.message.content,
        reasoning: this.extractReasoning(choice.message.content),
        confidence: this.calculateConfidence(choice),
        tokensUsed: response.usage?.totalTokens || 0,
        model: this.config.model,
        processingTime
      };
    } catch (error) {
      logger.error('DeepSeek R1 reasoning failed', { error, request: request.prompt });
      throw error;
    }
  }

  async generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    if (!this.client) {
      await this.initialize();
    }

    try {
      const embeddingModel = request.model || process.env.AI_DEEPSEEK_R1_DEFAULT_EMBEDDING_MODEL || 'deepseek-embedding';
      
      const response = await this.client.getEmbeddings(embeddingModel, [request.text]);
      
      return {
        embedding: response.data[0].embedding,
        dimensions: response.data[0].embedding.length,
        model: embeddingModel,
        tokensUsed: response.usage?.totalTokens || 0
      };
    } catch (error) {
      logger.error('DeepSeek R1 embedding generation failed', { error });
      throw error;
    }
  }

  private extractReasoning(content: string): string {
    // Extract reasoning steps from DeepSeek R1 response
    const reasoningPatterns = [
      /Let me think step by step:(.*?)(?=\n\n|$)/s,
      /Reasoning:(.*?)(?=\n\n|$)/s,
      /Analysis:(.*?)(?=\n\n|$)/s
    ];

    for (const pattern of reasoningPatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return '';
  }

  private calculateConfidence(choice: any): number {
    // Calculate confidence based on response characteristics
    if (choice.finishReason === 'stop') {
      return 0.9;
    } else if (choice.finishReason === 'length') {
      return 0.7;
    }
    return 0.5;
  }
}

// Llama Service - Content Generation AI
export class LlamaService {
  private config: AIServiceConfig;
  private client: any;

  constructor() {
    this.config = {
      endpoint: process.env.AI_LLAMA_ENDPOINT || '',
      key: process.env.AI_LLAMA_KEY || '',
      model: process.env.AI_LLAMA_DEFAULT_MODEL || 'llama-3-3-70b-instruct',
      enabled: process.env.AI_LLAMA_ENABLED === 'true'
    };
  }

  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      logger.warn('Llama service is disabled');
      return;
    }

    try {
      const { OpenAIClient, AzureKeyCredential } = require('@azure/openai');
      this.client = new OpenAIClient(this.config.endpoint, new AzureKeyCredential(this.config.key));
      logger.info('Llama service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Llama service', { error });
      throw error;
    }
  }

  async generateContent(request: AIRequest): Promise<AIResponse> {
    if (!this.client) {
      await this.initialize();
    }

    const startTime = Date.now();

    try {
      const messages = [
        {
          role: 'system',
          content: request.systemPrompt || 'You are Llama, a helpful AI assistant specialized in content generation. Create high-quality, engaging content.'
        },
        {
          role: 'user',
          content: request.prompt
        }
      ];

      const response = await this.client.getChatCompletions(this.config.model, messages, {
        maxTokens: request.maxTokens || 2000,
        temperature: request.temperature || 0.8,
        topP: request.topP || 0.95
      });

      const choice = response.choices[0];
      const processingTime = Date.now() - startTime;

      return {
        content: choice.message.content,
        confidence: this.calculateContentQuality(choice.message.content),
        tokensUsed: response.usage?.totalTokens || 0,
        model: this.config.model,
        processingTime
      };
    } catch (error) {
      logger.error('Llama content generation failed', { error, request: request.prompt });
      throw error;
    }
  }

  private calculateContentQuality(content: string): number {
    // Simple content quality assessment
    const wordCount = content.split(/\s+/).length;
    const sentenceCount = content.split(/[.!?]+/).length;
    const avgWordsPerSentence = wordCount / sentenceCount;

    // Quality factors
    let quality = 0.5;
    
    if (wordCount > 50) quality += 0.1;
    if (avgWordsPerSentence > 10 && avgWordsPerSentence < 25) quality += 0.2;
    if (content.includes('\n')) quality += 0.1; // Has structure
    if (!/(.)\1{3,}/.test(content)) quality += 0.1; // No repetitive patterns

    return Math.min(quality, 1.0);
  }
}

// AI Service Manager - Orchestrates all AI services
export class AIServiceManager {
  private deepSeekR1: DeepSeekR1Service;
  private llama: LlamaService;
  private initialized: boolean = false;

  constructor() {
    this.deepSeekR1 = new DeepSeekR1Service();
    this.llama = new LlamaService();
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await Promise.all([
        this.deepSeekR1.initialize(),
        this.llama.initialize()
      ]);
      
      this.initialized = true;
      logger.info('AI Service Manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize AI Service Manager', { error });
      throw error;
    }
  }

  async reason(prompt: string, context?: string[], options?: Partial<AIRequest>): Promise<AIResponse> {
    await this.initialize();
    
    return this.deepSeekR1.reason({
      prompt,
      context,
      ...options
    });
  }

  async generateContent(prompt: string, options?: Partial<AIRequest>): Promise<AIResponse> {
    await this.initialize();
    
    return this.llama.generateContent({
      prompt,
      ...options
    });
  }

  async generateEmbeddings(text: string, model?: string): Promise<EmbeddingResponse> {
    await this.initialize();
    
    return this.deepSeekR1.generateEmbeddings({ text, model });
  }

  getDeepSeekR1(): DeepSeekR1Service {
    return this.deepSeekR1;
  }

  getLlama(): LlamaService {
    return this.llama;
  }
}

// Export singleton instance
export const aiServices = new AIServiceManager();
