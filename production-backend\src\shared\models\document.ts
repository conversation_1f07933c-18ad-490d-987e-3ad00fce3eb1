/**
 * Document Models and Interfaces
 * Defines the structure for document-related data models
 */

export interface Document {
  id: string;
  name: string;
  description?: string;
  blobName: string;
  contentType: string;
  size: number;
  organizationId: string;
  projectId?: string;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  status: DocumentStatus;
  metadata?: DocumentMetadata;
  content?: string;
  extractedText?: string;
  tags?: string[];
  categories?: string[];
  tenantId: string;
  version?: number;
  parentDocumentId?: string;
  isTemplate?: boolean;
  templateData?: any;
}

export enum DocumentStatus {
  UPLOADING = 'UPLOADING',
  PROCESSING = 'PROCESSING',
  PROCESSED = 'PROCESSED',
  READY = 'READY',
  ERROR = 'ERROR',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED',
  ENHANCED = 'ENHANCED',
  SIGNED = 'SIGNED'
}

export interface DocumentMetadata {
  originalFileName?: string;
  uploadedAt?: string;
  processedAt?: string;
  processingTime?: number;
  extractionMethod?: string;
  aiAnalysis?: AIAnalysisResult;
  signatures?: DocumentSignature[];
  versions?: DocumentVersion[];
  shares?: DocumentShare[];
  comments?: DocumentComment[];
  enhancementHistory?: DocumentEnhancement[];
  [key: string]: any;
}

export interface AIAnalysisResult {
  confidence: number;
  entities: ExtractedEntity[];
  keyValuePairs: KeyValuePair[];
  tables: ExtractedTable[];
  documentType: string;
  language: string;
  insights: string[];
  summary?: string;
  sentiment?: {
    score: number;
    label: 'positive' | 'negative' | 'neutral';
  };
}

export interface ExtractedEntity {
  text: string;
  type: string;
  confidence: number;
  boundingBox?: BoundingBox;
  properties?: { [key: string]: any };
}

export interface KeyValuePair {
  key: string;
  value: string;
  confidence: number;
  keyBoundingBox?: BoundingBox;
  valueBoundingBox?: BoundingBox;
}

export interface ExtractedTable {
  rows: TableRow[];
  columnCount: number;
  rowCount: number;
  boundingBox?: BoundingBox;
}

export interface TableRow {
  cells: TableCell[];
}

export interface TableCell {
  text: string;
  rowIndex: number;
  columnIndex: number;
  rowSpan?: number;
  columnSpan?: number;
  boundingBox?: BoundingBox;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface DocumentSignature {
  id: string;
  signerId: string;
  signerName: string;
  signerEmail: string;
  signedAt: string;
  signatureData: string;
  signatureType: 'digital' | 'electronic' | 'handwritten';
  position: {
    page: number;
    x: number;
    y: number;
    width: number;
    height: number;
  };
  certificateInfo?: {
    issuer: string;
    subject: string;
    validFrom: string;
    validTo: string;
    serialNumber: string;
  };
}

export interface DocumentVersion {
  id: string;
  documentId: string;
  version: number;
  blobName: string;
  size: number;
  createdBy: string;
  createdAt: string;
  changes: string[];
  comment?: string;
  isActive: boolean;
}

export interface DocumentShare {
  id: string;
  documentId: string;
  sharedWith: string; // userId or email
  sharedBy: string;
  sharedAt: string;
  permissions: SharePermission[];
  expiresAt?: string;
  accessCount: number;
  lastAccessedAt?: string;
  message?: string;
  status: 'active' | 'expired' | 'revoked';
}

export enum SharePermission {
  VIEW = 'VIEW',
  COMMENT = 'COMMENT',
  EDIT = 'EDIT',
  DOWNLOAD = 'DOWNLOAD',
  SHARE = 'SHARE',
  DELETE = 'DELETE'
}

export interface DocumentComment {
  id: string;
  documentId: string;
  userId: string;
  userName: string;
  content: string;
  createdAt: string;
  updatedAt?: string;
  parentCommentId?: string;
  position?: {
    page: number;
    x: number;
    y: number;
  };
  status: 'active' | 'deleted' | 'hidden';
  mentions?: string[]; // userIds
  attachments?: string[]; // file URLs
}

export interface DocumentEnhancement {
  id: string;
  documentId: string;
  enhancedDocumentId: string;
  enhancementType: string;
  options: any;
  result: {
    originalSize: number;
    enhancedSize: number;
    qualityScore: number;
    improvements: string[];
    processingTime: number;
  };
  createdBy: string;
  createdAt: string;
  organizationId: string;
  projectId?: string;
  tenantId: string;
}

export interface DocumentProcessingRequest {
  documentId?: string;
  documentUrl?: string;
  documentContent?: string;
  options?: {
    extractTables?: boolean;
    extractKeyValuePairs?: boolean;
    extractEntities?: boolean;
    performOCR?: boolean;
    analyzeLayout?: boolean;
    detectLanguage?: boolean;
    generateSummary?: boolean;
    analyzeSentiment?: boolean;
  };
}

export interface DocumentProcessingResponse {
  documentId: string;
  documentType: string;
  extractedText: string;
  classification: {
    documentType: string;
    confidence: number;
    categories: string[];
  };
  fields: KeyValuePair[];
  processingResults: {
    success: boolean;
    processingTime: number;
    modelUsed: string;
    errors?: string[];
  };
  aiAnalysis?: AIAnalysisResult;
}

export interface DocumentSearchRequest {
  query: string;
  filters?: {
    organizationId?: string;
    projectId?: string;
    documentType?: string;
    createdBy?: string;
    dateRange?: {
      start: string;
      end: string;
    };
    tags?: string[];
    categories?: string[];
    status?: DocumentStatus[];
  };
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  pagination?: {
    page: number;
    limit: number;
  };
  includeContent?: boolean;
  highlightMatches?: boolean;
}

export interface DocumentSearchResponse {
  documents: Document[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  facets?: {
    [key: string]: { value: string; count: number }[];
  };
  suggestions?: string[];
  processingTime: number;
}
