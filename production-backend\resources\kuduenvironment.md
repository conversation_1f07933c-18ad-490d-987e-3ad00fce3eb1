Index
System Info
App Settings
Connection Strings
Environment variables
PATH
HTTP Headers
Server variables
System info
System up time: 07:58:01.4530000
OS version: Microsoft Windows NT 10.0.20348.0
64 bit system: True
64 bit process: False
Processor count: 2
Machine name: 10-30-16-101
Instance id: facb7caf14ce7aea5e68ac7dc71d1e73a5728ec46c959a87a969930b9620a821
Short instance id: facb7c
CLR version: 4.0.30319.42000
System directory: C:\Windows\system32
Current working directory: C:\Windows\system32
IIS command line: C:\Windows\SysWOW64\inetsrv\w3wp.exe -ap "~1hepzlogic" -v "v4.0" -a "\\.\pipe\iisipm90a96880-1ec4-49d8-be69-7ddd55437a56" -h "D:\DWASFiles\Sites\#1hepzlogic\Config\applicationhost.config" -w "D:\DWASFiles\Sites\#1hepzlogic\Config\rootweb.config" -m 0 -t 20 -ta 0
C:\home usage: 104,857,600 MB total; 104,857,431 MB free
C:\local usage: 500 MB total; 423 MB free
AppSettings
aspnet:PortableCompilationOutput = true
aspnet:PortableCompilationOutputSnapshotType = Microsoft.Web.Compilation.Snapshots.SnapshotHelper, Microsoft.Web.Compilation.Snapshots, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35
aspnet:DisableFcnDaclRead = true
SCM_GIT_USERNAME = windowsazure
SCM_GIT_EMAIL = windowsazure
webpages:Version = *******
webpages:Enabled = true
webactivator:assembliesToScan = Kudu.Services.Web
SEARCH_SERVICE_ENDPOINT = https://hepzaisearch.search.windows.net
AZURE_AD_B2C_SIGNIN_FLOW = B2C_1_SI
COSMOS_DB_ENDPOINT = https://hepz.documents.azure.com:443/
NOTIFICATION_HUB_CONNECTION_STRING = Endpoint=sb://hepzdocs.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=cQEXAxQ2FZH/2YFaba3QQwboj8SkScjIzlWywTYsoLQ=
ENABLE_DOCUMENT_VERSIONING = true
AZURE_AD_B2C_PASSWORD_RESET_FLOW = B2C_1_passwordreset1
LEMONSQUEEZY_WEBHOOK_URL = https://localhost:7071/api/webhooks/lemonsqueezy
SEARCH_SERVICE_KEY = T4QAOne7RPThcrVQD8ytShLEcsHuIKG3eXGeDMINu8AzSeDkeHk6
EVENT_GRID_TOPIC_NAME = hepzeg
REDIS_CONNECTION_STRING = rediss://hepzbackend.eastus.redis.azure.net:10000
AI_VISION_KEY = 4di4wjYaCkhFPFq9Et7p1KolimgCIZPRJD8YVFFV8kqiPf2rDYYgJQQJ99BBACYeBjFXJ3w3AAAFACOG10pk
AI_DEEPSEEK_R1_ENABLED = true
REDIS_COMMAND_TIMEOUT = 5000
SIGNALR_HUB_NAME = hepztech
REDIS_CONNECT_TIMEOUT = 10000
AI_LLAMA_ENABLED = true
AZURE_AD_B2C_SIGNUP_FLOW = B2C_1_SU
NODE_ENV = production
POSTMARK_FROM_EMAIL = <EMAIL>
PERFORMANCE_METRICS_INTERVAL = 60000
DATABASE_MONITORING_ENABLED = true
DEAD_LETTER_CONTAINER = dead-letter-events
WEBSITE_ENABLE_SYNC_UPDATE_SITE = true
REDIS_RETRY_DELAY = 1000
LEMONSQUEEZY_API_KEY = eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************.WoI-mwuE1grCUm20aGVWjDlebmGegOWBgndxB8rweMphqma8yzt2GAN8noLbWnJzVJfShTtg9g-MhnyvxPIYIfFEgutBRS-y4bgau-1m_kRBnLHM1MSh5xfZ_qF3W-ye_uJATR1nXMD14ha7tUveKtZfZDmz4LJngup_9QdCOtpEaRrZIl302JZhpf8NX8AP3oQaQ8SpbFnxntasNMf6Z3-0QhgrSqjOk0BVccu2t7g1xrA501SvIhYxWSbwIA7qgdMgVMdHLWsiXb5Zff4lQcYbFGO-orP1zTc8AZPlRWGzZ2YLCNs11dxvF6sya0AsuF0EOVodlaRyasILKdYXcBeaYNeNt_RI5iTGifnxWHE5qpE1Pf_AmdvMyaQrazFTk5VJcEE9cWRg5aHDZaDXYhjjhYJvf-2Xit0W8Y1nRg1CEh-Nc_z9Vf83eThS46yncTMnIsxPRrtPyFDI3vXVWPEFPdXkDbjp4UqLn52aXNqNwunDban_wFTrGegn23SL
AI_DEEPSEEK_R1_ENDPOINT = https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com
SENTRY_DEBUG = false
SEARCH_SEMANTIC_ENABLED = true
DEFAULT_FROM_EMAIL = <EMAIL>
APPLICATIONINSIGHTS_CONNECTION_STRING = InstrumentationKey=3b8fc07d-020b-42f6-aad9-cafdeec0c80c;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=253e34d3-fd53-4bac-b5a9-10006329b097
WEBSITE_SITE_NAME = hepzlogic
AZURE_AD_B2C_TENANT_NAME = hepzdocs
SENTRY_ENVIRONMENT = production
SENTRY_DSN = https://<EMAIL>/****************
AI_IMAGE_ANALYSIS_KEY = 4di4wjYaCkhFPFq9Et7p1KolimgCIZPRJD8YVFFV8kqiPf2rDYYgJQQJ99BBACYeBjFXJ3w3AAAFACOG10pk
SEARCH_VECTOR_ENABLED = true
AI_DEEPSEEK_R1_KEY = QZoihs6NTgSoGhmeUIvRyOJBvkTDpUlj
AI_IMAGE_ANALYSIS_ENDPOINT = https://docucontextocr.cognitiveservices.azure.com/
AZURE_STORAGE_CONNECTION_STRING = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
AZURE_AD_B2C_TENANT_ID = 4824d1a3-08f0-4377-aa1f-83ca62a2af46
AI_LLAMA_DEFAULT_MODEL = llama-3-3-70b-instruct
ENABLE_CIRCUIT_BREAKER = true
WEBSITE_DEFAULT_HOSTNAME = hepzlogic.azurewebsites.net
TEMPLATES_CONTAINER = templates
CORS_ORIGIN = http://localhost:3000
REDIS_DATABASE = 0
EVENT_GRID_TOPIC_ENDPOINT = https://hepzeg.eastus-1.eventgrid.azure.net/api/events
FUNCTIONS_NODE_BLOCK_ON_ENTRY_POINT_ERROR = true
DOCUMENT_CONTAINER = documents
WEBSITE_RUN_FROM_PACKAGE = 1
AI_LLAMA_ENDPOINT = https://Llama-3-3-70B-Instruct-finkl.eastus.models.ai.azure.com
LOG_LEVEL = info
AzureWebJobsFeatureFlags = EnableWorkerIndexing
EVENT_GRID_RETRY_ATTEMPTS = 3
SERVICE_BUS_CONNECTION_STRING = Endpoint=Endpoint=sb://hepzbackend.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=Nwgjqoxc4M0KxwM1vpB4ysHFL9P4bXAUS+ASbDQ9sBo=
ALLOWED_ORIGINS = http://localhost:3000,https://your-frontend-domain.com
WEBSITE_NODE_DEFAULT_VERSION = 20.18.3
CACHE_ENABLED = true
ScmType = GitHubAction
THUMBNAIL_CONTAINER = thumbnails
WEBSITE_CONTENTAZUREFILECONNECTIONSTRING = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
FUNCTIONS_EXTENSION_VERSION = ~4
AI_DOCUMENT_INTELLIGENCE_DEFAULT_MODEL_ID = prebuilt-document
LEMONSQUEEZY_WEBHOOK_SECRET = lemonmgmtsecret
SEARCH_SERVICE_ENABLED = true
SENTRY_REPORTABLE_STATUS_CODES = 500,503,504
NOTIFICATION_HUB_NAME = mainhub
WEBHOOK_ENDPOINTS = {"default":"https://localhost:7071/api/webhooks"}
AZURE_DEEPSEEK_ENDPOINT = https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com
DEFAULT_RATE_LIMIT_WINDOW = 60000
AI_LLAMA_KEY = k7aBugiIXIGrbRDtuZvivomeiNuWrrW8
PERFORMANCE_METRICS_ENABLED = true
AI_VISION_ENDPOINT = https://docucontextocr.cognitiveservices.azure.com/
WEBSITE_AUTH_ENABLED = False
REDIS_PORT = 10000
AzureWebJobsStorage = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
REDIS_ENABLED = true
COSMOS_DB_DATABASE = hepz
DEFAULT_RATE_LIMIT = 100
AZURE_AD_B2C_CLIENT_SECRET = ****************************************
AI_IMAGE_ANALYSIS_API_VERSION = 2023-04-01-preview
CORS_ALLOWED_ORIGINS = http://localhost:3000,https://your-frontend-domain.com
MONITORING_TOPIC_NAME = monitoring-events
REDIS_TLS = true
ENABLE_RATE_LIMITING = true
AZURE_AD_B2C_PROFILE_EDIT_FLOW = B2C_1_profileedit1
EVENT_GRID_ENABLED = true
REDIS_ENABLE_OFFLINE_QUEUE = false
SIGNALR_CONNECTION_STRING = Endpoint=Endpoint=https://hepztech.service.signalr.net;AccessKey=9SSBeg7BwC71eF74gZgCTSASGF5kNSUMTMIQbmbBlbdmQ0RLKLbGJQQJ99BEACYeBjFXJ3w3AAAAASRSrRRE;Version=1.0;
EVENT_GRID_TIMEOUT_MS = 30000
COSMOS_DB_CONNECTION_STRING = AccountEndpoint=https://hepz.documents.azure.com:443/;AccountKey=****************************************************************************************;
ENABLE_CACHING = true
AZURE_AD_B2C_CLIENT_ID = a2a369c1-cf18-40e8-b41b-102b02482c51
EXPORTS_CONTAINER = exports
CACHE_TTL = 300000
AI_IMAGE_ANALYSIS_ENABLED = true
SEARCH_INDEX_NAME = documents
SENTRY_ENABLED = true
COSMOS_DB_KEY = ****************************************************************************************
WEBSITE_CONTENTSHARE = hepzlogic9db4
POSTMARK_API_KEY = ************************************
AI_DEEPSEEK_R1_DEFAULT_MODEL = deepseek-r1-chat
AI_DOCUMENT_INTELLIGENCE_KEY = ****************************************************
AZURE_AD_B2C_CUSTOM_ATTRIBUTES = extension_SubscriptionPlan,extension_SubscriptionStatus
EVENT_GRID_TOPIC_KEY = y4rzs8d6szrDnHSvaJkC9o0mmrusgGeuo7wLsjcT5ot1Ap4dlWDhJQQJ99BEACYeBjFXJ3w3AAABAZEGM1bk
WEBSITE_SLOT_NAME = Production
REDIS_MAX_RETRIES = 3
SLOW_QUERY_THRESHOLD = 1000
PROCESSED_CONTAINER = processed
DEAD_LETTER_QUEUE_NAME = dead-letter-queue
SEARCH_SEMANTIC_CONFIG = default
AI_DEEPSEEK_R1_DEFAULT_EMBEDDING_MODEL = deepseek-embedding
FUNCTIONS_WORKER_RUNTIME = node
REDIS_HOST = hepzbackend.eastus.redis.azure.net
LEMONSQUEEZY_STORE_ID = 80076
AZURE_AD_B2C_AUTHORITY_DOMAIN = hepzdocs.b2clogin.com
REDIS_PASSWORD = uYB4CoTMNKayqhchSzci7CEG6oqpeRhZVAzCaEnHa70=
AI_DOCUMENT_INTELLIGENCE_ENDPOINT = https://doucucontextdocuintell.cognitiveservices.azure.com/
SENTRY_TRACES_SAMPLE_RATE = 1.0
Connection Strings
LocalSqlServer
ConnectionString = data source=.\SQLEXPRESS;Integrated Security=SSPI;AttachDBFilename=|DataDirectory|aspnetdb.mdf;User Instance=true
ProviderName = System.Data.SqlClient
LocalMySqlServer
ConnectionString =
ProviderName =
Environment variables
AI_DEEPSEEK_R1_DEFAULT_EMBEDDING_MODEL = deepseek-embedding
AI_DEEPSEEK_R1_DEFAULT_MODEL = deepseek-r1-chat
AI_DEEPSEEK_R1_ENABLED = true
AI_DEEPSEEK_R1_ENDPOINT = https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com
AI_DEEPSEEK_R1_KEY = QZoihs6NTgSoGhmeUIvRyOJBvkTDpUlj
AI_DOCUMENT_INTELLIGENCE_DEFAULT_MODEL_ID = prebuilt-document
AI_DOCUMENT_INTELLIGENCE_ENDPOINT = https://doucucontextdocuintell.cognitiveservices.azure.com/
AI_DOCUMENT_INTELLIGENCE_KEY = ****************************************************
AI_IMAGE_ANALYSIS_API_VERSION = 2023-04-01-preview
AI_IMAGE_ANALYSIS_ENABLED = true
AI_IMAGE_ANALYSIS_ENDPOINT = https://docucontextocr.cognitiveservices.azure.com/
AI_IMAGE_ANALYSIS_KEY = 4di4wjYaCkhFPFq9Et7p1KolimgCIZPRJD8YVFFV8kqiPf2rDYYgJQQJ99BBACYeBjFXJ3w3AAAFACOG10pk
AI_LLAMA_DEFAULT_MODEL = llama-3-3-70b-instruct
AI_LLAMA_ENABLED = true
AI_LLAMA_ENDPOINT = https://Llama-3-3-70B-Instruct-finkl.eastus.models.ai.azure.com
AI_LLAMA_KEY = k7aBugiIXIGrbRDtuZvivomeiNuWrrW8
AI_VISION_ENDPOINT = https://docucontextocr.cognitiveservices.azure.com/
AI_VISION_KEY = 4di4wjYaCkhFPFq9Et7p1KolimgCIZPRJD8YVFFV8kqiPf2rDYYgJQQJ99BBACYeBjFXJ3w3AAAFACOG10pk
ALLOWED_ORIGINS = http://localhost:3000,https://your-frontend-domain.com
ALLUSERSPROFILE = C:\local\ProgramData
APP_POOL_CONFIG = D:\DWASFiles\Sites\#1hepzlogic\Config\applicationhost.config
APP_POOL_ID = ~1hepzlogic
APPDATA = C:\local\AppData
APPLICATIONINSIGHTS_CONNECTION_STRING = InstrumentationKey=3b8fc07d-020b-42f6-aad9-cafdeec0c80c;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=253e34d3-fd53-4bac-b5a9-10006329b097
APPSETTING_AI_DEEPSEEK_R1_DEFAULT_EMBEDDING_MODEL = deepseek-embedding
APPSETTING_AI_DEEPSEEK_R1_DEFAULT_MODEL = deepseek-r1-chat
APPSETTING_AI_DEEPSEEK_R1_ENABLED = true
APPSETTING_AI_DEEPSEEK_R1_ENDPOINT = https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com
APPSETTING_AI_DEEPSEEK_R1_KEY = QZoihs6NTgSoGhmeUIvRyOJBvkTDpUlj
APPSETTING_AI_DOCUMENT_INTELLIGENCE_DEFAULT_MODEL_ID = prebuilt-document
APPSETTING_AI_DOCUMENT_INTELLIGENCE_ENDPOINT = https://doucucontextdocuintell.cognitiveservices.azure.com/
APPSETTING_AI_DOCUMENT_INTELLIGENCE_KEY = ****************************************************
APPSETTING_AI_IMAGE_ANALYSIS_API_VERSION = 2023-04-01-preview
APPSETTING_AI_IMAGE_ANALYSIS_ENABLED = true
APPSETTING_AI_IMAGE_ANALYSIS_ENDPOINT = https://docucontextocr.cognitiveservices.azure.com/
APPSETTING_AI_IMAGE_ANALYSIS_KEY = 4di4wjYaCkhFPFq9Et7p1KolimgCIZPRJD8YVFFV8kqiPf2rDYYgJQQJ99BBACYeBjFXJ3w3AAAFACOG10pk
APPSETTING_AI_LLAMA_DEFAULT_MODEL = llama-3-3-70b-instruct
APPSETTING_AI_LLAMA_ENABLED = true
APPSETTING_AI_LLAMA_ENDPOINT = https://Llama-3-3-70B-Instruct-finkl.eastus.models.ai.azure.com
APPSETTING_AI_LLAMA_KEY = k7aBugiIXIGrbRDtuZvivomeiNuWrrW8
APPSETTING_AI_VISION_ENDPOINT = https://docucontextocr.cognitiveservices.azure.com/
APPSETTING_AI_VISION_KEY = 4di4wjYaCkhFPFq9Et7p1KolimgCIZPRJD8YVFFV8kqiPf2rDYYgJQQJ99BBACYeBjFXJ3w3AAAFACOG10pk
APPSETTING_ALLOWED_ORIGINS = http://localhost:3000,https://your-frontend-domain.com
APPSETTING_APPLICATIONINSIGHTS_CONNECTION_STRING = InstrumentationKey=3b8fc07d-020b-42f6-aad9-cafdeec0c80c;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=253e34d3-fd53-4bac-b5a9-10006329b097
APPSETTING_AZURE_AD_B2C_AUTHORITY_DOMAIN = hepzdocs.b2clogin.com
APPSETTING_AZURE_AD_B2C_CLIENT_ID = a2a369c1-cf18-40e8-b41b-102b02482c51
APPSETTING_AZURE_AD_B2C_CLIENT_SECRET = ****************************************
APPSETTING_AZURE_AD_B2C_CUSTOM_ATTRIBUTES = extension_SubscriptionPlan,extension_SubscriptionStatus
APPSETTING_AZURE_AD_B2C_PASSWORD_RESET_FLOW = B2C_1_passwordreset1
APPSETTING_AZURE_AD_B2C_PROFILE_EDIT_FLOW = B2C_1_profileedit1
APPSETTING_AZURE_AD_B2C_SIGNIN_FLOW = B2C_1_SI
APPSETTING_AZURE_AD_B2C_SIGNUP_FLOW = B2C_1_SU
APPSETTING_AZURE_AD_B2C_TENANT_ID = 4824d1a3-08f0-4377-aa1f-83ca62a2af46
APPSETTING_AZURE_AD_B2C_TENANT_NAME = hepzdocs
APPSETTING_AZURE_DEEPSEEK_ENDPOINT = https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com
APPSETTING_AZURE_STORAGE_CONNECTION_STRING = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
APPSETTING_AzureWebJobsFeatureFlags = EnableWorkerIndexing
APPSETTING_AzureWebJobsStorage = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
APPSETTING_CACHE_ENABLED = true
APPSETTING_CACHE_TTL = 300000
APPSETTING_CORS_ALLOWED_ORIGINS = http://localhost:3000,https://your-frontend-domain.com
APPSETTING_CORS_ORIGIN = http://localhost:3000
APPSETTING_COSMOS_DB_CONNECTION_STRING = AccountEndpoint=https://hepz.documents.azure.com:443/;AccountKey=****************************************************************************************;
APPSETTING_COSMOS_DB_DATABASE = hepz
APPSETTING_COSMOS_DB_ENDPOINT = https://hepz.documents.azure.com:443/
APPSETTING_COSMOS_DB_KEY = ****************************************************************************************
APPSETTING_DATABASE_MONITORING_ENABLED = true
APPSETTING_DEAD_LETTER_CONTAINER = dead-letter-events
APPSETTING_DEAD_LETTER_QUEUE_NAME = dead-letter-queue
APPSETTING_DEFAULT_FROM_EMAIL = <EMAIL>
APPSETTING_DEFAULT_RATE_LIMIT = 100
APPSETTING_DEFAULT_RATE_LIMIT_WINDOW = 60000
APPSETTING_DOCUMENT_CONTAINER = documents
APPSETTING_ENABLE_CACHING = true
APPSETTING_ENABLE_CIRCUIT_BREAKER = true
APPSETTING_ENABLE_DOCUMENT_VERSIONING = true
APPSETTING_ENABLE_RATE_LIMITING = true
APPSETTING_EVENT_GRID_ENABLED = true
APPSETTING_EVENT_GRID_RETRY_ATTEMPTS = 3
APPSETTING_EVENT_GRID_TIMEOUT_MS = 30000
APPSETTING_EVENT_GRID_TOPIC_ENDPOINT = https://hepzeg.eastus-1.eventgrid.azure.net/api/events
APPSETTING_EVENT_GRID_TOPIC_KEY = y4rzs8d6szrDnHSvaJkC9o0mmrusgGeuo7wLsjcT5ot1Ap4dlWDhJQQJ99BEACYeBjFXJ3w3AAABAZEGM1bk
APPSETTING_EVENT_GRID_TOPIC_NAME = hepzeg
APPSETTING_EXPORTS_CONTAINER = exports
APPSETTING_FUNCTIONS_EXTENSION_VERSION = ~4
APPSETTING_FUNCTIONS_NODE_BLOCK_ON_ENTRY_POINT_ERROR = true
APPSETTING_FUNCTIONS_WORKER_RUNTIME = node
APPSETTING_LEMONSQUEEZY_API_KEY = eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************.WoI-mwuE1grCUm20aGVWjDlebmGegOWBgndxB8rweMphqma8yzt2GAN8noLbWnJzVJfShTtg9g-MhnyvxPIYIfFEgutBRS-y4bgau-1m_kRBnLHM1MSh5xfZ_qF3W-ye_uJATR1nXMD14ha7tUveKtZfZDmz4LJngup_9QdCOtpEaRrZIl302JZhpf8NX8AP3oQaQ8SpbFnxntasNMf6Z3-0QhgrSqjOk0BVccu2t7g1xrA501SvIhYxWSbwIA7qgdMgVMdHLWsiXb5Zff4lQcYbFGO-orP1zTc8AZPlRWGzZ2YLCNs11dxvF6sya0AsuF0EOVodlaRyasILKdYXcBeaYNeNt_RI5iTGifnxWHE5qpE1Pf_AmdvMyaQrazFTk5VJcEE9cWRg5aHDZaDXYhjjhYJvf-2Xit0W8Y1nRg1CEh-Nc_z9Vf83eThS46yncTMnIsxPRrtPyFDI3vXVWPEFPdXkDbjp4UqLn52aXNqNwunDban_wFTrGegn23SL
APPSETTING_LEMONSQUEEZY_STORE_ID = 80076
APPSETTING_LEMONSQUEEZY_WEBHOOK_SECRET = lemonmgmtsecret
APPSETTING_LEMONSQUEEZY_WEBHOOK_URL = https://localhost:7071/api/webhooks/lemonsqueezy
APPSETTING_LOG_LEVEL = info
APPSETTING_MONITORING_TOPIC_NAME = monitoring-events
APPSETTING_NODE_ENV = production
APPSETTING_NOTIFICATION_HUB_CONNECTION_STRING = Endpoint=sb://hepzdocs.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=cQEXAxQ2FZH/2YFaba3QQwboj8SkScjIzlWywTYsoLQ=
APPSETTING_NOTIFICATION_HUB_NAME = mainhub
APPSETTING_PERFORMANCE_METRICS_ENABLED = true
APPSETTING_PERFORMANCE_METRICS_INTERVAL = 60000
APPSETTING_POSTMARK_API_KEY = ************************************
APPSETTING_POSTMARK_FROM_EMAIL = <EMAIL>
APPSETTING_PROCESSED_CONTAINER = processed
APPSETTING_REDIS_COMMAND_TIMEOUT = 5000
APPSETTING_REDIS_CONNECT_TIMEOUT = 10000
APPSETTING_REDIS_CONNECTION_STRING = rediss://hepzbackend.eastus.redis.azure.net:10000
APPSETTING_REDIS_DATABASE = 0
APPSETTING_REDIS_ENABLE_OFFLINE_QUEUE = false
APPSETTING_REDIS_ENABLED = true
APPSETTING_REDIS_HOST = hepzbackend.eastus.redis.azure.net
APPSETTING_REDIS_MAX_RETRIES = 3
APPSETTING_REDIS_PASSWORD = uYB4CoTMNKayqhchSzci7CEG6oqpeRhZVAzCaEnHa70=
APPSETTING_REDIS_PORT = 10000
APPSETTING_REDIS_RETRY_DELAY = 1000
APPSETTING_REDIS_TLS = true
APPSETTING_ScmType = GitHubAction
APPSETTING_SEARCH_INDEX_NAME = documents
APPSETTING_SEARCH_SEMANTIC_CONFIG = default
APPSETTING_SEARCH_SEMANTIC_ENABLED = true
APPSETTING_SEARCH_SERVICE_ENABLED = true
APPSETTING_SEARCH_SERVICE_ENDPOINT = https://hepzaisearch.search.windows.net
APPSETTING_SEARCH_SERVICE_KEY = T4QAOne7RPThcrVQD8ytShLEcsHuIKG3eXGeDMINu8AzSeDkeHk6
APPSETTING_SEARCH_VECTOR_ENABLED = true
APPSETTING_SENTRY_DEBUG = false
APPSETTING_SENTRY_DSN = https://<EMAIL>/****************
APPSETTING_SENTRY_ENABLED = true
APPSETTING_SENTRY_ENVIRONMENT = production
APPSETTING_SENTRY_REPORTABLE_STATUS_CODES = 500,503,504
APPSETTING_SENTRY_TRACES_SAMPLE_RATE = 1.0
APPSETTING_SERVICE_BUS_CONNECTION_STRING = Endpoint=Endpoint=sb://hepzbackend.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=Nwgjqoxc4M0KxwM1vpB4ysHFL9P4bXAUS+ASbDQ9sBo=
APPSETTING_SIGNALR_CONNECTION_STRING = Endpoint=Endpoint=https://hepztech.service.signalr.net;AccessKey=9SSBeg7BwC71eF74gZgCTSASGF5kNSUMTMIQbmbBlbdmQ0RLKLbGJQQJ99BEACYeBjFXJ3w3AAAAASRSrRRE;Version=1.0;
APPSETTING_SIGNALR_HUB_NAME = hepztech
APPSETTING_SLOW_QUERY_THRESHOLD = 1000
APPSETTING_TEMPLATES_CONTAINER = templates
APPSETTING_THUMBNAIL_CONTAINER = thumbnails
APPSETTING_WEBHOOK_ENDPOINTS = {"default":"https://localhost:7071/api/webhooks"}
APPSETTING_WEBSITE_AUTH_ENABLED = False
APPSETTING_WEBSITE_CONTENTAZUREFILECONNECTIONSTRING = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
APPSETTING_WEBSITE_CONTENTSHARE = hepzlogic9db4
APPSETTING_WEBSITE_DEFAULT_HOSTNAME = hepzlogic.azurewebsites.net
APPSETTING_WEBSITE_ENABLE_SYNC_UPDATE_SITE = true
APPSETTING_WEBSITE_NODE_DEFAULT_VERSION = 20.18.3
APPSETTING_WEBSITE_RUN_FROM_PACKAGE = 1
APPSETTING_WEBSITE_SITE_NAME = hepzlogic
APPSETTING_WEBSITE_SLOT_NAME = Production
AZURE_AD_B2C_AUTHORITY_DOMAIN = hepzdocs.b2clogin.com
AZURE_AD_B2C_CLIENT_ID = a2a369c1-cf18-40e8-b41b-102b02482c51
AZURE_AD_B2C_CLIENT_SECRET = ****************************************
AZURE_AD_B2C_CUSTOM_ATTRIBUTES = extension_SubscriptionPlan,extension_SubscriptionStatus
AZURE_AD_B2C_PASSWORD_RESET_FLOW = B2C_1_passwordreset1
AZURE_AD_B2C_PROFILE_EDIT_FLOW = B2C_1_profileedit1
AZURE_AD_B2C_SIGNIN_FLOW = B2C_1_SI
AZURE_AD_B2C_SIGNUP_FLOW = B2C_1_SU
AZURE_AD_B2C_TENANT_ID = 4824d1a3-08f0-4377-aa1f-83ca62a2af46
AZURE_AD_B2C_TENANT_NAME = hepzdocs
AZURE_DEEPSEEK_ENDPOINT = https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com
AZURE_JETTY9_CMDLINE = -Djava.net.preferIPv4Stack=true -Djetty.port=%HTTP_PLATFORM_PORT% -Djetty.base="D:\Program Files (x86)\jetty-distribution-9.1.0.v20131115" -Djetty.webapps="d:\home\site\wwwroot\webapps" -jar "D:\Program Files (x86)\jetty-distribution-9.1.0.v20131115\start.jar" etc\jetty-logging.xml
AZURE_JETTY9_HOME = C:\Program Files (x86)\jetty-distribution-9.1.0.v20131115
AZURE_JETTY93_CMDLINE = -Djava.net.preferIPv4Stack=true -Djetty.port=%HTTP_PLATFORM_PORT% -Djetty.base="D:\Program Files (x86)\jetty-distribution-9.3.25.v20180904" -Djetty.webapps="d:\home\site\wwwroot\webapps" -jar "D:\Program Files (x86)\jetty-distribution-9.3.25.v20180904\start.jar" etc\jetty-logging.xml
AZURE_JETTY93_HOME = C:\Program Files (x86)\jetty-distribution-9.3.25.v20180904
AZURE_STORAGE_CONNECTION_STRING = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
AZURE_TOMCAT10_CMDLINE = -Dport.http=%HTTP_PLATFORM_PORT% -Djava.util.logging.config.file=\"C:\Program Files\apache-tomcat-10.0.27\conf\logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Dsite.logdir=\"%HOME%\LogFiles\\" -Dsite.tempdir=\"%HOME%\site\workdir\" -classpath \"C:\Program Files\apache-tomcat-10.0.27\bin\bootstrap.jar;C:\Program Files\apache-tomcat-10.0.27\bin\tomcat-juli.jar\" -Dcatalina.base=\"C:\Program Files\apache-tomcat-10.0.27\" -Djava.io.tmpdir=\"%HOME%\site\workdir\" org.apache.catalina.startup.Bootstrap
AZURE_TOMCAT10_HOME = C:\Program Files\apache-tomcat-10.0.27
AZURE_TOMCAT101_CMDLINE = -Dport.http=%HTTP_PLATFORM_PORT% -Djava.util.logging.config.file=\"C:\Program Files\apache-tomcat-10.1.39\conf\logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Dsite.logdir=\"%HOME%\LogFiles\\" -Dsite.tempdir=\"%HOME%\site\workdir\" -classpath \"C:\Program Files\apache-tomcat-10.1.39\bin\bootstrap.jar;C:\Program Files\apache-tomcat-10.1.39\bin\tomcat-juli.jar\" -Dcatalina.base=\"C:\Program Files\apache-tomcat-10.1.39\" -Djava.io.tmpdir=\"%HOME%\site\workdir\" org.apache.catalina.startup.Bootstrap
AZURE_TOMCAT101_HOME = C:\Program Files\apache-tomcat-10.1.39
AZURE_TOMCAT110_CMDLINE = -Dport.http=%HTTP_PLATFORM_PORT% -Djava.util.logging.config.file=\"C:\Program Files\apache-tomcat-11.0.5\conf\logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Dsite.logdir=\"%HOME%\LogFiles\\" -Dsite.tempdir=\"%HOME%\site\workdir\" -classpath \"C:\Program Files\apache-tomcat-11.0.5\bin\bootstrap.jar;C:\Program Files\apache-tomcat-11.0.5\bin\tomcat-juli.jar\" -Dcatalina.base=\"C:\Program Files\apache-tomcat-11.0.5\" -Djava.io.tmpdir=\"%HOME%\site\workdir\" org.apache.catalina.startup.Bootstrap
AZURE_TOMCAT110_HOME = C:\Program Files\apache-tomcat-11.0.5
AZURE_TOMCAT7_CMDLINE = -Dport.http=%HTTP_PLATFORM_PORT% -Djava.util.logging.config.file=\"C:\Program Files (x86)\apache-tomcat-7.0.81\conf\logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Dsite.logdir=\"%HOME%\LogFiles\\" -Dsite.tempdir=\"%HOME%\site\workdir\" -classpath \"C:\Program Files (x86)\apache-tomcat-7.0.81\bin\bootstrap.jar;C:\Program Files (x86)\apache-tomcat-7.0.81\bin\tomcat-juli.jar\" -Dcatalina.base=\"C:\Program Files (x86)\apache-tomcat-7.0.81\" -Djava.io.tmpdir=\"%HOME%\site\workdir\" org.apache.catalina.startup.Bootstrap
AZURE_TOMCAT7_HOME = C:\Program Files (x86)\apache-tomcat-7.0.81
AZURE_TOMCAT8_CMDLINE = -Dport.http=%HTTP_PLATFORM_PORT% -Djava.util.logging.config.file=\"C:\Program Files (x86)\apache-tomcat-8.0.46\conf\logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Dsite.logdir=\"%HOME%\LogFiles\\" -Dsite.tempdir=\"%HOME%\site\workdir\" -classpath \"C:\Program Files (x86)\apache-tomcat-8.0.46\bin\bootstrap.jar;C:\Program Files (x86)\apache-tomcat-8.0.46\bin\tomcat-juli.jar\" -Dcatalina.base=\"C:\Program Files (x86)\apache-tomcat-8.0.46\" -Djava.io.tmpdir=\"%HOME%\site\workdir\" org.apache.catalina.startup.Bootstrap
AZURE_TOMCAT8_HOME = C:\Program Files (x86)\apache-tomcat-8.0.46
AZURE_TOMCAT85_CMDLINE = -Dport.http=%HTTP_PLATFORM_PORT% -Djava.util.logging.config.file=\"C:\Program Files\apache-tomcat-8.5.100\conf\logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Dsite.logdir=\"%HOME%\LogFiles\\" -Dsite.tempdir=\"%HOME%\site\workdir\" -classpath \"C:\Program Files\apache-tomcat-8.5.100\bin\bootstrap.jar;C:\Program Files\apache-tomcat-8.5.100\bin\tomcat-juli.jar\" -Dcatalina.base=\"C:\Program Files\apache-tomcat-8.5.100\" -Djava.io.tmpdir=\"%HOME%\site\workdir\" org.apache.catalina.startup.Bootstrap
AZURE_TOMCAT85_HOME = C:\Program Files\apache-tomcat-8.5.100
AZURE_TOMCAT90_CMDLINE = -Dport.http=%HTTP_PLATFORM_PORT% -Djava.util.logging.config.file=\"C:\Program Files\apache-tomcat-9.0.102\conf\logging.properties\" -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Dsite.logdir=\"%HOME%\LogFiles\\" -Dsite.tempdir=\"%HOME%\site\workdir\" -classpath \"C:\Program Files\apache-tomcat-9.0.102\bin\bootstrap.jar;C:\Program Files\apache-tomcat-9.0.102\bin\tomcat-juli.jar\" -Dcatalina.base=\"C:\Program Files\apache-tomcat-9.0.102\" -Djava.io.tmpdir=\"%HOME%\site\workdir\" org.apache.catalina.startup.Bootstrap
AZURE_TOMCAT90_HOME = C:\Program Files\apache-tomcat-9.0.102
AzureWebJobsFeatureFlags = EnableWorkerIndexing
AzureWebJobsStorage = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
CACHE_ENABLED = true
CACHE_TTL = 300000
CommonProgramFiles = C:\Program Files (x86)\Common Files
CommonProgramFiles(x86) = C:\Program Files (x86)\Common Files
CommonProgramW6432 = C:\Program Files\Common Files
COMPUTERNAME = 10-30-16-101
ComSpec = C:\Windows\system32\cmd.exe
CORS_ALLOWED_ORIGINS = http://localhost:3000,https://your-frontend-domain.com
CORS_ORIGIN = http://localhost:3000
COSMOS_DB_CONNECTION_STRING = AccountEndpoint=https://hepz.documents.azure.com:443/;AccountKey=****************************************************************************************;
COSMOS_DB_DATABASE = hepz
COSMOS_DB_ENDPOINT = https://hepz.documents.azure.com:443/
COSMOS_DB_KEY = ****************************************************************************************
DATABASE_MONITORING_ENABLED = true
DEAD_LETTER_CONTAINER = dead-letter-events
DEAD_LETTER_QUEUE_NAME = dead-letter-queue
DEFAULT_FROM_EMAIL = <EMAIL>
DEFAULT_RATE_LIMIT = 100
DEFAULT_RATE_LIMIT_WINDOW = 60000
DH_RemoteToolsInstallDir = C:\Program Files\Microsoft Visual Studio 17.0\
DOCUMENT_CONTAINER = documents
DOTNET_CLI_TELEMETRY_PROFILE = AzureKudu
DOTNET_HOSTING_OPTIMIZATION_CACHE = C:\DotNetCache
DOTNET_SKIP_FIRST_TIME_EXPERIENCE = true
DriverData = C:\Windows\System32\Drivers\DriverData
ENABLE_CACHING = true
ENABLE_CIRCUIT_BREAKER = true
ENABLE_DOCUMENT_VERSIONING = true
ENABLE_RATE_LIMITING = true
EVENT_GRID_ENABLED = true
EVENT_GRID_RETRY_ATTEMPTS = 3
EVENT_GRID_TIMEOUT_MS = 30000
EVENT_GRID_TOPIC_ENDPOINT = https://hepzeg.eastus-1.eventgrid.azure.net/api/events
EVENT_GRID_TOPIC_KEY = y4rzs8d6szrDnHSvaJkC9o0mmrusgGeuo7wLsjcT5ot1Ap4dlWDhJQQJ99BEACYeBjFXJ3w3AAABAZEGM1bk
EVENT_GRID_TOPIC_NAME = hepzeg
EXPORTS_CONTAINER = exports
FUNCTIONS_EXTENSION_VERSION = ~4
FUNCTIONS_NODE_BLOCK_ON_ENTRY_POINT_ERROR = true
FUNCTIONS_WORKER_RUNTIME = node
FUNCTIONS_WORKER_RUNTIME_VERSION = ~20
HOME = C:\home
HOME_EXPANDED = D:\DWASFiles\Sites\#1hepzlogic\VirtualDirectory0
HTTP_AUTHORITY = hepzlogic.scm.azurewebsites.net
HTTP_HOST = hepzlogic.scm.azurewebsites.net
JAVA_HOME = C:\Program Files\Java\Adoptium-Eclipse-Temurin-OpenJDK-8u442
LEMONSQUEEZY_API_KEY = eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************.WoI-mwuE1grCUm20aGVWjDlebmGegOWBgndxB8rweMphqma8yzt2GAN8noLbWnJzVJfShTtg9g-MhnyvxPIYIfFEgutBRS-y4bgau-1m_kRBnLHM1MSh5xfZ_qF3W-ye_uJATR1nXMD14ha7tUveKtZfZDmz4LJngup_9QdCOtpEaRrZIl302JZhpf8NX8AP3oQaQ8SpbFnxntasNMf6Z3-0QhgrSqjOk0BVccu2t7g1xrA501SvIhYxWSbwIA7qgdMgVMdHLWsiXb5Zff4lQcYbFGO-orP1zTc8AZPlRWGzZ2YLCNs11dxvF6sya0AsuF0EOVodlaRyasILKdYXcBeaYNeNt_RI5iTGifnxWHE5qpE1Pf_AmdvMyaQrazFTk5VJcEE9cWRg5aHDZaDXYhjjhYJvf-2Xit0W8Y1nRg1CEh-Nc_z9Vf83eThS46yncTMnIsxPRrtPyFDI3vXVWPEFPdXkDbjp4UqLn52aXNqNwunDban_wFTrGegn23SL
LEMONSQUEEZY_STORE_ID = 80076
LEMONSQUEEZY_WEBHOOK_SECRET = lemonmgmtsecret
LEMONSQUEEZY_WEBHOOK_URL = https://localhost:7071/api/webhooks/lemonsqueezy
LOCAL_EXPANDED = D:\DWASFiles\Sites\#1hepzlogic
LOCALAPPDATA = C:\local\LocalAppData
LOG_LEVEL = info
MONITORING_TOPIC_NAME = monitoring-events
NODE_ENV = production
NOTIFICATION_HUB_CONNECTION_STRING = Endpoint=sb://hepzdocs.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=cQEXAxQ2FZH/2YFaba3QQwboj8SkScjIzlWywTYsoLQ=
NOTIFICATION_HUB_NAME = mainhub
NUGET_PACKAGES = C:\home\.nuget\
NUGET_XMLDOC_MODE = skip
NUMBER_OF_PROCESSORS = 2
OS = Windows_NT
Path = C:\home\site\deployments\tools;C:\Program Files (x86)\SiteExtensions\Kudu\103.250502.001\bin\Scripts;C:\Program Files (x86)\MSBuild\14.0\Bin;C:\Program Files\Git\cmd;C:\Program Files (x86)\Microsoft Visual Studio 11.0\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files (x86)\Microsoft SQL Server\110\Tools\Binn;C:\Program Files (x86)\Microsoft SDKs\F#\3.1\Framework\v4.0;C:\Program Files\Git\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files (x86)\npm\10.8.2;C:\Program Files (x86)\bower\1.7.9;C:\Program Files (x86)\grunt\0.1.13;C:\Program Files (x86)\gulp\*******;C:\Program Files (x86)\funcpack\1.0.0;C:\Python27;C:\Program Files (x86)\PHP\v5.6;C:\Program Files (x86)\nodejs\20.18.3;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft Network Monitor 3\;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\nodejs\;C:\Program Files (x86)\Mercurial\;C:\Program Files\Git\cmd;C:\Program Files (x86)\Microsoft ASP.NET\ASP.NET Web Pages\v1.0\;;C:\Program Files (x86)\dotnet;C:\Program Files\dotnet;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\Adoptium-Eclipse-Temurin-OpenJDK-8u442\bin;
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL;.PY;.PYW
PERFORMANCE_METRICS_ENABLED = true
PERFORMANCE_METRICS_INTERVAL = 60000
POSTMARK_API_KEY = ************************************
POSTMARK_FROM_EMAIL = <EMAIL>
PROCESSED_CONTAINER = processed
PROCESSOR_ARCHITECTURE = x86
PROCESSOR_ARCHITEW6432 = AMD64
PROCESSOR_IDENTIFIER = AMD64 Family 25 Model 1 Stepping 1, AuthenticAMD
PROCESSOR_LEVEL = 25
PROCESSOR_REVISION = 0101
ProgramData = C:\local\ProgramData
ProgramFiles = C:\Program Files (x86)
ProgramFiles(x86) = C:\Program Files (x86)
ProgramW6432 = C:\Program Files
PSModulePath = C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules;C:\Program Files\WindowsPowerShell\Modules\;C:\Program Files (x86)\Microsoft SDKs\Azure\PowerShell\ResourceManager\AzureResourceManager\;C:\Program Files (x86)\Microsoft SDKs\Azure\PowerShell\ServiceManagement\;C:\Program Files (x86)\Microsoft SDKs\Azure\PowerShell\Storage\
PUBLIC = C:\Users\<USER>\
RoleInstanceId = hr0SmallDedicatedWebWorkerRole_hr0HostRole-1693-VM-27
RoleName = hr0SmallDedicatedWebWorkerRole
RoleRoot = E:
ScmType = GitHubAction
SEARCH_INDEX_NAME = documents
SEARCH_SEMANTIC_CONFIG = default
SEARCH_SEMANTIC_ENABLED = true
SEARCH_SERVICE_ENABLED = true
SEARCH_SERVICE_ENDPOINT = https://hepzaisearch.search.windows.net
SEARCH_SERVICE_KEY = T4QAOne7RPThcrVQD8ytShLEcsHuIKG3eXGeDMINu8AzSeDkeHk6
SEARCH_VECTOR_ENABLED = true
SENTRY_DEBUG = false
SENTRY_DSN = https://<EMAIL>/****************
SENTRY_ENABLED = true
SENTRY_ENVIRONMENT = production
SENTRY_REPORTABLE_STATUS_CODES = 500,503,504
SENTRY_TRACES_SAMPLE_RATE = 1.0
SERVICE_BUS_CONNECTION_STRING = Endpoint=Endpoint=sb://hepzbackend.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=Nwgjqoxc4M0KxwM1vpB4ysHFL9P4bXAUS+ASbDQ9sBo=
SIGNALR_CONNECTION_STRING = Endpoint=Endpoint=https://hepztech.service.signalr.net;AccessKey=9SSBeg7BwC71eF74gZgCTSASGF5kNSUMTMIQbmbBlbdmQ0RLKLbGJQQJ99BEACYeBjFXJ3w3AAAAASRSrRRE;Version=1.0;
SIGNALR_HUB_NAME = hepztech
SITE_BITNESS = x86
SLOW_QUERY_THRESHOLD = 1000
SystemDrive = C:
SystemRoot = C:\Windows
TEMP = C:\local\Temp
TEMPLATES_CONTAINER = templates
THUMBNAIL_CONTAINER = thumbnails
TMP = C:\local\Temp
USERDOMAIN = WORKGROUP
USERNAME = 10-30-16-101$
USERPROFILE = C:\local\UserProfile
WEBHOOK_ENDPOINTS = {"default":"https://localhost:7071/api/webhooks"}
WEBSITE_APPSERVICEAPPLOGS_TRACE_ENABLED = true
WEBSITE_AUTH_ENABLED = False
WEBSITE_AUTH_ENCRYPTION_KEY = A11446F0650A29EC8F94230C38D63E390DA2A12AEAD2763985E71D829F92EB54
WEBSITE_AUTH_SIGNING_KEY = 92C564461AE29720FE330362F40AB8609A853DB0398F06109319B70F20502D0D
WEBSITE_COMPUTE_MODE = Dynamic
WEBSITE_CONTAINER_READY = 1
WEBSITE_CONTENTAZUREFILECONNECTIONSTRING = DefaultEndpointsProtocol=https;AccountName=stdocucontex900520441468;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
WEBSITE_CONTENTSHARE = hepzlogic9db4
WEBSITE_CORS_ALLOWED_ORIGINS = https://portal.azure.com
WEBSITE_CORS_SUPPORT_CREDENTIALS = False
WEBSITE_CURRENT_STAMPNAME = waws-prod-blu-537
WEBSITE_DAAS_EXTENSIONPATH = C:\Program Files (x86)\SiteExtensions\DaaS\4.7.15125.1
WEBSITE_DEFAULT_HOSTNAME = hepzlogic.azurewebsites.net
WEBSITE_DEPLOYMENT_ID = hepzlogic
WEBSITE_DYNAMIC_CACHE = 0
WEBSITE_ELASTIC_SCALING_ENABLED = 0
WEBSITE_ENABLE_SYNC_UPDATE_SITE = true
WEBSITE_FRAMEWORK_JIT = 1
WEBSITE_FUNCTIONS_AZUREMONITOR_CATEGORIES = None
WEBSITE_HOME_STAMPNAME = waws-prod-blu-537
WEBSITE_HOSTNAME = hepzlogic.azurewebsites.net
WEBSITE_HTTPLOGGING_ENABLED = 0
WEBSITE_IIS_SITE_NAME = ~1hepzlogic
WEBSITE_INFRASTRUCTURE_IP = ************
WEBSITE_INSTANCE_ID = facb7caf14ce7aea5e68ac7dc71d1e73a5728ec46c959a87a969930b9620a821
WEBSITE_ISOLATION = pico
WEBSITE_LOCALCACHE_ENABLED = False
WEBSITE_MEMORY_LIMIT_MB = 1536
WEBSITE_NODE_DEFAULT_VERSION = 20.18.3
WEBSITE_OS = windows
WEBSITE_OWNER_NAME = a7b6fcd5-038d-41ed-9d87-2098820fd014+DocuContext-EastUSwebspace
WEBSITE_PLATFORM_VERSION = ***********
WEBSITE_RELAYS =
WEBSITE_RESOURCE_GROUP = docucontext
WEBSITE_REWRITE_TABLE =
WEBSITE_RUN_FROM_PACKAGE = 1
WEBSITE_SCM_ALWAYS_ON_ENABLED = 0
WEBSITE_SCM_SEPARATE_STATUS = 1
WEBSITE_SITE_NAME = hepzlogic
WEBSITE_SKU = Dynamic
WEBSITE_SLOT_NAME = Production
WEBSITE_VOLUME_TYPE = AzureFiles
WEBSOCKET_CONCURRENT_REQUEST_LIMIT = 35
windir = C:\Windows
windows_tracing_flags =
windows_tracing_logfile =
PATH
C:\home\site\deployments\tools
C:\Program Files (x86)\SiteExtensions\Kudu\103.250502.001\bin\Scripts
C:\Program Files (x86)\MSBuild\14.0\Bin
C:\Program Files\Git\cmd
C:\Program Files (x86)\Microsoft Visual Studio 11.0\Common7\IDE\CommonExtensions\Microsoft\TestWindow
C:\Program Files (x86)\Microsoft SQL Server\110\Tools\Binn
C:\Program Files (x86)\Microsoft SDKs\F#\3.1\Framework\v4.0
C:\Program Files\Git\bin
C:\Program Files\Git\usr\bin
C:\Program Files\Git\mingw64\bin
C:\Program Files (x86)\npm\10.8.2
C:\Program Files (x86)\bower\1.7.9
C:\Program Files (x86)\grunt\0.1.13
C:\Program Files (x86)\gulp\*******
C:\Program Files (x86)\funcpack\1.0.0
C:\Python27
C:\Program Files (x86)\PHP\v5.6
C:\Program Files (x86)\nodejs\20.18.3
C:\Windows\system32
C:\Windows
C:\Windows\System32\Wbem
C:\Windows\System32\WindowsPowerShell\v1.0\
C:\Windows\System32\OpenSSH\
C:\Program Files\Microsoft Network Monitor 3\
C:\Users\<USER>\AppData\Roaming\npm
C:\Program Files (x86)\nodejs\
C:\Program Files (x86)\Mercurial\
C:\Program Files\Git\cmd
C:\Program Files (x86)\Microsoft ASP.NET\ASP.NET Web Pages\v1.0\
C:\Program Files (x86)\dotnet
C:\Program Files\dotnet
C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps
C:\Program Files\Java\Adoptium-Eclipse-Temurin-OpenJDK-8u442\bin
Request
Url: https://hepzlogic.scm.azurewebsites.net/Env.cshtml
HTTP headers
Accept=text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Accept-Encoding=gzip, deflate, br, zstd
Accept-Language=en-US,en;q=0.9,en-IN;q=0.8
CLIENT-IP=************:58466
Cookie=ARRAffinity=6187c8037ef92a3b567aae0206be9a48137126a2a72e19f901b557b00f92056f; ARRAffinitySameSite=6187c8037ef92a3b567aae0206be9a48137126a2a72e19f901b557b00f92056f
DISGUISED-HOST=hepzlogic.scm.azurewebsites.net
DNT=1
Host=hepzlogic.scm.azurewebsites.net
Max-Forwards=10
Referer=https://hepzlogic.scm.azurewebsites.net/DebugConsole
sec-ch-ua="Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
sec-ch-ua-mobile=?0
sec-ch-ua-platform="Windows"
Sec-Fetch-Dest=document
Sec-Fetch-Mode=navigate
Sec-Fetch-Site=same-origin
Sec-Fetch-User=?1
User-Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
WAS-DEFAULT-HOSTNAME=hepzlogic.scm.azurewebsites.net
X-AppService-Proto=https
X-ARR-LOG-ID=937352ab-4c33-4782-9a4a-c544ab217070
X-ARR-SSL=2048|256|CN=Microsoft Azure RSA TLS Issuing CA 07, O=Microsoft Corporation, C=US|CN=*.azurewebsites.net, O=Microsoft Corporation, L=Redmond, S=WA, C=US
X-Forwarded-For=************:58466
X-Forwarded-Proto=https
X-Forwarded-TlsVersion=1.3
X-MS-CLIENT-PRINCIPAL-NAME=<EMAIL>
X-Original-URL=/Env.cshtml
X-SITE-DEPLOYMENT-ID=hepzlogic
X-WAWS-Unencoded-URL=/Env.cshtml
Server variables
ALL_HTTP=HTTP_ACCEPT:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7 HTTP_ACCEPT_ENCODING:gzip, deflate, br, zstd HTTP_ACCEPT_LANGUAGE:en-US,en;q=0.9,en-IN;q=0.8 HTTP_COOKIE:ARRAffinity=6187c8037ef92a3b567aae0206be9a48137126a2a72e19f901b557b00f92056f; ARRAffinitySameSite=6187c8037ef92a3b567aae0206be9a48137126a2a72e19f901b557b00f92056f HTTP_HOST:hepzlogic.scm.azurewebsites.net HTTP_MAX_FORWARDS:10 HTTP_REFERER:https://hepzlogic.scm.azurewebsites.net/DebugConsole HTTP_USER_AGENT:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/********* HTTP_SEC_CH_UA:"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99" HTTP_SEC_CH_UA_MOBILE:?0 HTTP_SEC_CH_UA_PLATFORM:"Windows" HTTP_DNT:1 HTTP_SEC_FETCH_SITE:same-origin HTTP_SEC_FETCH_MODE:navigate HTTP_SEC_FETCH_USER:?1 HTTP_SEC_FETCH_DEST:document HTTP_X_ARR_LOG_ID:937352ab-4c33-4782-9a4a-c544ab217070 HTTP_CLIENT_IP:************:58466 HTTP_DISGUISED_HOST:hepzlogic.scm.azurewebsites.net HTTP_X_SITE_DEPLOYMENT_ID:hepzlogic HTTP_WAS_DEFAULT_HOSTNAME:hepzlogic.scm.azurewebsites.net HTTP_X_FORWARDED_PROTO:https HTTP_X_APPSERVICE_PROTO:https HTTP_X_ARR_SSL:2048|256|CN=Microsoft Azure RSA TLS Issuing CA 07, O=Microsoft Corporation, C=US|CN=*.azurewebsites.net, O=Microsoft Corporation, L=Redmond, S=WA, C=US HTTP_X_FORWARDED_TLSVERSION:1.3 HTTP_X_MS_CLIENT_PRINCIPAL_NAME:<EMAIL> HTTP_X_FORWARDED_FOR:************:58466 HTTP_X_ORIGINAL_URL:/Env.cshtml HTTP_X_WAWS_UNENCODED_URL:/Env.cshtml
ALL_RAW=Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7 Accept-Encoding: gzip, deflate, br, zstd Accept-Language: en-US,en;q=0.9,en-IN;q=0.8 Cookie: ARRAffinity=6187c8037ef92a3b567aae0206be9a48137126a2a72e19f901b557b00f92056f; ARRAffinitySameSite=6187c8037ef92a3b567aae0206be9a48137126a2a72e19f901b557b00f92056f Host: hepzlogic.scm.azurewebsites.net Max-Forwards: 10 Referer: https://hepzlogic.scm.azurewebsites.net/DebugConsole User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/********* sec-ch-ua: "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99" sec-ch-ua-mobile: ?0 sec-ch-ua-platform: "Windows" DNT: 1 Sec-Fetch-Site: same-origin Sec-Fetch-Mode: navigate Sec-Fetch-User: ?1 Sec-Fetch-Dest: document X-ARR-LOG-ID: 937352ab-4c33-4782-9a4a-c544ab217070 CLIENT-IP: ************:58466 DISGUISED-HOST: hepzlogic.scm.azurewebsites.net X-SITE-DEPLOYMENT-ID: hepzlogic WAS-DEFAULT-HOSTNAME: hepzlogic.scm.azurewebsites.net X-Forwarded-Proto: https X-AppService-Proto: https X-ARR-SSL: 2048|256|CN=Microsoft Azure RSA TLS Issuing CA 07, O=Microsoft Corporation, C=US|CN=*.azurewebsites.net, O=Microsoft Corporation, L=Redmond, S=WA, C=US X-Forwarded-TlsVersion: 1.3 X-MS-CLIENT-PRINCIPAL-NAME: <EMAIL> X-Forwarded-For: ************:58466 X-Original-URL: /Env.cshtml X-WAWS-Unencoded-URL: /Env.cshtml
APPL_MD_PATH=/LM/W3SVC/1047681048/ROOT
APPL_PHYSICAL_PATH=C:\Program Files (x86)\SiteExtensions\Kudu\103.250502.001\
AUTH_PASSWORD=
AUTH_TYPE=
AUTH_USER=
CERT_COOKIE=
CERT_FLAGS=
CERT_ISSUER=
CERT_KEYSIZE=256
CERT_SECRETKEYSIZE=2048
CERT_SERIALNUMBER=
CERT_SERVER_ISSUER=CN=Microsoft Azure RSA TLS Issuing CA 07, O=Microsoft Corporation, C=US
CERT_SERVER_SUBJECT=CN=*.azurewebsites.net, O=Microsoft Corporation, L=Redmond, S=WA, C=US
CERT_SUBJECT=
CONTENT_LENGTH=0
CONTENT_TYPE=
GATEWAY_INTERFACE=CGI/1.1
HTTP_ACCEPT=text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
HTTP_ACCEPT_ENCODING=gzip, deflate, br, zstd
HTTP_ACCEPT_LANGUAGE=en-US,en;q=0.9,en-IN;q=0.8
HTTP_CLIENT_IP=************:58466
HTTP_COOKIE=ARRAffinity=6187c8037ef92a3b567aae0206be9a48137126a2a72e19f901b557b00f92056f; ARRAffinitySameSite=6187c8037ef92a3b567aae0206be9a48137126a2a72e19f901b557b00f92056f
HTTP_DISGUISED_HOST=hepzlogic.scm.azurewebsites.net
HTTP_DNT=1
HTTP_HOST=hepzlogic.scm.azurewebsites.net
HTTP_MAX_FORWARDS=10
HTTP_REFERER=https://hepzlogic.scm.azurewebsites.net/DebugConsole
HTTP_SEC_CH_UA="Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"
HTTP_SEC_CH_UA_MOBILE=?0
HTTP_SEC_CH_UA_PLATFORM="Windows"
HTTP_SEC_FETCH_DEST=document
HTTP_SEC_FETCH_MODE=navigate
HTTP_SEC_FETCH_SITE=same-origin
HTTP_SEC_FETCH_USER=?1
HTTP_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
HTTP_WAS_DEFAULT_HOSTNAME=hepzlogic.scm.azurewebsites.net
HTTP_X_APPSERVICE_PROTO=https
HTTP_X_ARR_LOG_ID=937352ab-4c33-4782-9a4a-c544ab217070
HTTP_X_ARR_SSL=2048|256|CN=Microsoft Azure RSA TLS Issuing CA 07, O=Microsoft Corporation, C=US|CN=*.azurewebsites.net, O=Microsoft Corporation, L=Redmond, S=WA, C=US
HTTP_X_FORWARDED_FOR=************:58466
HTTP_X_FORWARDED_PROTO=https
HTTP_X_FORWARDED_TLSVERSION=1.3
HTTP_X_MS_CLIENT_PRINCIPAL_NAME=<EMAIL>
HTTP_X_ORIGINAL_URL=/Env.cshtml
HTTP_X_SITE_DEPLOYMENT_ID=hepzlogic
HTTP_X_WAWS_UNENCODED_URL=/Env.cshtml
HTTPS=on
HTTPS_KEYSIZE=256
HTTPS_SECRETKEYSIZE=2048
HTTPS_SERVER_ISSUER=CN=Microsoft Azure RSA TLS Issuing CA 07, O=Microsoft Corporation, C=US
HTTPS_SERVER_SUBJECT=CN=*.azurewebsites.net, O=Microsoft Corporation, L=Redmond, S=WA, C=US
INSTANCE_ID=1047681048
INSTANCE_META_PATH=/LM/W3SVC/1047681048
LOCAL_ADDR=**************
LOGON_USER=
PATH_INFO=/Env.cshtml
PATH_TRANSLATED=C:\Program Files (x86)\SiteExtensions\Kudu\103.250502.001\Env.cshtml
QUERY_STRING=
REMOTE_ADDR=************
REMOTE_HOST=************
REMOTE_PORT=58466
REMOTE_USER=
REQUEST_METHOD=GET
SCRIPT_NAME=/Env.cshtml
SERVER_NAME=hepzlogic.scm.azurewebsites.net
SERVER_PORT=443
SERVER_PORT_SECURE=1
SERVER_PROTOCOL=HTTP/1.1
SERVER_SOFTWARE=Microsoft-IIS/10.0
URL=/Env.cshtml