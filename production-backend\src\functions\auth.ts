/**
 * Authentication Functions
 * Handles user login, token validation, and user sync operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import jwt from "jsonwebtoken";
import * as bcrypt from "bcryptjs";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

const syncUserSchema = Joi.object({
  sub: Joi.string().required(),
  oid: Joi.string().required(),
  email: Joi.string().email().required(),
  emails: Joi.array().items(Joi.string().email()).optional(),
  given_name: Joi.string().optional(),
  family_name: Joi.string().optional(),
  name: Joi.string().optional(),
  picture: Joi.string().uri().optional(),
  tfp: Joi.string().optional()
});

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  passwordHash?: string;
  tenantId?: string;
  roles: string[];
  systemRoles: string[];
  organizationIds: string[];
  defaultOrganizationId?: string;
  status: string;
  refreshTokens: any[];
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  preferences?: any;
  avatarUrl?: string;
}

/**
 * User login handler
 */
export async function userLogin(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("User login started", { correlationId });

  try {
    // Validate request body
    const body = await request.json();
    const { error, value } = loginSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { email, password } = value;

    // Query for user by email
    const query = 'SELECT * FROM c WHERE c.email = @email';
    const users = await db.queryItems('users', query, [email.toLowerCase()]);

    if (users.length === 0) {
      logger.warn("Login attempt with invalid email", { email, correlationId });
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Invalid email or password" }
      }, request);
    }

    const user = users[0] as User;

    // Check if account is locked or disabled
    if (user.status === 'locked' || user.status === 'disabled') {
      logger.warn("Login attempt on locked/disabled account", { 
        userId: user.id, 
        email: user.email, 
        status: user.status,
        correlationId 
      });

      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Account is not accessible. Please contact support." }
      }, request);
    }

    // Verify password
    let passwordValid = false;
    if (user.passwordHash) {
      passwordValid = await bcrypt.compare(password, user.passwordHash);
    }

    if (!passwordValid) {
      logger.warn("Login attempt with invalid password", { email, correlationId });

      // Log failed login attempt
      await db.createItem('login-history', {
        id: uuidv4(),
        userId: user.id,
        email: user.email,
        timestamp: new Date().toISOString(),
        ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
        success: false,
        reason: "invalid_password",
        tenantId: user.tenantId
      });

      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Invalid email or password" }
      }, request);
    }

    // Generate tokens
    const refreshToken = uuidv4();
    const refreshTokenExpiry = new Date();
    refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30 days

    const jwtSecret = process.env.AUTH_CLIENT_SECRET;
    if (!jwtSecret) {
      logger.error("AUTH_CLIENT_SECRET environment variable is not set", { correlationId });
      return addCorsHeaders({
        status: 500,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Authentication service misconfigured" }
      }, request);
    }

    const now = Math.floor(Date.now() / 1000);
    const expiresIn = 3600; // 1 hour

    const accessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.displayName || `${user.firstName} ${user.lastName}`,
        tenantId: user.tenantId,
        roles: user.roles || [],
        systemRoles: user.systemRoles || [],
        organizationIds: user.organizationIds || [],
        defaultOrganizationId: user.defaultOrganizationId,
        iat: now,
        exp: now + expiresIn,
        jti: uuidv4(),
        auth_time: now
      },
      jwtSecret,
      {
        expiresIn: `${expiresIn}s`,
        issuer: process.env.AUTH_ISSUER || "hepz-platform",
        audience: process.env.AUTH_CLIENT_ID || "hepz-client",
        algorithm: "HS256"
      }
    );

    // Update user with refresh token and last login
    const updatedUser = {
      ...user,
      refreshTokens: [
        ...(user.refreshTokens || []),
        {
          token: refreshToken,
          expiresAt: refreshTokenExpiry.toISOString(),
          createdAt: new Date().toISOString(),
          userAgent: request.headers.get("user-agent") || "unknown",
          ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown"
        }
      ],
      lastLoginAt: new Date().toISOString()
    };

    await db.updateItem('users', updatedUser);

    // Log successful login
    await db.createItem('login-history', {
      id: uuidv4(),
      userId: user.id,
      email: user.email,
      timestamp: new Date().toISOString(),
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
      userAgent: request.headers.get("user-agent") || "unknown",
      success: true,
      tokenId: accessToken.split('.')[2],
      tenantId: user.tenantId
    });

    logger.info("User authenticated successfully", { 
      userId: user.id, 
      email: user.email, 
      correlationId 
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        accessToken,
        refreshToken,
        expiresIn,
        tokenType: "Bearer",
        user: {
          id: user.id,
          email: user.email,
          displayName: user.displayName || `${user.firstName} ${user.lastName}`,
          firstName: user.firstName,
          lastName: user.lastName,
          tenantId: user.tenantId,
          roles: user.roles || [],
          systemRoles: user.systemRoles || [],
          status: user.status || "active",
          organizationIds: user.organizationIds || [],
          defaultOrganizationId: user.defaultOrganizationId,
          avatarUrl: user.avatarUrl,
          lastLoginAt: new Date().toISOString(),
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
          preferences: user.preferences || {
            theme: "system",
            language: "en",
            timezone: "UTC",
            dateFormat: "MM/DD/YYYY"
          }
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("User login failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get current user info handler
 */
export async function getCurrentUser(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get current user started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get full user profile from database
    const userProfile = await db.readItem('users', user.id, user.id);

    if (!userProfile) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User profile not found" }
      }, request);
    }

    // Remove sensitive fields
    const sanitizedProfile = {
      ...(userProfile as any),
      refreshTokens: undefined,
      passwordHash: undefined,
      _rid: undefined,
      _self: undefined,
      _etag: undefined,
      _attachments: undefined,
      _ts: undefined
    };

    logger.info("Current user retrieved successfully", {
      correlationId,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: sanitizedProfile
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get current user failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('auth-login', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'anonymous',
  route: 'auth/login',
  handler: userLogin
});

app.http('auth-me', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'anonymous',
  route: 'auth/me',
  handler: getCurrentUser
});
