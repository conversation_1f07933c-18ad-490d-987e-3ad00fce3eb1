/**
 * Database service for Cosmos DB operations
 * Provides centralized database access and common operations
 */

import { CosmosClient, Database, Container } from '@azure/cosmos';
import { logger } from '../utils/logger';
import { config } from '../../env';

export class DatabaseService {
  private static instance: DatabaseService;
  private client: CosmosClient;
  private database: Database;
  private containers: Map<string, Container> = new Map();

  private constructor() {
    // Initialize as null - will be lazily initialized when first used
    this.client = null as any;
    this.database = null as any;
  }

  /**
   * Initialize the database connection lazily
   */
  private initializeDatabase(): void {
    if (this.client !== null) {
      return; // Already initialized
    }

    // Check if we have valid Cosmos DB configuration
    if (!config.cosmosDb.endpoint || !config.cosmosDb.key) {
      logger.warn('Cosmos DB configuration missing. Database operations will be mocked in development.');
      return;
    }

    try {
      // Validate URL format before creating client
      const url = new URL(config.cosmosDb.endpoint);
      if (!url.protocol.startsWith('https')) {
        throw new Error('Cosmos DB endpoint must use HTTPS protocol');
      }

      this.client = new CosmosClient({
        endpoint: config.cosmosDb.endpoint,
        key: config.cosmosDb.key
      });
      this.database = this.client.database(config.cosmosDb.database);
      logger.info('Cosmos DB client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Cosmos DB client', {
        endpoint: config.cosmosDb.endpoint,
        error: error instanceof Error ? error.message : String(error)
      });
      this.client = null as any;
      this.database = null as any;
    }
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Check if database is available
   */
  private isDatabaseAvailable(): boolean {
    this.initializeDatabase();
    return this.client !== null && this.database !== null;
  }

  /**
   * Get container instance
   */
  public getContainer(containerName: string): Container {
    if (!this.isDatabaseAvailable()) {
      throw new Error('Database not available. Please configure Cosmos DB connection.');
    }

    if (!this.containers.has(containerName)) {
      const container = this.database.container(containerName);
      this.containers.set(containerName, container);
    }
    return this.containers.get(containerName)!;
  }

  /**
   * Create item in container
   */
  public async createItem<T extends Record<string, any>>(containerName: string, item: T): Promise<T> {
    if (!this.isDatabaseAvailable()) {
      logger.warn(`Mock: Creating item in ${containerName}`, { itemId: (item as any)?.id });
      return item; // Return the item as-is in mock mode
    }

    try {
      const container = this.getContainer(containerName);
      const { resource } = await container.items.create(item as any);
      logger.info(`Created item in ${containerName}`, { itemId: (resource as any)?.id });
      return resource as T;
    } catch (error) {
      logger.error(`Failed to create item in ${containerName}`, { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * Read item by id and partition key
   */
  public async readItem<T extends Record<string, any>>(containerName: string, id: string, partitionKey: string): Promise<T | null> {
    if (!this.isDatabaseAvailable()) {
      logger.warn(`Mock: Reading item from ${containerName}`, { id, partitionKey });
      return null; // Return null in mock mode
    }

    try {
      const container = this.getContainer(containerName);
      const { resource } = await container.item(id, partitionKey).read();
      return (resource as T) || null;
    } catch (error: any) {
      if (error.code === 404) {
        return null;
      }
      logger.error(`Failed to read item from ${containerName}`, {
        id,
        partitionKey,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Update item
   */
  public async updateItem<T>(containerName: string, item: T & { id: string }): Promise<T> {
    try {
      const container = this.getContainer(containerName);
      const { resource } = await container.item(item.id).replace(item);
      logger.info(`Updated item in ${containerName}`, { itemId: item.id });
      return resource as T;
    } catch (error) {
      logger.error(`Failed to update item in ${containerName}`, {
        itemId: (item as any).id,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }



  /**
   * Delete item
   */
  public async deleteItem(containerName: string, id: string, partitionKey: string): Promise<void> {
    try {
      const container = this.getContainer(containerName);
      await container.item(id, partitionKey).delete();
      logger.info(`Deleted item from ${containerName}`, { id, partitionKey });
    } catch (error) {
      logger.error(`Failed to delete item from ${containerName}`, {
        id,
        partitionKey,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Query items with SQL
   */
  public async queryItems<T>(
    containerName: string,
    query: string,
    parameters?: any[]
  ): Promise<T[]> {
    try {
      const container = this.getContainer(containerName);

      // Build query spec with proper parameter mapping
      const querySpec = {
        query,
        parameters: parameters?.map((value, index) => ({
          name: `@param${index}`,
          value
        })) || []
      };

      // Replace parameter placeholders in query to match the parameter names
      let finalQuery = query;
      if (parameters && parameters.length > 0) {
        const paramNames = ['@documentId', '@sharedWith', '@status', '@tenantId', '@userId', '@pageNumber', '@resolved', '@organizationId', '@projectId', '@orgIds', '@projectIds', '@query', '@userId2', '@search', '@visibility', '@tags', '@assigneeId', '@limit'];
        parameters.forEach((param, index) => {
          if (index < paramNames.length) {
            finalQuery = finalQuery.replace(new RegExp(paramNames[index], 'g'), `@param${index}`);
          }
        });
      }

      querySpec.query = finalQuery;

      const { resources } = await container.items.query<T>(querySpec).fetchAll();
      logger.info(`Queried items from ${containerName}`, {
        query: query.substring(0, 100),
        resultCount: resources.length
      });
      return resources;
    } catch (error) {
      logger.error(`Failed to query items from ${containerName}`, {
        query: query.substring(0, 100),
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Query items with pagination
   */
  public async queryItemsPaginated<T>(
    containerName: string,
    query: string,
    parameters?: any[],
    maxItemCount: number = 20,
    continuationToken?: string
  ): Promise<{ items: T[]; continuationToken?: string }> {
    try {
      const container = this.getContainer(containerName);
      const querySpec = {
        query,
        parameters: parameters?.map((value, index) => ({
          name: `@param${index}`,
          value
        })) || []
      };

      const queryIterator = container.items.query<T>(querySpec, {
        maxItemCount,
        continuationToken
      });

      const { resources, continuationToken: nextToken } = await queryIterator.fetchNext();

      return {
        items: resources,
        continuationToken: nextToken
      };
    } catch (error) {
      logger.error(`Failed to query items with pagination from ${containerName}`, {
        query,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Check if container exists
   */
  public async containerExists(containerName: string): Promise<boolean> {
    try {
      await this.database.container(containerName).read();
      return true;
    } catch (error: any) {
      if (error.code === 404) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Upsert an item in a container (create or update)
   */
  public async upsertItem(containerName: string, item: any): Promise<any> {
    try {
      const container = this.getContainer(containerName);
      const { resource } = await container.items.upsert(item);

      logger.debug('Database item upserted', {
        container: containerName,
        id: item.id
      });

      return resource;
    } catch (error) {
      logger.error('Failed to upsert database item', {
        container: containerName,
        id: item.id,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Create container if it doesn't exist
   */
  public async createContainerIfNotExists(
    containerName: string,
    partitionKey: string
  ): Promise<Container> {
    try {
      const { container } = await this.database.containers.createIfNotExists({
        id: containerName,
        partitionKey: { paths: [partitionKey] }
      });

      this.containers.set(containerName, container);
      logger.info(`Container ${containerName} created or already exists`);
      return container;
    } catch (error) {
      logger.error(`Failed to create container ${containerName}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }
}

// Export singleton instance
export const db = DatabaseService.getInstance();
export default db;
