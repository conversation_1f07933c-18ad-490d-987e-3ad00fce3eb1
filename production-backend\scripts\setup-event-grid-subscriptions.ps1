# Simple Event Grid Subscriptions Setup
# Creates essential Event Grid subscriptions for the application

Write-Host "🚀 Setting up Event Grid Subscriptions..." -ForegroundColor Green

$ResourceGroup = "docucontext"
$FunctionAppName = "hepzlogic"
$EventGridTopicName = "hepzeg"
$StorageAccountName = "stdocucontex900520441468"

# Get subscription ID
$subscriptionId = az account show --query id --output tsv
Write-Host "Subscription ID: $subscriptionId" -ForegroundColor Yellow

# Get Function App URL
Write-Host "📋 Getting Function App URL..." -ForegroundColor Blue
$functionAppUrl = az functionapp show --name $FunctionAppName --resource-group $ResourceGroup --query defaultHostName --output tsv

if (-not $functionAppUrl) {
    Write-Error "Function App '$FunctionAppName' not found"
    exit 1
}

Write-Host "Function App URL: https://$functionAppUrl" -ForegroundColor Green

# 1. Create System Topic for Storage Account (if not exists)
Write-Host "📦 Creating Storage System Topic..." -ForegroundColor Blue
$systemTopicName = "$StorageAccountName-events"
$storageAccountId = "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.Storage/storageAccounts/$StorageAccountName"

$existingSystemTopic = az eventgrid system-topic show --name $systemTopicName --resource-group $ResourceGroup 2>$null
if (-not $existingSystemTopic) {
    az eventgrid system-topic create `
        --name $systemTopicName `
        --resource-group $ResourceGroup `
        --source $storageAccountId `
        --topic-type "Microsoft.Storage.StorageAccounts" `
        --location "eastus"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Storage System Topic created: $systemTopicName" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create Storage System Topic" -ForegroundColor Red
    }
} else {
    Write-Host "✅ Storage System Topic already exists: $systemTopicName" -ForegroundColor Green
}

# 2. Create Event Subscription for Blob Created Events
Write-Host "📬 Creating Blob Created Events Subscription..." -ForegroundColor Blue
$blobCreatedSubscription = "blob-created-events"

$existingSubscription = az eventgrid event-subscription show --name $blobCreatedSubscription --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$systemTopicName" 2>$null
if (-not $existingSubscription) {
    az eventgrid event-subscription create `
        --name $blobCreatedSubscription `
        --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$systemTopicName" `
        --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
        --endpoint-type webhook `
        --included-event-types "Microsoft.Storage.BlobCreated" `
        --subject-begins-with "/blobServices/default/containers/documents/" `
        --max-delivery-attempts 3 `
        --event-ttl 1440

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Blob Created subscription created: $blobCreatedSubscription" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create Blob Created subscription" -ForegroundColor Red
    }
} else {
    Write-Host "✅ Blob Created subscription already exists: $blobCreatedSubscription" -ForegroundColor Green
}

# 3. Create Event Subscription for Custom Application Events
Write-Host "🎯 Creating Custom Events Subscription..." -ForegroundColor Blue
$customEventsSubscription = "custom-app-events"

$existingCustomSubscription = az eventgrid event-subscription show --name $customEventsSubscription --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" 2>$null
if (-not $existingCustomSubscription) {
    az eventgrid event-subscription create `
        --name $customEventsSubscription `
        --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
        --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
        --endpoint-type webhook `
        --max-delivery-attempts 3 `
        --event-ttl 1440

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Custom Events subscription created: $customEventsSubscription" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create Custom Events subscription" -ForegroundColor Red
    }
} else {
    Write-Host "✅ Custom Events subscription already exists: $customEventsSubscription" -ForegroundColor Green
}

# 4. Verify Event Grid Topic Configuration
Write-Host "🔍 Verifying Event Grid Topic..." -ForegroundColor Blue
$topicEndpoint = az eventgrid topic show --name $EventGridTopicName --resource-group $ResourceGroup --query "endpoint" --output tsv 2>$null
if ($topicEndpoint) {
    Write-Host "✅ Event Grid Topic verified: $topicEndpoint" -ForegroundColor Green
} else {
    Write-Host "❌ Event Grid Topic not found or inaccessible" -ForegroundColor Red
}

# 5. List all Event Subscriptions
Write-Host "📋 Listing Event Subscriptions..." -ForegroundColor Blue
Write-Host "Storage Event Subscriptions:" -ForegroundColor Yellow
az eventgrid event-subscription list --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$systemTopicName" --query "[].{Name:name, Endpoint:destination.endpointUrl}" --output table 2>$null

Write-Host "Custom Topic Event Subscriptions:" -ForegroundColor Yellow
az eventgrid event-subscription list --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" --query "[].{Name:name, Endpoint:destination.endpointUrl}" --output table 2>$null

Write-Host "✅ Event Grid setup completed!" -ForegroundColor Green
Write-Host "📝 Summary:" -ForegroundColor Yellow
Write-Host "   - Storage System Topic: $systemTopicName" -ForegroundColor White
Write-Host "   - Blob Events Subscription: $blobCreatedSubscription" -ForegroundColor White
Write-Host "   - Custom Events Subscription: $customEventsSubscription" -ForegroundColor White
Write-Host "   - Function App Webhook: https://$functionAppUrl/api/eventgrid/webhook" -ForegroundColor White

Write-Host "🔄 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Deploy your Azure Functions" -ForegroundColor White
Write-Host "   2. Test Event Grid integration with: node scripts/test-event-grid.js" -ForegroundColor White
Write-Host "   3. Monitor events in Azure Portal" -ForegroundColor White
