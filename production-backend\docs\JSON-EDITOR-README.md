# JSON Column Editor

A powerful and intuitive JavaScript tool for removing columns from JSON files. Works with any JSON structure including nested objects and arrays.

## Features

- 🔍 **Smart Analysis**: Automatically detects all columns in your JSON structure
- 🌳 **Nested Support**: Handles deeply nested objects and arrays
- 🎯 **Flexible Selection**: Multiple ways to select columns for deletion
- ✅ **Safe Operations**: Validates inputs and confirms destructive actions
- 📊 **Detailed Logging**: Shows progress and provides helpful feedback
- 💾 **File Management**: Proper source/destination file handling

## Usage

### Basic Usage
```bash
# Interactive mode - prompts for all inputs
node json-column-editor.js

# With source file specified
node json-column-editor.js your-file.json

# With both source and destination specified
node json-column-editor.js input.json output.json

# Using command line flags
node json-column-editor.js --source input.json --dest output.json

# Show help
node json-column-editor.js --help
```

### Demo Mode
```bash
# Run the demo to see how the tool analyzes JSON structure
node demo-usage.js
```

### Make it executable (optional)
```bash
chmod +x json-column-editor.js
./json-column-editor.js
```

## How It Works

1. **Select Source File**: Choose your JSON file to edit
2. **Choose Destination**: Specify where to save the edited file
3. **Analyze Structure**: The tool scans and displays all available columns
4. **Select Columns**: Choose which columns to delete using various methods
5. **Confirm & Process**: Review your selection and confirm the changes
6. **Save Results**: The modified JSON is saved to your destination file

## Column Selection Methods

### Single Column
```
5
```

### Multiple Columns
```
1,3,5,7
```

### Range of Columns
```
1-5
```

### Mixed Selection
```
1,3,7-10,15
```

### Special Commands
- `none` - Skip deletion
- `all` - Delete all columns (with confirmation)
- `show` - Display columns list again
- `exit` - Quit the application

## Example Session

```
🚀 JSON COLUMN EDITOR
═══════════════════════════════════════════════════════
Welcome! This tool helps you remove columns from JSON files.

📁 SOURCE FILE SELECTION
──────────────────────────────────────────────────────
Enter the path to your JSON file: ./data.json
✅ Source file selected: /path/to/data.json

💾 DESTINATION FILE SELECTION
──────────────────────────────────────────────────────
Enter destination file path (default: ./data_edited.json): 
✅ Destination file set: /path/to/data_edited.json

📖 LOADING JSON FILE
──────────────────────────────────────────────────────
📊 File size: 45.67 KB
✅ JSON file loaded and parsed successfully

🔍 JSON STRUCTURE ANALYSIS
──────────────────────────────────────────────────────
📋 Type: Array with 150 items
🔑 Total unique columns found: 12
🌳 Nested objects found: 3

📋 AVAILABLE COLUMNS
──────────────────────────────────────────────────────
  1: config
  2: config.bindings 🌳
  3: config.entryPoint
  4: config.functionDirectory
  5: config.name
  6: config.scriptFile
  7: href
  8: id
  9: invokeUrlTemplate
  10: name
  11: resourceGroup
  12: scriptHref
  13: testDataHref
  14: type

🌳 = Nested object/array
Total: 14 columns

🗑️  COLUMN DELETION SELECTION
──────────────────────────────────────────────────────
Enter your selection: 7,9,12-14

📋 Selected columns for deletion:
  1. href
  2. invokeUrlTemplate
  3. scriptHref
  4. testDataHref
  5. type

Confirm deletion of 5 columns? (y/n): y
✅ 5 columns selected for deletion

🔄 PROCESSING DELETIONS
──────────────────────────────────────────────────────
✅ Deleted: href
✅ Deleted: invokeUrlTemplate
✅ Deleted: scriptHref
✅ Deleted: testDataHref
✅ Deleted: type

📊 Summary: 5/5 columns deleted successfully

💾 SAVING MODIFIED JSON
──────────────────────────────────────────────────────
✅ File saved successfully!
📁 Location: /path/to/data_edited.json
📊 Size: 28.45 KB
📉 Size reduced by 17654 bytes (38.7%)

🎉 OPERATION COMPLETED
──────────────────────────────────────────────────────
📥 Source: /path/to/data.json
📤 Destination: /path/to/data_edited.json
✅ JSON column editing completed successfully!
```

## Supported JSON Structures

### Simple Objects
```json
{
  "name": "John",
  "age": 30,
  "city": "New York"
}
```

### Arrays of Objects
```json
[
  {"id": 1, "name": "Item 1"},
  {"id": 2, "name": "Item 2"}
]
```

### Nested Structures
```json
{
  "user": {
    "profile": {
      "name": "John",
      "settings": {
        "theme": "dark"
      }
    }
  }
}
```

## Error Handling

The tool includes comprehensive error handling for:
- Invalid file paths
- Malformed JSON
- Permission issues
- Invalid column selections
- File system errors

## Tips

- Always backup important files before editing
- Use version control to track changes
- You can run the script multiple times on the same file
- The tool preserves the original JSON formatting and structure
- Nested columns are shown with dot notation (e.g., `config.bindings`)

## Requirements

- Node.js (any recent version)
- Read/write access to source and destination directories

## License

This tool is provided as-is for educational and utility purposes.
