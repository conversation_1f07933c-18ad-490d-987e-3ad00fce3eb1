/**
 * Send properly formatted test messages to Service Bus
 * This will test the handlers with valid message formats
 */

const { ServiceBusClient } = require('@azure/service-bus');
const fs = require('fs');
const path = require('path');

// Load configuration from local.settings.json
let localSettings = {};
try {
  const settingsPath = path.join(__dirname, '..', 'local.settings.json');
  const settingsContent = fs.readFileSync(settingsPath, 'utf8');
  localSettings = JSON.parse(settingsContent);
  console.log('✅ Loaded configuration from local.settings.json');
} catch (error) {
  console.error('❌ Failed to load local.settings.json:', error.message);
  process.exit(1);
}

const connectionString = localSettings.Values?.SERVICE_BUS_CONNECTION_STRING;

if (!connectionString) {
  console.error('❌ Service Bus connection string not found');
  process.exit(1);
}

async function sendProperTestMessages() {
  console.log('🧪 Sending properly formatted test messages...\n');
  
  const client = new ServiceBusClient(connectionString);
  
  try {
    // 1. Analytics Events Test Message
    console.log('📊 Sending analytics test message...');
    const analyticsSender = client.createSender('analytics-events');
    await analyticsSender.sendMessages({
      body: JSON.stringify({
        eventType: 'document_view',
        entityId: 'test-doc-123',
        entityType: 'document',
        metrics: {
          viewCount: 1,
          duration: 30000
        },
        timestamp: new Date().toISOString(),
        userId: 'test-user-123'
      }),
      messageId: `analytics-test-${Date.now()}`
    });
    await analyticsSender.close();
    console.log('✅ Analytics test message sent');

    // 2. Workflow Orchestration Test Message
    console.log('⚙️ Sending workflow test message...');
    const workflowSender = client.createSender('workflow-orchestration');
    await workflowSender.sendMessages({
      body: JSON.stringify({
        workflowId: 'test-workflow-123',
        stepId: 'step-1',
        action: 'start',
        data: {
          documentId: 'test-doc-123',
          processingType: 'ocr'
        },
        userId: 'test-user-123',
        priority: 'normal'
      }),
      messageId: `workflow-test-${Date.now()}`
    });
    await workflowSender.close();
    console.log('✅ Workflow test message sent');

    // 3. Monitoring Events Test Message
    console.log('📈 Sending monitoring test message...');
    const monitoringSender = client.createSender('monitoring-events');
    await monitoringSender.sendMessages({
      body: JSON.stringify({
        source: 'test-system',
        metricType: 'cpu_usage',
        value: 75.5,
        threshold: {
          warning: 80,
          critical: 95
        },
        severity: 'info',
        timestamp: new Date().toISOString()
      }),
      messageId: `monitoring-test-${Date.now()}`
    });
    await monitoringSender.close();
    console.log('✅ Monitoring test message sent');

    // 4. Document Collaboration Test Message
    console.log('👥 Sending collaboration test message...');
    const collaborationSender = client.createSender('document-collaboration');
    await collaborationSender.sendMessages({
      body: JSON.stringify({
        documentId: 'test-doc-123',
        userId: 'test-user-123',
        action: 'join',
        data: {
          sessionId: 'session-123',
          userRole: 'editor'
        },
        timestamp: new Date().toISOString()
      }),
      messageId: `collaboration-test-${Date.now()}`
    });
    await collaborationSender.close();
    console.log('✅ Collaboration test message sent');

    console.log('\n🎉 All properly formatted test messages sent!');
    console.log('📝 Check your Azure Functions logs to see successful processing');
    
  } catch (error) {
    console.error('❌ Error sending test messages:', error.message);
  } finally {
    await client.close();
  }
}

// Run if this script is executed directly
if (require.main === module) {
  sendProperTestMessages().catch(console.error);
}

module.exports = { sendProperTestMessages };
