/**
 * Notification Tracking Function
 * Handles notification engagement tracking, analytics, and delivery monitoring
 * Migrated from old-arch/src/notification-service/analytics/track/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Notification tracking types and enums
enum NotificationEvent {
  DELIVERED = 'delivered',
  VIEWED = 'viewed',
  CLICKED = 'clicked',
  DISMISSED = 'dismissed',
  OPENED = 'opened',
  FAILED = 'failed'
}

enum NotificationChannel {
  IN_APP = 'in_app',
  EMAIL = 'email',
  PUSH = 'push',
  SMS = 'sms'
}

// Validation schemas
const trackNotificationSchema = Joi.object({
  notificationId: Joi.string().uuid().required(),
  event: Joi.string().valid(...Object.values(NotificationEvent)).required(),
  channel: Joi.string().valid(...Object.values(NotificationChannel)).optional(),
  action: Joi.string().max(100).optional(),
  metadata: Joi.object().optional(),
  timestamp: Joi.string().isoDate().optional(),
  userAgent: Joi.string().optional(),
  ipAddress: Joi.string().ip().optional(),
  deviceInfo: Joi.object({
    platform: Joi.string().optional(),
    browser: Joi.string().optional(),
    version: Joi.string().optional()
  }).optional()
});

const getNotificationAnalyticsSchema = Joi.object({
  notificationId: Joi.string().uuid().optional(),
  userId: Joi.string().uuid().optional(),
  organizationId: Joi.string().uuid().optional(),
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
  event: Joi.string().valid(...Object.values(NotificationEvent)).optional(),
  channel: Joi.string().valid(...Object.values(NotificationChannel)).optional(),
  groupBy: Joi.string().valid('event', 'channel', 'type', 'day', 'hour').optional(),
  limit: Joi.number().min(1).max(1000).default(100)
});

interface TrackNotificationRequest {
  notificationId: string;
  event: NotificationEvent;
  channel?: NotificationChannel;
  action?: string;
  metadata?: any;
  timestamp?: string;
  userAgent?: string;
  ipAddress?: string;
  deviceInfo?: {
    platform?: string;
    browser?: string;
    version?: string;
  };
}

interface NotificationAnalyticsRequest {
  notificationId?: string;
  userId?: string;
  organizationId?: string;
  startDate?: string;
  endDate?: string;
  event?: NotificationEvent;
  channel?: NotificationChannel;
  groupBy?: string;
  limit: number;
}

/**
 * Track notification engagement handler
 */
export async function trackNotificationEngagement(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Track notification engagement started", { correlationId });

  try {
    // For tracking, we allow both authenticated and anonymous requests
    // Some tracking events (like email opens) may not have user context
    let user = null;
    const authResult = await authenticateRequest(request);
    if (authResult.success) {
      user = authResult.user;
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = trackNotificationSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const trackingRequest: TrackNotificationRequest = value;

    // Get notification details
    const notification = await db.readItem('notifications', trackingRequest.notificationId, trackingRequest.notificationId);
    if (!notification) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Notification not found" }
      }, request);
    }

    const notificationData = notification as any;
    const now = new Date().toISOString();

    // Extract additional context from request
    const clientIP = trackingRequest.ipAddress || 
                    request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    
    const userAgent = trackingRequest.userAgent || 
                     request.headers.get('user-agent') || 
                     'unknown';

    // Create tracking record
    const trackingId = uuidv4();
    const trackingRecord = {
      id: trackingId,
      notificationId: trackingRequest.notificationId,
      userId: user?.id || notificationData.userId,
      organizationId: notificationData.organizationId,
      event: trackingRequest.event,
      channel: trackingRequest.channel || NotificationChannel.IN_APP,
      action: trackingRequest.action,
      timestamp: trackingRequest.timestamp || now,
      metadata: {
        ...trackingRequest.metadata,
        notificationType: notificationData.type,
        notificationTitle: notificationData.title,
        clientIP,
        userAgent,
        deviceInfo: trackingRequest.deviceInfo
      },
      createdAt: now,
      tenantId: notificationData.userId // Use notification userId for partitioning
    };

    await db.createItem('notification-tracking', trackingRecord);

    // Update notification status based on event
    if (trackingRequest.event === NotificationEvent.VIEWED && notificationData.status === 'unread') {
      const updatedNotification = {
        ...notificationData,
        status: 'read',
        readAt: now,
        updatedAt: now
      };
      await db.updateItem('notifications', updatedNotification);
    }

    // Update notification engagement metrics
    await updateNotificationMetrics(notificationData, trackingRequest.event, trackingRequest.channel);

    // Publish domain event
    await eventService.publishEvent({
      type: 'NotificationEngagementTracked',
      aggregateId: trackingId,
      aggregateType: 'NotificationTracking',
      version: 1,
      data: {
        tracking: trackingRecord,
        notification: notificationData,
        trackedBy: user?.id
      },
      userId: user?.id || notificationData.userId,
      organizationId: notificationData.organizationId,
      tenantId: notificationData.userId
    });

    logger.info("Notification engagement tracked successfully", {
      correlationId,
      trackingId,
      notificationId: trackingRequest.notificationId,
      event: trackingRequest.event,
      channel: trackingRequest.channel,
      userId: user?.id || notificationData.userId
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: trackingId,
        notificationId: trackingRequest.notificationId,
        event: trackingRequest.event,
        channel: trackingRequest.channel,
        timestamp: trackingRecord.timestamp,
        message: "Notification engagement tracked successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Track notification engagement failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get notification analytics handler
 */
export async function getNotificationAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get notification analytics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      notificationId: url.searchParams.get('notificationId') || undefined,
      userId: url.searchParams.get('userId') || undefined,
      organizationId: url.searchParams.get('organizationId') || undefined,
      startDate: url.searchParams.get('startDate') || undefined,
      endDate: url.searchParams.get('endDate') || undefined,
      event: url.searchParams.get('event') || undefined,
      channel: url.searchParams.get('channel') || undefined,
      groupBy: url.searchParams.get('groupBy') || undefined,
      limit: parseInt(url.searchParams.get('limit') || '100')
    };

    // Validate request
    const { error, value } = getNotificationAnalyticsSchema.validate(queryParams);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const analyticsRequest: NotificationAnalyticsRequest = value;

    // Check permissions - users can only view their own analytics unless they're admin
    if (analyticsRequest.userId && analyticsRequest.userId !== user.id && !user.roles?.includes('admin')) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to notification analytics" }
      }, request);
    }

    // Build query based on filters
    const analytics = await getNotificationAnalyticsData(analyticsRequest, user);

    logger.info("Notification analytics retrieved successfully", {
      correlationId,
      userId: user.id,
      filters: analyticsRequest,
      resultCount: analytics.summary.totalEvents
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: analytics
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get notification analytics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update notification metrics
 */
async function updateNotificationMetrics(notification: any, event: NotificationEvent, channel?: NotificationChannel): Promise<void> {
  try {
    // Get or create metrics record for this notification
    const metricsId = `metrics_${notification.id}`;
    let metrics = await db.readItem('notification-metrics', metricsId, metricsId);
    
    if (!metrics) {
      metrics = {
        id: metricsId,
        notificationId: notification.id,
        userId: notification.userId,
        organizationId: notification.organizationId,
        type: notification.type,
        sentAt: notification.createdAt,
        metrics: {
          delivered: 0,
          viewed: 0,
          clicked: 0,
          dismissed: 0,
          opened: 0,
          failed: 0
        },
        channels: {
          in_app: { delivered: 0, viewed: 0, clicked: 0, dismissed: 0 },
          email: { delivered: 0, viewed: 0, clicked: 0, dismissed: 0 },
          push: { delivered: 0, viewed: 0, clicked: 0, dismissed: 0 },
          sms: { delivered: 0, viewed: 0, clicked: 0, dismissed: 0 }
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tenantId: notification.userId
      };
    }

    const metricsData = metrics as any;

    // Update overall metrics
    if (metricsData.metrics[event] !== undefined) {
      metricsData.metrics[event]++;
    }

    // Update channel-specific metrics
    if (channel && metricsData.channels[channel] && metricsData.channels[channel][event] !== undefined) {
      metricsData.channels[channel][event]++;
    }

    metricsData.updatedAt = new Date().toISOString();

    await db.upsertItem('notification-metrics', metricsData);

  } catch (error) {
    logger.error('Failed to update notification metrics', { 
      error, 
      notificationId: notification.id, 
      event, 
      channel 
    });
  }
}

/**
 * Get notification analytics data
 */
async function getNotificationAnalyticsData(request: NotificationAnalyticsRequest, user: any): Promise<any> {
  try {
    // Build base query
    let query = 'SELECT * FROM c WHERE 1=1';
    const parameters: any[] = [];

    // Add filters
    if (request.notificationId) {
      query += ' AND c.notificationId = @notificationId';
      parameters.push(request.notificationId);
    }

    if (request.userId) {
      query += ' AND c.userId = @userId';
      parameters.push(request.userId);
    } else if (!user.roles?.includes('admin')) {
      // Non-admin users can only see their own data
      query += ' AND c.userId = @currentUserId';
      parameters.push(user.id);
    }

    if (request.organizationId) {
      query += ' AND c.organizationId = @organizationId';
      parameters.push(request.organizationId);
    }

    if (request.startDate) {
      query += ' AND c.timestamp >= @startDate';
      parameters.push(request.startDate);
    }

    if (request.endDate) {
      query += ' AND c.timestamp <= @endDate';
      parameters.push(request.endDate);
    }

    if (request.event) {
      query += ' AND c.event = @event';
      parameters.push(request.event);
    }

    if (request.channel) {
      query += ' AND c.channel = @channel';
      parameters.push(request.channel);
    }

    query += ` ORDER BY c.timestamp DESC OFFSET 0 LIMIT ${request.limit}`;

    // Get tracking data
    const trackingData = await db.queryItems('notification-tracking', query, parameters);

    // Calculate summary statistics
    const summary = calculateAnalyticsSummary(trackingData);

    // Group data if requested
    const groupedData = request.groupBy ? groupAnalyticsData(trackingData, request.groupBy) : undefined;

    return {
      summary,
      groupedData,
      events: trackingData.slice(0, 50), // Return recent events
      filters: request
    };

  } catch (error) {
    logger.error('Failed to get notification analytics data', { error, request });
    throw error;
  }
}

/**
 * Calculate analytics summary
 */
function calculateAnalyticsSummary(trackingData: any[]): any {
  const summary = {
    totalEvents: trackingData.length,
    uniqueNotifications: new Set(trackingData.map(t => t.notificationId)).size,
    uniqueUsers: new Set(trackingData.map(t => t.userId)).size,
    eventCounts: {} as any,
    channelCounts: {} as any,
    engagementRate: 0,
    clickThroughRate: 0
  };

  // Count events by type
  trackingData.forEach(event => {
    summary.eventCounts[event.event] = (summary.eventCounts[event.event] || 0) + 1;
    summary.channelCounts[event.channel] = (summary.channelCounts[event.channel] || 0) + 1;
  });

  // Calculate engagement rates
  const deliveredCount = summary.eventCounts[NotificationEvent.DELIVERED] || 0;
  const viewedCount = summary.eventCounts[NotificationEvent.VIEWED] || 0;
  const clickedCount = summary.eventCounts[NotificationEvent.CLICKED] || 0;

  if (deliveredCount > 0) {
    summary.engagementRate = Math.round((viewedCount / deliveredCount) * 100);
    summary.clickThroughRate = Math.round((clickedCount / deliveredCount) * 100);
  }

  return summary;
}

/**
 * Group analytics data
 */
function groupAnalyticsData(trackingData: any[], groupBy: string): any {
  const grouped: { [key: string]: any } = {};

  trackingData.forEach(event => {
    let key: string;

    switch (groupBy) {
      case 'event':
        key = event.event;
        break;
      case 'channel':
        key = event.channel;
        break;
      case 'type':
        key = event.metadata?.notificationType || 'unknown';
        break;
      case 'day':
        key = new Date(event.timestamp).toISOString().split('T')[0];
        break;
      case 'hour':
        key = new Date(event.timestamp).toISOString().substring(0, 13);
        break;
      default:
        key = 'all';
    }

    if (!grouped[key]) {
      grouped[key] = {
        key,
        count: 0,
        events: {} as any
      };
    }

    grouped[key].count++;
    grouped[key].events[event.event] = (grouped[key].events[event.event] || 0) + 1;
  });

  return Object.values(grouped).sort((a: any, b: any) => b.count - a.count);
}

// Register functions
app.http('notification-tracking-track', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'anonymous', // Allow anonymous tracking for email opens, etc.
  route: 'notifications/track',
  handler: trackNotificationEngagement
});

app.http('notification-analytics', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/analytics',
  handler: getNotificationAnalytics
});
