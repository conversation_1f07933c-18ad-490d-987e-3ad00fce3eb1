/**
 * User Permissions Function
 * Handles user permission retrieval and management
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Permission enums
export enum SystemRole {
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  ORGANIZATION_ADMIN = 'ORGANIZATION_ADMIN',
  ORGANIZATION_MEMBER = 'OR<PERSON><PERSON>ZAT<PERSON>_MEMBER',
  ORGANIZATION_VIEWER = 'ORGANIZATION_VIEWER',
  PROJECT_ADMIN = 'PROJECT_ADMIN',
  PROJECT_MEMBER = 'PROJECT_MEMBER',
  PROJECT_VIEWER = 'PROJECT_VIEWER',
  GUEST = 'GUEST'
}

export enum PermissionAction {
  // Organization permissions
  VIEW_ORGANIZATION = 'VIEW_ORGANIZATION',
  UPDATE_ORGANIZATION = 'UPDATE_ORGANIZATION',
  MANAGE_ORGANIZATION = 'MANAGE_ORGANIZATION',
  VIEW_ORGANIZATION_MEMBERS = 'VIEW_ORGANIZATION_MEMBERS',
  INVITE_ORGANIZATION_MEMBER = 'INVITE_ORGANIZATION_MEMBER',
  REMOVE_ORGANIZATION_MEMBER = 'REMOVE_ORGANIZATION_MEMBER',
  MANAGE_ORGANIZATION_MEMBERS = 'MANAGE_ORGANIZATION_MEMBERS',

  // Project permissions
  CREATE_PROJECT = 'CREATE_PROJECT',
  VIEW_PROJECT = 'VIEW_PROJECT',
  UPDATE_PROJECT = 'UPDATE_PROJECT',
  DELETE_PROJECT = 'DELETE_PROJECT',
  MANAGE_PROJECT = 'MANAGE_PROJECT',
  ADD_PROJECT_MEMBER = 'ADD_PROJECT_MEMBER',
  REMOVE_PROJECT_MEMBER = 'REMOVE_PROJECT_MEMBER',
  MANAGE_PROJECT_MEMBERS = 'MANAGE_PROJECT_MEMBERS',

  // Document permissions
  UPLOAD_DOCUMENT = 'UPLOAD_DOCUMENT',
  VIEW_DOCUMENT = 'VIEW_DOCUMENT',
  UPDATE_DOCUMENT = 'UPDATE_DOCUMENT',
  DELETE_DOCUMENT = 'DELETE_DOCUMENT',
  SHARE_DOCUMENT = 'SHARE_DOCUMENT',
  COMMENT_DOCUMENT = 'COMMENT_DOCUMENT',
  EDIT_ANY_COMMENT = 'EDIT_ANY_COMMENT',
  DELETE_ANY_COMMENT = 'DELETE_ANY_COMMENT',

  // Analytics permissions
  VIEW_ANALYTICS = 'VIEW_ANALYTICS',

  // User permissions
  VIEW_USER_PERMISSIONS = 'VIEW_USER_PERMISSIONS',
  GRANT_PERMISSION = 'GRANT_PERMISSION'
}

// Validation schemas
const getPermissionsSchema = Joi.object({
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional()
});

/**
 * Get user permissions handler
 */
export async function getUserPermissions(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const userId = request.params.userId;

  logger.info("Get user permissions started", { correlationId, userId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;
    const targetUserId = userId || user.id;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getPermissionsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { organizationId, projectId } = value;

    // Users can only view their own permissions unless they're admins
    const isViewingSelf = targetUserId === user.id;
    const isSystemAdmin = user.roles?.includes('admin') || (user as any).systemRoles?.includes(SystemRole.SYSTEM_ADMIN);
    const isOrgAdmin = (user as any).systemRoles?.includes(SystemRole.ORGANIZATION_ADMIN) &&
                      organizationId &&
                      (user as any).organizationIds?.includes(organizationId);

    if (!isViewingSelf && !isSystemAdmin && !isOrgAdmin) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "You don't have permission to view this user's permissions" }
      }, request);
    }

    // Get target user
    const targetUser = await db.readItem('users', targetUserId, targetUserId);
    if (!targetUser) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User not found" }
      }, request);
    }

    // Get user's system roles
    const systemRoles = (targetUser as any).systemRoles || [];

    // Get all permissions from system roles
    let allPermissions: string[] = [];

    // System admin has all permissions
    if (systemRoles.includes(SystemRole.SYSTEM_ADMIN)) {
      allPermissions = Object.values(PermissionAction);
    } else {
      // Get role assignments for the user
      let query = "SELECT * FROM c WHERE c.userId = @userId AND c.isActive = true";
      const parameters = [targetUserId];

      // Add organization filter if provided
      if (organizationId) {
        query += " AND (c.scope = 'SYSTEM' OR (c.scope = 'ORGANIZATION' AND c.organizationId = @organizationId)";

        // Add project filter if provided
        if (projectId) {
          query += " OR (c.scope = 'PROJECT' AND c.organizationId = @organizationId AND c.projectId = @projectId)";
          parameters.push(organizationId, projectId);
        } else {
          parameters.push(organizationId);
        }

        query += ")";
      }

      const roleAssignments = await db.queryItems('role-assignments', query, parameters);

      // Get permissions from assigned roles
      if (roleAssignments.length > 0) {
        // Get all role IDs
        const roleIds = roleAssignments.map((assignment: any) => assignment.roleId);

        // Get roles
        const roleQuery = "SELECT * FROM c WHERE ARRAY_CONTAINS(@roleIds, c.id)";
        const roles = await db.queryItems('roles', roleQuery, [roleIds]);

        // Collect all permissions
        roles.forEach((role: any) => {
          if (role.permissions) {
            allPermissions = [...allPermissions, ...role.permissions];
          }
        });
      }

      // Add permissions from system roles
      systemRoles.forEach((roleName: SystemRole) => {
        switch (roleName) {
          case SystemRole.ORGANIZATION_ADMIN:
            allPermissions = [...allPermissions, ...getOrganizationAdminPermissions()];
            break;
          case SystemRole.ORGANIZATION_MEMBER:
            allPermissions = [...allPermissions, ...getOrganizationMemberPermissions()];
            break;
          case SystemRole.ORGANIZATION_VIEWER:
            allPermissions = [...allPermissions, ...getOrganizationViewerPermissions()];
            break;
          case SystemRole.PROJECT_ADMIN:
            allPermissions = [...allPermissions, ...getProjectAdminPermissions()];
            break;
          case SystemRole.PROJECT_MEMBER:
            allPermissions = [...allPermissions, ...getProjectMemberPermissions()];
            break;
          case SystemRole.PROJECT_VIEWER:
            allPermissions = [...allPermissions, ...getProjectViewerPermissions()];
            break;
          case SystemRole.GUEST:
            allPermissions = [...allPermissions, ...getGuestPermissions()];
            break;
        }
      });
    }

    // Remove duplicates
    const uniquePermissions = [...new Set(allPermissions)];

    logger.info("User permissions retrieved successfully", {
      correlationId,
      targetUserId,
      permissionsCount: uniquePermissions.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        userId: targetUserId,
        systemRoles,
        permissions: uniquePermissions,
        organizationId,
        projectId
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get user permissions failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Helper functions to get permissions for each role
function getOrganizationAdminPermissions(): PermissionAction[] {
  return [
    PermissionAction.UPDATE_ORGANIZATION,
    PermissionAction.VIEW_ORGANIZATION,
    PermissionAction.MANAGE_ORGANIZATION,
    PermissionAction.VIEW_ORGANIZATION_MEMBERS,
    PermissionAction.INVITE_ORGANIZATION_MEMBER,
    PermissionAction.REMOVE_ORGANIZATION_MEMBER,
    PermissionAction.MANAGE_ORGANIZATION_MEMBERS,
    PermissionAction.CREATE_PROJECT,
    PermissionAction.UPDATE_PROJECT,
    PermissionAction.DELETE_PROJECT,
    PermissionAction.VIEW_PROJECT,
    PermissionAction.MANAGE_PROJECT,
    PermissionAction.UPLOAD_DOCUMENT,
    PermissionAction.VIEW_DOCUMENT,
    PermissionAction.UPDATE_DOCUMENT,
    PermissionAction.DELETE_DOCUMENT,
    PermissionAction.SHARE_DOCUMENT,
    PermissionAction.COMMENT_DOCUMENT,
    PermissionAction.VIEW_ANALYTICS,
    PermissionAction.VIEW_USER_PERMISSIONS,
    PermissionAction.GRANT_PERMISSION
  ];
}

function getOrganizationMemberPermissions(): PermissionAction[] {
  return [
    PermissionAction.VIEW_ORGANIZATION,
    PermissionAction.VIEW_ORGANIZATION_MEMBERS,
    PermissionAction.CREATE_PROJECT,
    PermissionAction.VIEW_PROJECT,
    PermissionAction.UPLOAD_DOCUMENT,
    PermissionAction.VIEW_DOCUMENT,
    PermissionAction.UPDATE_DOCUMENT,
    PermissionAction.SHARE_DOCUMENT,
    PermissionAction.COMMENT_DOCUMENT,
    PermissionAction.VIEW_ANALYTICS
  ];
}

function getOrganizationViewerPermissions(): PermissionAction[] {
  return [
    PermissionAction.VIEW_ORGANIZATION,
    PermissionAction.VIEW_ORGANIZATION_MEMBERS,
    PermissionAction.VIEW_PROJECT,
    PermissionAction.VIEW_DOCUMENT,
    PermissionAction.COMMENT_DOCUMENT,
    PermissionAction.VIEW_ANALYTICS
  ];
}

function getProjectAdminPermissions(): PermissionAction[] {
  return [
    PermissionAction.UPDATE_PROJECT,
    PermissionAction.VIEW_PROJECT,
    PermissionAction.MANAGE_PROJECT,
    PermissionAction.ADD_PROJECT_MEMBER,
    PermissionAction.REMOVE_PROJECT_MEMBER,
    PermissionAction.MANAGE_PROJECT_MEMBERS,
    PermissionAction.UPLOAD_DOCUMENT,
    PermissionAction.VIEW_DOCUMENT,
    PermissionAction.UPDATE_DOCUMENT,
    PermissionAction.DELETE_DOCUMENT,
    PermissionAction.SHARE_DOCUMENT,
    PermissionAction.COMMENT_DOCUMENT,
    PermissionAction.EDIT_ANY_COMMENT,
    PermissionAction.DELETE_ANY_COMMENT
  ];
}

function getProjectMemberPermissions(): PermissionAction[] {
  return [
    PermissionAction.VIEW_PROJECT,
    PermissionAction.UPLOAD_DOCUMENT,
    PermissionAction.VIEW_DOCUMENT,
    PermissionAction.UPDATE_DOCUMENT,
    PermissionAction.SHARE_DOCUMENT,
    PermissionAction.COMMENT_DOCUMENT
  ];
}

function getProjectViewerPermissions(): PermissionAction[] {
  return [
    PermissionAction.VIEW_PROJECT,
    PermissionAction.VIEW_DOCUMENT,
    PermissionAction.COMMENT_DOCUMENT
  ];
}

function getGuestPermissions(): PermissionAction[] {
  return [
    PermissionAction.VIEW_DOCUMENT
  ];
}

// Register functions
app.http('user-permissions', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/{userId?}/permissions',
  handler: getUserPermissions
});
