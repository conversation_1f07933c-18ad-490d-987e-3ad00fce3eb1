/**
 * Test script for Redis enhancements with database fallback
 * Tests all the critical cache-aside patterns and fallback mechanisms
 */

const path = require('path');

// Set environment variables for testing
process.env.NODE_ENV = 'development';
process.env.REDIS_ENABLED = 'true';
process.env.AZURE_REDIS_HOST = process.env.AZURE_REDIS_HOST || 'hepzbackend.eastus.redis.azure.net';
process.env.AZURE_REDIS_PORT = process.env.AZURE_REDIS_PORT || '10000';

async function testRedisEnhancements() {
  console.log('🚀 Starting Redis Enhancement Tests...\n');

  try {
    // Import services
    const appRoot = path.join(__dirname, '..');
    const { redis } = require(path.join(appRoot, 'src/shared/services/redis.ts'));
    const { cacheAside } = require(path.join(appRoot, 'src/shared/services/cache-aside.ts'));

    console.log('📦 Initializing Redis service...');
    await redis.initialize();
    console.log('✅ Redis service initialized\n');

    // Test 1: Session Management with Database Fallback
    console.log('🔍 Test 1: Session Management with Database Fallback');
    const testSessionId = 'test-session-123';
    const testSessionData = {
      id: testSessionId,
      userId: 'user-123',
      documentId: 'doc-456',
      status: 'ACTIVE',
      createdAt: new Date().toISOString()
    };

    // Test setting session
    console.log('  Setting session data...');
    const setResult = await redis.setSession(testSessionId, testSessionData, 300);
    console.log(`  ✅ Session set: ${setResult}`);

    // Test getting session (should come from cache)
    console.log('  Getting session from cache...');
    const cachedSession = await redis.getSession(testSessionId, false); // No fallback
    console.log(`  ✅ Session retrieved from cache: ${cachedSession ? 'Found' : 'Not found'}`);

    // Test getting session with fallback (simulate cache miss)
    console.log('  Testing database fallback...');
    await redis.deleteSession(testSessionId); // Clear cache
    const fallbackSession = await redis.getSession(testSessionId, true); // With fallback
    console.log(`  ✅ Session fallback test: ${fallbackSession ? 'Database fallback worked' : 'No fallback data'}`);

    console.log('');

    // Test 2: Document Content Management
    console.log('🔍 Test 2: Document Content Management');
    const testDocumentId = 'test-doc-789';
    const testContent = 'This is test document content for Redis enhancement testing.';

    // Test setting document content
    console.log('  Setting document content...');
    const contentSetResult = await redis.setDocumentContent(testDocumentId, testContent, 300);
    console.log(`  ✅ Document content set: ${contentSetResult}`);

    // Test getting document content (should come from cache)
    console.log('  Getting document content from cache...');
    const cachedContent = await redis.getDocumentContent(testDocumentId, false); // No fallback
    console.log(`  ✅ Document content retrieved: ${cachedContent ? 'Found' : 'Not found'}`);

    // Test cache invalidation
    console.log('  Testing cache invalidation...');
    await redis.invalidateDocumentCaches(testDocumentId);
    const afterInvalidation = await redis.get(`document:${testDocumentId}:content`);
    console.log(`  ✅ Cache invalidation: ${afterInvalidation ? 'Failed' : 'Success'}`);

    console.log('');

    // Test 3: User Activity Tracking
    console.log('🔍 Test 3: User Activity Tracking');
    const testUserId = 'test-user-456';

    // Test getting user activities (should fallback to database)
    console.log('  Getting user activities with fallback...');
    const activities = await redis.getUserActivities(testUserId, 10, true);
    console.log(`  ✅ User activities retrieved: ${activities.length} activities`);

    console.log('');

    // Test 4: Device Registration
    console.log('🔍 Test 4: Device Registration');
    const testPlatform = 'ios';

    // Test getting device registration (should fallback to database)
    console.log('  Getting device registration with fallback...');
    const device = await redis.getDeviceRegistration(testUserId, testPlatform, true);
    console.log(`  ✅ Device registration: ${device ? 'Found' : 'Not found'}`);

    console.log('');

    // Test 5: Cache-Aside Service
    console.log('🔍 Test 5: Generic Cache-Aside Service');
    const testCacheKey = 'test-cache-key';
    const testData = { id: '123', name: 'Test Item', value: 42 };

    // Test cache-aside set
    console.log('  Testing cache-aside set...');
    const cacheSetResult = await cacheAside.set(testCacheKey, testData, {
      ttlSeconds: 300,
      cachePrefix: 'test',
      invalidatePatterns: ['test:*:related']
    });
    console.log(`  ✅ Cache-aside set: ${cacheSetResult}`);

    // Test cache-aside get (should come from cache)
    console.log('  Testing cache-aside get...');
    const cachedData = await cacheAside.get(
      testCacheKey,
      {
        containerName: 'test-container',
        query: 'SELECT * FROM c WHERE c.id = @id',
        parameters: ['123']
      },
      {
        cachePrefix: 'test',
        enableFallback: false // No fallback for this test
      }
    );
    console.log(`  ✅ Cache-aside get: ${cachedData ? 'Found' : 'Not found'}`);

    console.log('');

    // Test 6: Cache Invalidation Patterns
    console.log('🔍 Test 6: Cache Invalidation Patterns');
    
    // Set multiple related cache entries
    await redis.set('user:123:profile', JSON.stringify({ name: 'Test User' }), 300);
    await redis.set('user:123:preferences', JSON.stringify({ theme: 'dark' }), 300);
    await redis.set('user:123:activities', JSON.stringify([]), 300);

    console.log('  Set multiple user cache entries...');
    
    // Test user cache invalidation
    console.log('  Testing user cache invalidation...');
    await redis.invalidateUserCaches('123');
    
    // Check if caches were invalidated
    const profileAfter = await redis.get('user:123:profile');
    const preferencesAfter = await redis.get('user:123:preferences');
    const activitiesAfter = await redis.get('user:123:activities');
    
    const invalidationSuccess = !profileAfter && !preferencesAfter && !activitiesAfter;
    console.log(`  ✅ User cache invalidation: ${invalidationSuccess ? 'Success' : 'Failed'}`);

    console.log('');

    // Test 7: Redis Availability and Graceful Degradation
    console.log('🔍 Test 7: Redis Availability and Graceful Degradation');
    console.log(`  Redis availability: ${redis.isAvailable()}`);
    
    const metrics = redis.getMetrics();
    console.log('  Redis metrics:');
    console.log(`    Operations: ${metrics.operations}`);
    console.log(`    Errors: ${metrics.errors}`);
    console.log(`    Cache hit rate: ${(metrics.cacheHitRate * 100).toFixed(2)}%`);
    console.log(`    Last operation: ${metrics.lastOperation}`);

    console.log('');

    // Test 8: Performance Test
    console.log('🔍 Test 8: Performance Test');
    const startTime = Date.now();
    
    // Perform multiple cache operations
    const promises = [];
    for (let i = 0; i < 10; i++) {
      promises.push(redis.set(`perf:test:${i}`, `value-${i}`, 60));
    }
    
    await Promise.all(promises);
    
    const endTime = Date.now();
    console.log(`  ✅ Performance test: ${promises.length} operations in ${endTime - startTime}ms`);

    console.log('');
    console.log('🎉 All Redis enhancement tests completed successfully!');
    console.log('');
    console.log('📊 Summary:');
    console.log('✅ Session management with database fallback');
    console.log('✅ Document content management with cache invalidation');
    console.log('✅ User activity tracking with fallback');
    console.log('✅ Device registration with fallback');
    console.log('✅ Generic cache-aside service');
    console.log('✅ Cache invalidation patterns');
    console.log('✅ Graceful degradation');
    console.log('✅ Performance optimization');

  } catch (error) {
    console.error('❌ Redis enhancement test failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the tests
testRedisEnhancements()
  .then(() => {
    console.log('\n✅ Test script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Test script failed:', error);
    process.exit(1);
  });
