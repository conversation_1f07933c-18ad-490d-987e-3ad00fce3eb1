/**
 * Deploy Functions and Configure Event Grid
 * This script deploys the functions with Event Grid triggers and then configures subscriptions
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  resourceGroup: 'docucontext',
  functionAppName: 'hepzlogic',
  eventGridTopicName: 'hepzeg',
  storageAccountName: 'stdocucontex900520441468',
  location: 'eastus'
};

console.log('🚀 Deploy Functions and Configure Event Grid\n');

/**
 * Execute command with error handling
 */
function runCommand(command, description, throwOnError = false) {
  try {
    console.log(`📋 ${description}...`);
    console.log(`   Command: ${command}`);
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description} - SUCCESS`);
    return { success: true, output: result };
  } catch (error) {
    console.log(`⚠️ ${description} - FAILED`);
    console.log(`   Error: ${error.message}`);
    if (throwOnError) {
      throw error;
    }
    return { success: false, error: error.message };
  }
}

/**
 * Get subscription ID
 */
function getSubscriptionId() {
  try {
    const result = execSync('az account show --query id --output tsv', { encoding: 'utf8' });
    return result.trim();
  } catch (error) {
    console.error('❌ Failed to get subscription ID');
    process.exit(1);
  }
}

/**
 * Check if functions are deployed
 */
function checkFunctionDeployment() {
  try {
    const result = execSync(`az functionapp function list --name ${config.functionAppName} --resource-group ${config.resourceGroup} --query "[?contains(name, 'EventGrid')].name" --output tsv`, { encoding: 'utf8' });
    const functions = result.trim().split('\n').filter(f => f);
    
    console.log('📋 Event Grid Functions Found:');
    functions.forEach(func => console.log(`   - ${func}`));
    
    return functions.length >= 2; // We expect at least 2 Event Grid functions
  } catch (error) {
    console.log('⚠️ Could not check function deployment status');
    return false;
  }
}

/**
 * Deploy functions to Azure
 */
function deployFunctions() {
  console.log('📦 Deploying Functions to Azure...\n');
  
  // Build the project first
  runCommand('npm run build', 'Building TypeScript project', true);
  
  // Deploy to Azure
  runCommand(`func azure functionapp publish ${config.functionAppName}`, 'Deploying to Azure Function App', true);
  
  // Wait a moment for deployment to complete
  console.log('⏳ Waiting for deployment to complete...');
  setTimeout(() => {}, 10000); // 10 second delay
  
  return checkFunctionDeployment();
}

/**
 * Configure Event Grid subscriptions
 */
function configureEventGrid() {
  console.log('🔧 Configuring Event Grid Subscriptions...\n');
  
  const subscriptionId = getSubscriptionId();
  console.log(`📋 Subscription: ${subscriptionId}`);
  
  // Resource IDs
  const storageAccountId = `/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.Storage/storageAccounts/${config.storageAccountName}`;
  const eventGridTopicId = `/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.EventGrid/topics/${config.eventGridTopicName}`;
  const functionAppId = `/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.Web/sites/${config.functionAppName}`;
  
  const storageSystemTopic = `${config.storageAccountName}-events`;

  // 1. Create Storage System Topic
  runCommand(
    `az eventgrid system-topic create --name "${storageSystemTopic}" --resource-group "${config.resourceGroup}" --source "${storageAccountId}" --topic-type "Microsoft.Storage.StorageAccounts" --location "${config.location}"`,
    'Creating Storage System Topic'
  );

  // 2. Storage Events to Event Grid Function
  runCommand(
    `az eventgrid event-subscription create --name "storage-to-eventgrid-function" --source-resource-id "/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.EventGrid/systemTopics/${storageSystemTopic}" --endpoint "${functionAppId}/functions/storageEventGridTrigger" --endpoint-type azurefunction --included-event-types "Microsoft.Storage.BlobCreated" "Microsoft.Storage.BlobDeleted" --max-delivery-attempts 3 --event-ttl 1440`,
    'Creating Storage to Event Grid Function subscription'
  );

  // 3. Custom Events to Event Grid Function
  runCommand(
    `az eventgrid event-subscription create --name "custom-to-eventgrid-function" --source-resource-id "${eventGridTopicId}" --endpoint "${functionAppId}/functions/customEventGridTrigger" --endpoint-type azurefunction --max-delivery-attempts 3 --event-ttl 1440`,
    'Creating Custom Events to Event Grid Function subscription'
  );

  // 4. Create Dead Letter Storage Container
  runCommand(
    `az storage container create --name "event-dead-letters" --account-name "${config.storageAccountName}" --auth-mode login`,
    'Creating Dead Letter Storage Container'
  );

  console.log('\n🎉 Event Grid Configuration Complete!\n');
  
  console.log('📊 Configuration Summary:');
  console.log(`   ✅ Storage System Topic: ${storageSystemTopic}`);
  console.log(`   ✅ Storage Events → storageEventGridTrigger function`);
  console.log(`   ✅ Custom Events → customEventGridTrigger function`);
  console.log(`   ✅ Dead Letter Storage: event-dead-letters`);
  console.log(`   ✅ Function App: ${config.functionAppName}\n`);
  
  console.log('🔄 Next Steps:');
  console.log('   1. Test Event Grid with: node scripts/test-event-grid.js');
  console.log('   2. Monitor events in Azure Portal');
  console.log('   3. Check Function App logs for event processing');
  console.log('   4. Upload a file to test storage events');
}

/**
 * Main execution
 */
async function main() {
  try {
    console.log('🔍 Checking prerequisites...');
    
    // Check if Azure CLI is logged in
    try {
      execSync('az account show', { stdio: 'pipe' });
      console.log('✅ Azure CLI is logged in');
    } catch (error) {
      console.error('❌ Azure CLI is not logged in. Please run "az login" first.');
      process.exit(1);
    }
    
    // Check if Azure Functions Core Tools is available
    try {
      execSync('func --version', { stdio: 'pipe' });
      console.log('✅ Azure Functions Core Tools is available');
    } catch (error) {
      console.error('❌ Azure Functions Core Tools not found. Please install it first.');
      process.exit(1);
    }
    
    console.log('\n📦 Step 1: Deploy Functions with Event Grid Triggers');
    const deploymentSuccess = deployFunctions();
    
    if (!deploymentSuccess) {
      console.log('⚠️ Event Grid functions may not be deployed yet. Continuing with configuration...');
    }
    
    console.log('\n🔧 Step 2: Configure Event Grid Subscriptions');
    configureEventGrid();
    
    console.log('\n🎯 Deployment and Configuration Complete!');
    console.log('Your Event Grid is now properly configured with native triggers.');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    process.exit(1);
  }
}

// Run if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { main, config };
