/**
 * Data Encryption Function
 * Handles data encryption, decryption, and key management
 * Migrated from old-arch/src/security-service/encryption/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import eventService from '../shared/services/event-driven-cache';

// Encryption types and enums
enum EncryptionAlgorithm {
  AES_256_GCM = 'AES_256_GCM',
  AES_128_GCM = 'AES_128_GCM',
  CHACHA20_POLY1305 = 'CHACHA20_POLY1305'
}

enum KeyType {
  MASTER = 'MASTER',
  DATA = 'DATA',
  DOCUMENT = 'DOCUMENT',
  FIELD = 'FIELD',
  BACKUP = 'BACKUP'
}

enum KeyStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ROTATED = 'ROTATED',
  COMPROMISED = 'COMPROMISED',
  EXPIRED = 'EXPIRED'
}

// Validation schemas
const encryptDataSchema = Joi.object({
  data: Joi.string().required(),
  keyType: Joi.string().valid(...Object.values(KeyType)).default(KeyType.DATA),
  algorithm: Joi.string().valid(...Object.values(EncryptionAlgorithm)).default(EncryptionAlgorithm.AES_256_GCM),
  organizationId: Joi.string().uuid().required(),
  context: Joi.object({
    documentId: Joi.string().uuid().optional(),
    fieldName: Joi.string().optional(),
    purpose: Joi.string().optional(),
    classification: Joi.string().valid('public', 'internal', 'confidential', 'restricted').optional()
  }).optional(),
  metadata: Joi.object().optional()
});

const decryptDataSchema = Joi.object({
  encryptedData: Joi.string().required(),
  keyId: Joi.string().uuid().required(),
  organizationId: Joi.string().uuid().required(),
  context: Joi.object({
    documentId: Joi.string().uuid().optional(),
    fieldName: Joi.string().optional(),
    purpose: Joi.string().optional()
  }).optional()
});

const createEncryptionKeySchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  keyType: Joi.string().valid(...Object.values(KeyType)).required(),
  algorithm: Joi.string().valid(...Object.values(EncryptionAlgorithm)).default(EncryptionAlgorithm.AES_256_GCM),
  organizationId: Joi.string().uuid().required(),
  expirationDays: Joi.number().min(1).max(3650).default(365),
  rotationDays: Joi.number().min(1).max(365).default(90),
  metadata: Joi.object().optional()
});

interface EncryptDataRequest {
  data: string;
  keyType?: KeyType;
  algorithm?: EncryptionAlgorithm;
  organizationId: string;
  context?: {
    documentId?: string;
    fieldName?: string;
    purpose?: string;
    classification?: string;
  };
  metadata?: any;
}

interface DecryptDataRequest {
  encryptedData: string;
  keyId: string;
  organizationId: string;
  context?: {
    documentId?: string;
    fieldName?: string;
    purpose?: string;
  };
}

interface EncryptionKey {
  id: string;
  name: string;
  description?: string;
  keyType: KeyType;
  algorithm: EncryptionAlgorithm;
  status: KeyStatus;
  organizationId: string;
  keyData: {
    encryptedKey: string;
    keyVersion: number;
    salt: string;
    iv: string;
  };
  usage: {
    encryptionCount: number;
    decryptionCount: number;
    lastUsed?: string;
  };
  lifecycle: {
    createdAt: string;
    expiresAt: string;
    rotationDue: string;
    rotatedFrom?: string;
    rotatedTo?: string;
  };
  access: {
    allowedUsers: string[];
    allowedRoles: string[];
    accessLog: Array<{
      userId: string;
      action: string;
      timestamp: string;
      ipAddress?: string;
    }>;
  };
  metadata: any;
  createdBy: string;
  updatedAt: string;
  tenantId: string;
}

interface EncryptionOperation {
  id: string;
  type: 'ENCRYPT' | 'DECRYPT';
  keyId: string;
  organizationId: string;
  context?: any;
  metadata: {
    algorithm: EncryptionAlgorithm;
    dataSize: number;
    duration: number;
    success: boolean;
    errorMessage?: string;
  };
  audit: {
    userId: string;
    ipAddress?: string;
    userAgent?: string;
    timestamp: string;
  };
  tenantId: string;
}

/**
 * Encrypt data handler
 */
export async function encryptData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Encrypt data started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = encryptDataSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const encryptRequest: EncryptDataRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(encryptRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check encryption permissions
    const hasEncryptionAccess = await checkEncryptionAccess(user, encryptRequest.organizationId);
    if (!hasEncryptionAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to encryption operations" }
      }, request);
    }

    // Get or create encryption key
    const encryptionKey = await getOrCreateEncryptionKey(
      encryptRequest.organizationId,
      encryptRequest.keyType || KeyType.DATA,
      encryptRequest.algorithm || EncryptionAlgorithm.AES_256_GCM,
      user.id
    );

    if (!encryptionKey) {
      return addCorsHeaders({
        status: 500,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Failed to obtain encryption key" }
      }, request);
    }

    // Perform encryption
    const encryptionResult = await performEncryption(
      encryptRequest.data,
      encryptionKey,
      encryptRequest.algorithm || EncryptionAlgorithm.AES_256_GCM
    );

    if (!encryptionResult.success) {
      return addCorsHeaders({
        status: 500,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Encryption failed", details: encryptionResult.error }
      }, request);
    }

    const duration = Date.now() - startTime;

    // Log encryption operation
    await logEncryptionOperation({
      type: 'ENCRYPT',
      keyId: encryptionKey.id,
      organizationId: encryptRequest.organizationId,
      context: encryptRequest.context,
      metadata: {
        algorithm: encryptRequest.algorithm || EncryptionAlgorithm.AES_256_GCM,
        dataSize: encryptRequest.data.length,
        duration,
        success: true
      },
      audit: {
        userId: user.id,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date().toISOString()
      },
      tenantId: user.tenantId || user.id
    });

    // Update key usage
    await updateKeyUsage(encryptionKey.id, 'encrypt');

    logger.info("Data encrypted successfully", {
      correlationId,
      keyId: encryptionKey.id,
      algorithm: encryptRequest.algorithm,
      dataSize: encryptRequest.data.length,
      duration,
      encryptedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        encryptedData: encryptionResult.encryptedData,
        keyId: encryptionKey.id,
        algorithm: encryptRequest.algorithm || EncryptionAlgorithm.AES_256_GCM,
        metadata: {
          iv: encryptionResult.iv,
          authTag: encryptionResult.authTag,
          keyVersion: encryptionKey.keyData.keyVersion
        },
        encryptedAt: new Date().toISOString(),
        message: "Data encrypted successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Encrypt data failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Decrypt data handler
 */
export async function decryptData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Decrypt data started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = decryptDataSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const decryptRequest: DecryptDataRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(decryptRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Get encryption key
    const encryptionKey = await getEncryptionKey(decryptRequest.keyId);
    if (!encryptionKey) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Encryption key not found" }
      }, request);
    }

    // Check key access
    const hasKeyAccess = await checkKeyAccess(encryptionKey, user.id);
    if (!hasKeyAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to encryption key" }
      }, request);
    }

    // Perform decryption
    const decryptionResult = await performDecryption(
      decryptRequest.encryptedData,
      encryptionKey
    );

    if (!decryptionResult.success) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Decryption failed", details: decryptionResult.error }
      }, request);
    }

    const duration = Date.now() - startTime;

    // Log decryption operation
    await logEncryptionOperation({
      type: 'DECRYPT',
      keyId: encryptionKey.id,
      organizationId: decryptRequest.organizationId,
      context: decryptRequest.context,
      metadata: {
        algorithm: encryptionKey.algorithm,
        dataSize: decryptRequest.encryptedData.length,
        duration,
        success: true
      },
      audit: {
        userId: user.id,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date().toISOString()
      },
      tenantId: user.tenantId || user.id
    });

    // Update key usage
    await updateKeyUsage(encryptionKey.id, 'decrypt');

    logger.info("Data decrypted successfully", {
      correlationId,
      keyId: encryptionKey.id,
      algorithm: encryptionKey.algorithm,
      dataSize: decryptRequest.encryptedData.length,
      duration,
      decryptedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        data: decryptionResult.data,
        keyId: encryptionKey.id,
        algorithm: encryptionKey.algorithm,
        decryptedAt: new Date().toISOString(),
        message: "Data decrypted successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Decrypt data failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkEncryptionAccess(user: any, organizationId: string): Promise<boolean> {
  try {
    // Check if user has admin or encryption role
    if (user.roles?.includes('admin') || user.roles?.includes('encryption_admin')) {
      return true;
    }

    // Check organization-level permissions
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);

    if (memberships.length > 0) {
      const membership = memberships[0] as any;
      return membership.role === 'OWNER' || membership.role === 'ADMIN';
    }

    return false;
  } catch (error) {
    logger.error('Failed to check encryption access', { error, userId: user.id, organizationId });
    return false;
  }
}

async function getOrCreateEncryptionKey(organizationId: string, keyType: KeyType, algorithm: EncryptionAlgorithm, userId: string): Promise<EncryptionKey | null> {
  try {
    // Try to find existing active key
    const keyQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.keyType = @keyType AND c.algorithm = @algorithm AND c.status = @active';
    const existingKeys = await db.queryItems('encryption-keys', keyQuery, [organizationId, keyType, algorithm, KeyStatus.ACTIVE]);

    if (existingKeys.length > 0) {
      return existingKeys[0] as EncryptionKey;
    }

    // Create new key
    return await createNewEncryptionKey(organizationId, keyType, algorithm, userId);

  } catch (error) {
    logger.error('Failed to get or create encryption key', { error, organizationId, keyType, algorithm });
    return null;
  }
}

async function createNewEncryptionKey(organizationId: string, keyType: KeyType, algorithm: EncryptionAlgorithm, userId: string): Promise<EncryptionKey | null> {
  try {
    const keyId = uuidv4();
    const now = new Date().toISOString();
    const expirationDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(); // 1 year
    const rotationDate = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(); // 90 days

    // Generate encryption key (simplified - in production use proper key generation)
    const keyData = generateEncryptionKeyData(algorithm);

    const encryptionKey: EncryptionKey = {
      id: keyId,
      name: `${keyType}_${algorithm}_${Date.now()}`,
      keyType,
      algorithm,
      status: KeyStatus.ACTIVE,
      organizationId,
      keyData,
      usage: {
        encryptionCount: 0,
        decryptionCount: 0
      },
      lifecycle: {
        createdAt: now,
        expiresAt: expirationDate,
        rotationDue: rotationDate
      },
      access: {
        allowedUsers: [userId],
        allowedRoles: ['admin', 'encryption_admin'],
        accessLog: []
      },
      metadata: {},
      createdBy: userId,
      updatedAt: now,
      tenantId: organizationId
    };

    await db.createItem('encryption-keys', encryptionKey);
    return encryptionKey;

  } catch (error) {
    logger.error('Failed to create new encryption key', { error, organizationId, keyType, algorithm });
    return null;
  }
}

function generateEncryptionKeyData(algorithm: EncryptionAlgorithm): any {
  // Production-grade key generation using Node.js crypto
  const crypto = require('crypto');

  try {
    // Use appropriate key sizes for different algorithms
    const keySize = algorithm === EncryptionAlgorithm.AES_256_GCM ? 32 : 16;

    // Generate cryptographically secure random key
    const key = crypto.randomBytes(keySize);

    // Generate salt for key derivation
    const salt = crypto.randomBytes(32); // Larger salt for better security

    // Generate IV for encryption
    const iv = crypto.randomBytes(16);

    // Create key derivation using PBKDF2
    const derivedKey = crypto.pbkdf2Sync(key, salt, 100000, keySize, 'sha256');

    logger.info('Encryption key generated successfully', {
      algorithm,
      keySize,
      saltLength: salt.length,
      ivLength: iv.length
    });

    return {
      encryptedKey: derivedKey.toString('base64'),
      keyVersion: 1,
      salt: salt.toString('base64'),
      iv: iv.toString('base64'),
      algorithm: algorithm,
      keySize: keySize,
      iterations: 100000,
      hashFunction: 'sha256'
    };
  } catch (error) {
    logger.error('Key generation failed', {
      error: error instanceof Error ? error.message : String(error),
      algorithm
    });
    throw new Error('Failed to generate encryption key');
  }
}

async function performEncryption(data: string, key: EncryptionKey, algorithm: EncryptionAlgorithm): Promise<any> {
  try {
    // Production-grade encryption using AES-256-GCM
    const crypto = require('crypto');

    const keyBuffer = Buffer.from(key.keyData.encryptedKey, 'base64');
    const iv = crypto.randomBytes(16); // 128-bit IV for AES

    // Use AES-256-GCM for authenticated encryption
    const cipher = crypto.createCipherGCM('aes-256-gcm', keyBuffer, iv);

    // Add additional authenticated data (AAD)
    const aad = Buffer.from(JSON.stringify({
      keyId: key.id,
      algorithm: algorithm,
      timestamp: new Date().toISOString()
    }));
    cipher.setAAD(aad);

    let encrypted = cipher.update(data, 'utf8', 'base64');
    encrypted += cipher.final('base64');

    const authTag = cipher.getAuthTag();

    logger.info('Data encrypted successfully', {
      algorithm,
      keyId: key.id,
      dataLength: data.length,
      encryptedLength: encrypted.length
    });

    return {
      success: true,
      encryptedData: encrypted,
      iv: iv.toString('base64'),
      authTag: authTag.toString('base64'),
      aad: aad.toString('base64'),
      algorithm: algorithm,
      keyId: key.id,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logger.error('Encryption failed', {
      error: error instanceof Error ? error.message : String(error),
      keyId: key.id,
      algorithm
    });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Encryption failed'
    };
  }
}

async function performDecryption(encryptedData: any, key: EncryptionKey): Promise<any> {
  try {
    // Production-grade decryption using AES-256-GCM
    const crypto = require('crypto');

    const keyBuffer = Buffer.from(key.keyData.encryptedKey, 'base64');
    const iv = Buffer.from(encryptedData.iv, 'base64');
    const authTag = Buffer.from(encryptedData.authTag, 'base64');
    const aad = Buffer.from(encryptedData.aad || '', 'base64');

    // Use AES-256-GCM for authenticated decryption
    const decipher = crypto.createDecipherGCM('aes-256-gcm', keyBuffer, iv);

    // Set the authentication tag
    decipher.setAuthTag(authTag);

    // Set additional authenticated data if present
    if (encryptedData.aad) {
      decipher.setAAD(aad);
    }

    let decrypted = decipher.update(encryptedData.encryptedData, 'base64', 'utf8');
    decrypted += decipher.final('utf8');

    logger.info('Data decrypted successfully', {
      keyId: key.id,
      algorithm: encryptedData.algorithm,
      decryptedLength: decrypted.length
    });

    return {
      success: true,
      data: decrypted,
      algorithm: encryptedData.algorithm,
      keyId: key.id
    };

  } catch (error) {
    logger.error('Decryption failed', {
      error: error instanceof Error ? error.message : String(error),
      keyId: key.id
    });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Decryption failed'
    };
  }
}

async function getEncryptionKey(keyId: string): Promise<EncryptionKey | null> {
  try {
    const key = await db.readItem('encryption-keys', keyId, keyId);
    return key as EncryptionKey;
  } catch (error) {
    logger.error('Failed to get encryption key', { error, keyId });
    return null;
  }
}

async function checkKeyAccess(key: EncryptionKey, userId: string): Promise<boolean> {
  try {
    return key.access.allowedUsers.includes(userId) || key.createdBy === userId;
  } catch (error) {
    logger.error('Failed to check key access', { error, keyId: key.id, userId });
    return false;
  }
}

async function logEncryptionOperation(operation: Omit<EncryptionOperation, 'id'>): Promise<void> {
  try {
    const operationRecord: EncryptionOperation = {
      id: uuidv4(),
      ...operation
    };

    await db.createItem('encryption-operations', operationRecord);
  } catch (error) {
    logger.error('Failed to log encryption operation', { error });
  }
}

async function updateKeyUsage(keyId: string, operation: 'encrypt' | 'decrypt'): Promise<void> {
  try {
    const key = await db.readItem('encryption-keys', keyId, keyId);
    if (key) {
      const keyData = key as any;

      if (operation === 'encrypt') {
        keyData.usage.encryptionCount += 1;
      } else {
        keyData.usage.decryptionCount += 1;
      }

      keyData.usage.lastUsed = new Date().toISOString();
      keyData.updatedAt = new Date().toISOString();

      await db.updateItem('encryption-keys', keyData);
    }
  } catch (error) {
    logger.error('Failed to update key usage', { error, keyId, operation });
  }
}

// Register functions
app.http('data-encrypt', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'security/encrypt',
  handler: encryptData
});

app.http('data-decrypt', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'security/decrypt',
  handler: decryptData
});
