/**
 * Cache Warming Scheduler
 * Azure Function that runs on a timer to warm frequently accessed cache entries
 * Implements intelligent cache warming based on usage patterns and analytics
 */

import { app, InvocationContext, Timer } from '@azure/functions';
import { logger } from '../shared/utils/logger';
import { eventDrivenCache } from '../shared/services/event-driven-cache';
import { cacheAside } from '../shared/services/cache-aside';
import { redis } from '../shared/services/redis';
import { db } from '../shared/services/database';

interface WarmingAnalytics {
  mostAccessedKeys: string[];
  cacheMissPatterns: string[];
  highTrafficPeriods: { hour: number; count: number }[];
  userActivityPatterns: { userId: string; accessCount: number }[];
}

interface WarmingJob {
  id: string;
  pattern: string;
  priority: 'high' | 'medium' | 'low';
  lastExecuted?: Date;
  successCount: number;
  errorCount: number;
  avgExecutionTime: number;
}

/**
 * Cache Warming Scheduler - Runs every 5 minutes
 */
async function cacheWarmingScheduler(myTimer: Timer, context: InvocationContext): Promise<void> {
  const correlationId = context.invocationId;
  
  logger.info("Cache warming scheduler started", {
    correlationId,
    functionName: context.functionName,
    scheduleStatus: myTimer.scheduleStatus,
    isPastDue: myTimer.isPastDue
  });

  try {
    // Initialize event-driven cache service
    await eventDrivenCache.initialize();

    // Get current statistics
    const stats = eventDrivenCache.getStatistics();
    logger.info("Current cache statistics", { correlationId, stats });

    // Perform intelligent cache warming
    await performIntelligentWarming(correlationId);

    // Analyze cache usage patterns
    await analyzeCacheUsagePatterns(correlationId);

    // Clean up expired warming jobs
    await cleanupExpiredJobs(correlationId);

    // Update warming configuration based on analytics
    await updateWarmingConfiguration(correlationId);

    logger.info("Cache warming scheduler completed successfully", {
      correlationId,
      duration: Date.now() - new Date(context.invocationId).getTime()
    });

  } catch (error) {
    logger.error("Cache warming scheduler failed", {
      correlationId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Perform intelligent cache warming based on usage patterns
 */
async function performIntelligentWarming(correlationId: string): Promise<void> {
  try {
    logger.info("Starting intelligent cache warming", { correlationId });

    // Get warming analytics
    const analytics = await getWarmingAnalytics();
    
    // Warm most accessed keys
    await warmMostAccessedKeys(analytics.mostAccessedKeys, correlationId);
    
    // Warm based on cache miss patterns
    await warmCacheMissPatterns(analytics.cacheMissPatterns, correlationId);
    
    // Warm based on user activity patterns
    await warmUserActivityPatterns(analytics.userActivityPatterns, correlationId);
    
    // Warm based on time-based patterns
    await warmTimeBasedPatterns(analytics.highTrafficPeriods, correlationId);

    logger.info("Intelligent cache warming completed", { correlationId });

  } catch (error) {
    logger.error("Failed to perform intelligent warming", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Get warming analytics from Redis and database
 */
async function getWarmingAnalytics(): Promise<WarmingAnalytics> {
  try {
    // This would typically analyze Redis access logs, database query patterns, etc.
    // For now, we'll return mock analytics based on common patterns
    
    return {
      mostAccessedKeys: [
        'feature_flag:document_collaboration',
        'feature_flag:real_time_editing',
        'config:global:global:max_file_size',
        'config:global:global:session_timeout',
        'session:*',
        'document:*:content'
      ],
      cacheMissPatterns: [
        'user:*:recent_activities',
        'device:*:ios',
        'document:*:metadata',
        'bi_report:*'
      ],
      highTrafficPeriods: [
        { hour: 9, count: 1500 },  // 9 AM
        { hour: 13, count: 1200 }, // 1 PM
        { hour: 15, count: 1800 }, // 3 PM
        { hour: 17, count: 1000 }  // 5 PM
      ],
      userActivityPatterns: [
        { userId: 'user-123', accessCount: 45 },
        { userId: 'user-456', accessCount: 38 },
        { userId: 'user-789', accessCount: 32 }
      ]
    };

  } catch (error) {
    logger.error("Failed to get warming analytics", {
      error: error instanceof Error ? error.message : String(error)
    });
    
    // Return empty analytics on error
    return {
      mostAccessedKeys: [],
      cacheMissPatterns: [],
      highTrafficPeriods: [],
      userActivityPatterns: []
    };
  }
}

/**
 * Warm most accessed cache keys
 */
async function warmMostAccessedKeys(keys: string[], correlationId: string): Promise<void> {
  try {
    logger.info("Warming most accessed keys", { correlationId, count: keys.length });

    for (const keyPattern of keys) {
      if (keyPattern.includes('*')) {
        // Handle pattern-based warming
        await warmKeyPattern(keyPattern, 'high');
      } else {
        // Handle specific key warming
        await warmSpecificKey(keyPattern, 'high');
      }
    }

  } catch (error) {
    logger.error("Failed to warm most accessed keys", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Warm cache based on miss patterns
 */
async function warmCacheMissPatterns(patterns: string[], correlationId: string): Promise<void> {
  try {
    logger.info("Warming cache miss patterns", { correlationId, count: patterns.length });

    for (const pattern of patterns) {
      await warmKeyPattern(pattern, 'medium');
    }

  } catch (error) {
    logger.error("Failed to warm cache miss patterns", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Warm cache based on user activity patterns
 */
async function warmUserActivityPatterns(patterns: { userId: string; accessCount: number }[], correlationId: string): Promise<void> {
  try {
    logger.info("Warming user activity patterns", { correlationId, count: patterns.length });

    for (const pattern of patterns) {
      // Warm user-specific caches for high-activity users
      if (pattern.accessCount > 30) {
        await warmUserSpecificCaches(pattern.userId, 'high');
      } else if (pattern.accessCount > 15) {
        await warmUserSpecificCaches(pattern.userId, 'medium');
      }
    }

  } catch (error) {
    logger.error("Failed to warm user activity patterns", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Warm cache based on time-based patterns
 */
async function warmTimeBasedPatterns(periods: { hour: number; count: number }[], correlationId: string): Promise<void> {
  try {
    const currentHour = new Date().getHours();
    const nextHour = (currentHour + 1) % 24;
    
    // Find if next hour is a high-traffic period
    const nextPeriod = periods.find(p => p.hour === nextHour);
    
    if (nextPeriod && nextPeriod.count > 1000) {
      logger.info("Pre-warming for high-traffic period", { 
        correlationId, 
        nextHour, 
        expectedCount: nextPeriod.count 
      });
      
      // Pre-warm high-priority caches
      await warmKeyPattern('session:*', 'high');
      await warmKeyPattern('feature_flag:*', 'high');
      await warmKeyPattern('config:*', 'high');
    }

  } catch (error) {
    logger.error("Failed to warm time-based patterns", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Warm specific key pattern
 */
async function warmKeyPattern(pattern: string, priority: 'high' | 'medium' | 'low'): Promise<void> {
  try {
    // Emit warming event
    cacheAside.emitCacheEvent({
      type: 'warm',
      key: pattern,
      pattern,
      priority,
      timestamp: new Date(),
      source: 'scheduler-pattern'
    });

    logger.debug("Key pattern warming event emitted", { pattern, priority });

  } catch (error) {
    logger.error("Failed to warm key pattern", {
      error: error instanceof Error ? error.message : String(error),
      pattern
    });
  }
}

/**
 * Warm specific cache key
 */
async function warmSpecificKey(key: string, priority: 'high' | 'medium' | 'low'): Promise<void> {
  try {
    // Check if key exists in cache
    const exists = await redis.get(key);
    
    if (!exists) {
      // Emit warming event for missing key
      cacheAside.emitCacheEvent({
        type: 'warm',
        key,
        priority,
        timestamp: new Date(),
        source: 'scheduler-specific'
      });
      
      logger.debug("Specific key warming event emitted", { key, priority });
    }

  } catch (error) {
    logger.error("Failed to warm specific key", {
      error: error instanceof Error ? error.message : String(error),
      key
    });
  }
}

/**
 * Warm user-specific caches
 */
async function warmUserSpecificCaches(userId: string, priority: 'high' | 'medium' | 'low'): Promise<void> {
  try {
    const userPatterns = [
      `user:${userId}:recent_activities`,
      `user:${userId}:preferences`,
      `device:${userId}:*`,
      `session:${userId}:*`
    ];

    for (const pattern of userPatterns) {
      await warmKeyPattern(pattern, priority);
    }

  } catch (error) {
    logger.error("Failed to warm user-specific caches", {
      error: error instanceof Error ? error.message : String(error),
      userId
    });
  }
}

/**
 * Analyze cache usage patterns for optimization
 */
async function analyzeCacheUsagePatterns(correlationId: string): Promise<void> {
  try {
    logger.info("Analyzing cache usage patterns", { correlationId });

    const metrics = redis.getMetrics();
    
    // Log cache performance metrics
    logger.info("Cache performance metrics", {
      correlationId,
      operations: metrics.operations,
      errors: metrics.errors,
      cacheHitRate: metrics.cacheHitRate,
      lastOperation: metrics.lastOperation
    });

    // Store analytics for future optimization
    await storeCacheAnalytics(metrics, correlationId);

  } catch (error) {
    logger.error("Failed to analyze cache usage patterns", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Store cache analytics for future optimization
 */
async function storeCacheAnalytics(metrics: any, correlationId: string): Promise<void> {
  try {
    const analytics = {
      id: `cache-analytics-${Date.now()}`,
      timestamp: new Date().toISOString(),
      metrics,
      correlationId,
      type: 'cache_performance'
    };

    await db.createItem('analytics', analytics);
    logger.debug("Cache analytics stored", { correlationId });

  } catch (error) {
    logger.error("Failed to store cache analytics", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Clean up expired warming jobs
 */
async function cleanupExpiredJobs(correlationId: string): Promise<void> {
  try {
    logger.info("Cleaning up expired warming jobs", { correlationId });
    
    // This would clean up old warming job records
    // Implementation depends on how warming jobs are tracked
    
    logger.debug("Warming job cleanup completed", { correlationId });

  } catch (error) {
    logger.error("Failed to cleanup expired jobs", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Update warming configuration based on analytics
 */
async function updateWarmingConfiguration(correlationId: string): Promise<void> {
  try {
    logger.info("Updating warming configuration", { correlationId });
    
    // This would analyze performance and adjust warming rules
    // For now, we'll just log the current configuration
    
    const stats = eventDrivenCache.getStatistics();
    logger.info("Current warming configuration", { correlationId, stats });

  } catch (error) {
    logger.error("Failed to update warming configuration", {
      correlationId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Register the timer function - runs every 5 minutes
app.timer('cache-warming-scheduler', {
  schedule: '0 */5 * * * *', // Every 5 minutes
  handler: cacheWarmingScheduler
});
