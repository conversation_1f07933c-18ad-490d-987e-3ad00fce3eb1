/**
 * Template Generate Function
 * Handles document generation from templates with variable substitution
 * Migrated from old-arch/src/template-service/generate/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient, BlobSASPermissions } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Output formats
enum OutputFormat {
  PDF = 'pdf',
  DOCX = 'docx',
  HTML = 'html',
  TXT = 'txt'
}

// Validation schema
const generateFromTemplateSchema = Joi.object({
  templateId: Joi.string().uuid().required(),
  variables: Joi.object().required(),
  outputFormat: Joi.string().valid(...Object.values(OutputFormat)).default(OutputFormat.PDF),
  outputName: Joi.string().max(255).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  options: Joi.object({
    includeMetadata: Joi.boolean().default(true),
    watermark: Joi.string().max(100).optional(),
    password: Joi.string().min(6).max(50).optional(),
    quality: Joi.string().valid('low', 'medium', 'high').default('medium'),
    compression: Joi.boolean().default(true)
  }).optional()
});

interface GenerateFromTemplateRequest {
  templateId: string;
  variables: { [key: string]: any };
  outputFormat: OutputFormat;
  outputName?: string;
  organizationId: string;
  projectId?: string;
  options?: {
    includeMetadata?: boolean;
    watermark?: string;
    password?: string;
    quality?: 'low' | 'medium' | 'high';
    compression?: boolean;
  };
}

interface TemplateGenerationResponse {
  documentId: string;
  templateId: string;
  templateName: string;
  outputFormat: OutputFormat;
  fileName: string;
  fileSize: number;
  downloadUrl: string;
  variables: { [key: string]: any };
  metadata: {
    generatedAt: string;
    processingTime: number;
    variableCount: number;
    substitutionCount: number;
  };
  success: boolean;
}

/**
 * Generate document from template handler
 */
export async function generateFromTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Generate from template started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = generateFromTemplateSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const generateRequest: GenerateFromTemplateRequest = value;
    const startTime = Date.now();

    // Get template
    const template = await db.readItem('templates', generateRequest.templateId, generateRequest.templateId);
    if (!template) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Template not found" }
      }, request);
    }

    const templateData = template as any;

    // Check template access permissions
    const hasAccess = await checkTemplateAccess(templateData, user, generateRequest.organizationId);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to template" }
      }, request);
    }

    // Verify organization access
    const organization = await db.readItem('organizations', generateRequest.organizationId, generateRequest.organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check generation limits for organization tier
    const orgData = organization as any;
    if (await isGenerationLimitReached(generateRequest.organizationId, orgData.tier)) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: "Document generation limit reached for this organization tier",
          tier: orgData.tier
        }
      }, request);
    }

    // Validate required variables
    const missingVariables = validateTemplateVariables(templateData, generateRequest.variables);
    if (missingVariables.length > 0) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: "Missing required template variables",
          missingVariables
        }
      }, request);
    }

    // Generate document from template
    const generationResult = await performTemplateGeneration(
      templateData,
      generateRequest.variables,
      generateRequest.outputFormat,
      generateRequest.options || {}
    );

    // Save generated document to blob storage
    const documentId = uuidv4();
    const fileName = generateRequest.outputName || `${templateData.name}_${new Date().toISOString().split('T')[0]}.${generateRequest.outputFormat}`;
    const blobName = `${generateRequest.organizationId}/${generateRequest.projectId || 'templates'}/${documentId}_${fileName}`;

    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
    const containerClient = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || "documents"
    );
    const blobClient = containerClient.getBlockBlobClient(blobName);

    await blobClient.upload(
      generationResult.content,
      generationResult.content.length,
      {
        blobHTTPHeaders: {
          blobContentType: getContentType(generateRequest.outputFormat),
          blobContentDisposition: `attachment; filename="${fileName}"`
        }
      }
    );

    // Create document record
    const generatedDocument = {
      id: documentId,
      name: fileName,
      description: `Generated from template: ${templateData.name}`,
      blobName,
      contentType: getContentType(generateRequest.outputFormat),
      size: generationResult.content.length,
      organizationId: generateRequest.organizationId,
      projectId: generateRequest.projectId,
      templateId: generateRequest.templateId,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      status: "GENERATED",
      metadata: {
        generatedFromTemplate: true,
        templateName: templateData.name,
        templateVersion: templateData.version,
        variables: generateRequest.variables,
        outputFormat: generateRequest.outputFormat,
        processingTime: Date.now() - startTime,
        substitutionCount: generationResult.substitutionCount
      },
      tenantId: user.tenantId
    };

    await db.createItem('documents', generatedDocument);

    // Update template usage count
    const updatedTemplate = {
      ...templateData,
      usageCount: (templateData.usageCount || 0) + 1,
      lastUsedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('templates', updatedTemplate);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "template_document_generated",
      userId: user.id,
      organizationId: generateRequest.organizationId,
      projectId: generateRequest.projectId,
      documentId,
      templateId: generateRequest.templateId,
      timestamp: new Date().toISOString(),
      details: {
        templateName: templateData.name,
        outputFormat: generateRequest.outputFormat,
        fileName,
        fileSize: generationResult.content.length,
        variableCount: Object.keys(generateRequest.variables).length,
        substitutionCount: generationResult.substitutionCount,
        processingTime: Date.now() - startTime
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'DocumentGeneratedFromTemplate',
      aggregateId: documentId,
      aggregateType: 'Document',
      version: 1,
      data: {
        document: generatedDocument,
        template: templateData,
        variables: generateRequest.variables,
        generatedBy: user.id
      },
      userId: user.id,
      organizationId: generateRequest.organizationId,
      tenantId: user.tenantId
    });

    // Send notification
    await notificationService.sendNotification({
      userId: user.id,
      type: 'DOCUMENT_GENERATED',
      title: 'Document generated successfully!',
      message: `Your document "${fileName}" has been generated from template "${templateData.name}".`,
      priority: 'normal',
      metadata: {
        documentId,
        templateId: generateRequest.templateId,
        templateName: templateData.name,
        fileName,
        outputFormat: generateRequest.outputFormat,
        organizationId: generateRequest.organizationId
      },
      organizationId: generateRequest.organizationId,
      projectId: generateRequest.projectId
    });

    // Generate download URL
    const permissions = new BlobSASPermissions();
    permissions.read = true;

    const downloadUrl = await blobClient.generateSasUrl({
      permissions,
      expiresOn: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
    });

    const response: TemplateGenerationResponse = {
      documentId,
      templateId: generateRequest.templateId,
      templateName: templateData.name,
      outputFormat: generateRequest.outputFormat,
      fileName,
      fileSize: generationResult.content.length,
      downloadUrl,
      variables: generateRequest.variables,
      metadata: {
        generatedAt: new Date().toISOString(),
        processingTime: Date.now() - startTime,
        variableCount: Object.keys(generateRequest.variables).length,
        substitutionCount: generationResult.substitutionCount
      },
      success: true
    };

    logger.info("Document generated from template successfully", {
      correlationId,
      documentId,
      templateId: generateRequest.templateId,
      templateName: templateData.name,
      outputFormat: generateRequest.outputFormat,
      fileSize: generationResult.content.length,
      processingTime: Date.now() - startTime,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Generate from template failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check template access permissions
 */
async function checkTemplateAccess(template: any, user: any, organizationId: string): Promise<boolean> {
  // Template is public
  if (template.isPublic) {
    return true;
  }

  // User is the creator
  if (template.createdBy === user.id) {
    return true;
  }

  // Template belongs to user's organization
  if (template.organizationId === organizationId) {
    // Check if user is member of the organization
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'ACTIVE']);
    return memberships.length > 0;
  }

  // Check explicit permissions
  if (template.permissions) {
    return template.permissions.some((p: any) =>
      p.entityType === 'user' && p.entityId === user.id && p.permission === 'use'
    );
  }

  return false;
}

/**
 * Validate template variables
 */
function validateTemplateVariables(template: any, providedVariables: any): string[] {
  const requiredVariables = template.variables?.filter((v: any) => v.required) || [];
  const missingVariables: string[] = [];

  for (const variable of requiredVariables) {
    if (!(variable.name in providedVariables) || providedVariables[variable.name] === null || providedVariables[variable.name] === undefined) {
      missingVariables.push(variable.name);
    }
  }

  return missingVariables;
}

/**
 * Check if generation limit is reached for organization
 */
async function isGenerationLimitReached(organizationId: string, tier: string): Promise<boolean> {
  try {
    // Get current month's generation count
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const generationQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate AND c.metadata.generatedFromTemplate = true';
    const result = await db.queryItems('documents', generationQuery, [organizationId, startOfMonth.toISOString()]);
    const currentCount = Number(result[0]) || 0;

    // Define tier limits
    const limits: { [key: string]: number } = {
      'FREE': 50,
      'PROFESSIONAL': 500,
      'ENTERPRISE': -1 // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];
    return limit > 0 && currentCount >= limit;

  } catch (error) {
    logger.error('Failed to check generation limit', { error, organizationId });
    return false;
  }
}

/**
 * Perform template generation (simplified implementation)
 */
async function performTemplateGeneration(
  template: any,
  variables: any,
  outputFormat: OutputFormat,
  options: any
): Promise<{ content: Buffer; substitutionCount: number }> {
  logger.info("Performing template generation", {
    templateId: template.id,
    templateName: template.name,
    outputFormat,
    variableCount: Object.keys(variables).length
  });

  // Get template content
  let templateContent = template.content || template.body || "";

  // Perform variable substitution
  let substitutionCount = 0;
  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
    const matches = templateContent.match(regex);
    if (matches) {
      substitutionCount += matches.length;
      templateContent = templateContent.replace(regex, String(value));
    }
  }

  // Add metadata if requested
  if (options.includeMetadata) {
    const metadata = `\n\n<!-- Generated from template: ${template.name} on ${new Date().toISOString()} -->`;
    templateContent += metadata;
  }

  // Add watermark if specified
  if (options.watermark) {
    templateContent = `${options.watermark}\n\n${templateContent}`;
  }

  // Convert to specified format
  let content: Buffer;

  switch (outputFormat) {
    case OutputFormat.HTML:
      content = Buffer.from(`<!DOCTYPE html>
<html>
<head>
    <title>${template.name}</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        .document-content { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="document-content">
        ${templateContent.replace(/\n/g, '<br>')}
    </div>
</body>
</html>`, 'utf-8');
      break;

    case OutputFormat.TXT:
      content = Buffer.from(templateContent, 'utf-8');
      break;

    case OutputFormat.PDF:
    case OutputFormat.DOCX:
    default:
      // In production, this would use libraries like puppeteer for PDF or docx for DOCX
      content = Buffer.from(`[${outputFormat.toUpperCase()} Content]\n\n${templateContent}\n\n[Generated from template: ${template.name}]`, 'utf-8');
      break;
  }

  return { content, substitutionCount };
}

/**
 * Get content type for output format
 */
function getContentType(format: OutputFormat): string {
  const contentTypes = {
    [OutputFormat.PDF]: 'application/pdf',
    [OutputFormat.DOCX]: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    [OutputFormat.HTML]: 'text/html',
    [OutputFormat.TXT]: 'text/plain'
  };

  return contentTypes[format] || 'application/octet-stream';
}

// Register functions
app.http('template-generate', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'templates/generate',
  handler: generateFromTemplate
});
