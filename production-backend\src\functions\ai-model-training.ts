/**
 * AI Model Training Function
 * Handles custom AI model training, deployment, and management
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Model types enum
enum ModelType {
  DOCUMENT_CLASSIFIER = 'DOCUMENT_CLASSIFIER',
  ENTITY_EXTRACTOR = 'ENTITY_EXTRACTOR',
  SENTIMENT_ANALYZER = 'SENTIMENT_ANALYZER',
  CONTENT_SUMMARIZER = 'CONTENT_SUMMARIZER',
  LANGUAGE_DETECTOR = 'LANGUAGE_DETECTOR',
  CUSTOM_NLP = 'CUSTOM_NLP',
  PREDICTIVE_ANALYTICS = 'PREDICTIVE_ANALYTICS',
  ANOMALY_DETECTOR = 'ANOMALY_DETECTOR'
}

// Training status enum
enum TrainingStatus {
  PENDING = 'PENDING',
  PREPARING = 'PREPARING',
  TRAINING = 'TRAINING',
  VALIDATING = 'VALIDATING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// Model status enum
enum ModelStatus {
  DRAFT = 'DRAFT',
  TRAINING = 'TRAINING',
  READY = 'READY',
  DEPLOYED = 'DEPLOYED',
  DEPRECATED = 'DEPRECATED',
  FAILED = 'FAILED'
}

// Validation schemas
const createModelSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(ModelType)).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  configuration: Joi.object({
    algorithm: Joi.string().optional(),
    hyperparameters: Joi.object().optional(),
    features: Joi.array().items(Joi.string()).optional(),
    targetVariable: Joi.string().optional(),
    validationSplit: Joi.number().min(0.1).max(0.5).default(0.2)
  }).optional(),
  trainingData: Joi.object({
    datasetId: Joi.string().uuid().optional(),
    documentIds: Joi.array().items(Joi.string().uuid()).optional(),
    externalDataUrl: Joi.string().uri().optional(),
    annotations: Joi.array().items(
      Joi.object({
        documentId: Joi.string().uuid().required(),
        labels: Joi.array().items(Joi.string()).required(),
        entities: Joi.array().items(
          Joi.object({
            text: Joi.string().required(),
            label: Joi.string().required(),
            startIndex: Joi.number().integer().min(0).required(),
            endIndex: Joi.number().integer().min(0).required()
          })
        ).optional()
      })
    ).optional()
  }).required()
});

const trainModelSchema = Joi.object({
  modelId: Joi.string().uuid().required(),
  trainingOptions: Joi.object({
    epochs: Joi.number().integer().min(1).max(1000).default(10),
    batchSize: Joi.number().integer().min(1).max(1000).default(32),
    learningRate: Joi.number().min(0.0001).max(1).default(0.001),
    earlyStopping: Joi.boolean().default(true),
    patience: Joi.number().integer().min(1).max(100).default(5)
  }).optional()
});

const deployModelSchema = Joi.object({
  modelId: Joi.string().uuid().required(),
  deploymentName: Joi.string().required().min(2).max(100),
  environment: Joi.string().valid('development', 'staging', 'production').default('development'),
  scalingConfig: Joi.object({
    minInstances: Joi.number().integer().min(0).max(100).default(1),
    maxInstances: Joi.number().integer().min(1).max(100).default(10),
    targetUtilization: Joi.number().min(0.1).max(1).default(0.7)
  }).optional()
});

/**
 * Create AI model handler
 */
export async function createModel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create AI model started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createModelSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const modelData = value;

    // Check organization access and AI features
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, modelData.organizationId, 'active']);
    
    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check if organization has AI features enabled
    const organization = await db.readItem('organizations', modelData.organizationId, modelData.organizationId);
    if (!organization || !(organization as any).features?.aiModels) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "AI model training not available for this organization" }
      }, request);
    }

    // Validate training data
    const trainingDataValidation = await validateTrainingData(modelData.trainingData, modelData.type, user);
    if (!trainingDataValidation.valid) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Invalid Training Data', 
          message: trainingDataValidation.error 
        }
      }, request);
    }

    // Create model
    const modelId = uuidv4();
    const model = {
      id: modelId,
      name: modelData.name,
      description: modelData.description || "",
      type: modelData.type,
      organizationId: modelData.organizationId,
      projectId: modelData.projectId,
      configuration: modelData.configuration || {},
      trainingData: modelData.trainingData,
      status: ModelStatus.DRAFT,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      version: '1.0.0',
      metrics: {
        accuracy: null,
        precision: null,
        recall: null,
        f1Score: null,
        trainingLoss: null,
        validationLoss: null
      },
      deployments: [],
      trainingHistory: [],
      tenantId: user.tenantId
    };

    await db.createItem('ai-models', model);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "ai_model_created",
      userId: user.id,
      organizationId: modelData.organizationId,
      projectId: modelData.projectId,
      modelId,
      timestamp: new Date().toISOString(),
      details: {
        modelName: model.name,
        modelType: model.type,
        trainingDataSize: trainingDataValidation.dataSize
      },
      tenantId: user.tenantId
    });

    logger.info("AI model created successfully", {
      correlationId,
      modelId,
      userId: user.id,
      organizationId: modelData.organizationId,
      type: modelData.type
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: modelId,
        name: model.name,
        type: model.type,
        status: model.status,
        version: model.version,
        message: "AI model created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create AI model failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Train model handler
 */
export async function trainModel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Train AI model started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = trainModelSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { modelId, trainingOptions } = value;

    // Get model
    const model = await db.readItem('ai-models', modelId, modelId);
    if (!model) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Model not found" }
      }, request);
    }

    // Check permissions
    const hasAccess = (
      (model as any).createdBy === user.id ||
      (model as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to model" }
      }, request);
    }

    // Check if model is in trainable state
    if ((model as any).status !== ModelStatus.DRAFT && (model as any).status !== ModelStatus.FAILED) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Model is not in a trainable state" }
      }, request);
    }

    // Create training job
    const trainingJobId = uuidv4();
    const trainingJob = {
      id: trainingJobId,
      modelId,
      status: TrainingStatus.PENDING,
      options: trainingOptions || {},
      startedAt: new Date().toISOString(),
      startedBy: user.id,
      progress: 0,
      currentEpoch: 0,
      logs: [],
      organizationId: (model as any).organizationId,
      projectId: (model as any).projectId,
      tenantId: user.tenantId
    };

    await db.createItem('ai-training-jobs', trainingJob);

    // Update model status
    const updatedModel = {
      ...(model as any),
      id: modelId,
      status: ModelStatus.TRAINING,
      currentTrainingJobId: trainingJobId,
      updatedBy: user.id,
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('ai-models', updatedModel);

    // Start training process (simplified - in production, this would be async)
    await startModelTraining(trainingJob, model as any);

    logger.info("AI model training started", {
      correlationId,
      modelId,
      trainingJobId,
      userId: user.id,
      organizationId: (model as any).organizationId
    });

    return addCorsHeaders({
      status: 202,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        modelId,
        trainingJobId,
        status: TrainingStatus.PENDING,
        estimatedDuration: estimateTrainingDuration((model as any).type, trainingOptions),
        message: "Model training started"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Train AI model failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Deploy model handler
 */
export async function deployModel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Deploy AI model started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = deployModelSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { modelId, deploymentName, environment, scalingConfig } = value;

    // Get model
    const model = await db.readItem('ai-models', modelId, modelId);
    if (!model) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Model not found" }
      }, request);
    }

    // Check if model is ready for deployment
    if ((model as any).status !== ModelStatus.READY) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Model is not ready for deployment" }
      }, request);
    }

    // Create deployment
    const deploymentId = uuidv4();
    const deployment = {
      id: deploymentId,
      modelId,
      name: deploymentName,
      environment,
      scalingConfig: scalingConfig || {
        minInstances: 1,
        maxInstances: 10,
        targetUtilization: 0.7
      },
      endpoint: `https://api.hepz.com/ai/models/${modelId}/predict`,
      status: 'DEPLOYING',
      deployedBy: user.id,
      deployedAt: new Date().toISOString(),
      organizationId: (model as any).organizationId,
      projectId: (model as any).projectId,
      tenantId: user.tenantId
    };

    await db.createItem('ai-deployments', deployment);

    // Update model with deployment info
    const updatedModel = {
      ...(model as any),
      id: modelId,
      status: ModelStatus.DEPLOYED,
      deployments: [...((model as any).deployments || []), deploymentId],
      updatedBy: user.id,
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('ai-models', updatedModel);

    // Simulate deployment process
    setTimeout(async () => {
      const finalDeployment = {
        ...deployment,
        id: deploymentId,
        status: 'ACTIVE',
        activatedAt: new Date().toISOString()
      };
      await db.updateItem('ai-deployments', finalDeployment);
    }, 5000);

    logger.info("AI model deployment started", {
      correlationId,
      modelId,
      deploymentId,
      userId: user.id,
      environment
    });

    return addCorsHeaders({
      status: 202,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        modelId,
        deploymentId,
        deploymentName,
        environment,
        endpoint: deployment.endpoint,
        status: 'DEPLOYING',
        message: "Model deployment started"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Deploy AI model failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Validate training data
 */
async function validateTrainingData(trainingData: any, modelType: string, user: any): Promise<any> {
  try {
    let dataSize = 0;

    if (trainingData.documentIds && trainingData.documentIds.length > 0) {
      // Validate document access
      for (const docId of trainingData.documentIds) {
        const doc = await db.readItem('documents', docId, docId);
        if (!doc) {
          return { valid: false, error: `Document not found: ${docId}` };
        }
        dataSize++;
      }
    }

    if (trainingData.annotations && trainingData.annotations.length > 0) {
      dataSize += trainingData.annotations.length;
    }

    // Check minimum data requirements
    const minDataRequirements: { [key: string]: number } = {
      [ModelType.DOCUMENT_CLASSIFIER]: 50,
      [ModelType.ENTITY_EXTRACTOR]: 100,
      [ModelType.SENTIMENT_ANALYZER]: 200,
      [ModelType.CONTENT_SUMMARIZER]: 500
    };

    const minRequired = minDataRequirements[modelType] || 10;
    if (dataSize < minRequired) {
      return { 
        valid: false, 
        error: `Insufficient training data. Minimum ${minRequired} samples required, got ${dataSize}` 
      };
    }

    return { valid: true, dataSize };
  } catch (error) {
    return { 
      valid: false, 
      error: error instanceof Error ? error.message : String(error) 
    };
  }
}

/**
 * Start model training (simplified)
 */
async function startModelTraining(trainingJob: any, model: any): Promise<void> {
  // In production, this would integrate with ML training infrastructure
  logger.info("Starting model training simulation", {
    trainingJobId: trainingJob.id,
    modelId: model.id,
    modelType: model.type
  });

  // Simulate training progress
  setTimeout(async () => {
    try {
      // Update training job to completed
      const completedJob = {
        ...trainingJob,
        id: trainingJob.id,
        status: TrainingStatus.COMPLETED,
        progress: 100,
        completedAt: new Date().toISOString(),
        metrics: {
          accuracy: 0.85 + Math.random() * 0.1,
          precision: 0.82 + Math.random() * 0.1,
          recall: 0.78 + Math.random() * 0.1,
          f1Score: 0.80 + Math.random() * 0.1,
          trainingLoss: 0.15 + Math.random() * 0.1,
          validationLoss: 0.18 + Math.random() * 0.1
        }
      };
      await db.updateItem('ai-training-jobs', completedJob);

      // Update model to ready
      const readyModel = {
        ...model,
        id: model.id,
        status: ModelStatus.READY,
        metrics: completedJob.metrics,
        trainedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      await db.updateItem('ai-models', readyModel);

      logger.info("Model training completed", {
        trainingJobId: trainingJob.id,
        modelId: model.id,
        metrics: completedJob.metrics
      });

    } catch (error) {
      logger.error("Model training failed", {
        trainingJobId: trainingJob.id,
        modelId: model.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }, 30000); // 30 seconds simulation
}

/**
 * Estimate training duration
 */
function estimateTrainingDuration(modelType: string, options: any): string {
  const baseDurations: { [key: string]: number } = {
    [ModelType.DOCUMENT_CLASSIFIER]: 10,
    [ModelType.ENTITY_EXTRACTOR]: 15,
    [ModelType.SENTIMENT_ANALYZER]: 8,
    [ModelType.CONTENT_SUMMARIZER]: 25
  };

  const baseMinutes = baseDurations[modelType] || 10;
  const epochs = options?.epochs || 10;
  const estimatedMinutes = Math.ceil(baseMinutes * (epochs / 10));

  return `${estimatedMinutes} minutes`;
}

// Register functions
app.http('ai-model-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/models',
  handler: createModel
});

app.http('ai-model-train', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/models/{modelId}/train',
  handler: trainModel
});

app.http('ai-model-deploy', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/models/{modelId}/deploy',
  handler: deployModel
});
