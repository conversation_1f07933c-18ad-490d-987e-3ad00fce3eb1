/**
 * Document Content Completion Function
 * Handles AI-powered content completion for documents
 * Migrated from old-arch/src/document-service/complete-content/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Content completion types
enum CompletionType {
  CONTINUE = 'continue',
  EXPAND = 'expand',
  SUMMARIZE = 'summarize',
  REWRITE = 'rewrite'
}

enum ToneType {
  PROFESSIONAL = 'professional',
  CASUAL = 'casual',
  ACADEMIC = 'academic',
  CREATIVE = 'creative'
}

// Validation schema
const contentCompletionSchema = Joi.object({
  prompt: Joi.string().max(1000).optional(),
  context: Joi.string().max(5000).optional(),
  completionType: Joi.string().valid(...Object.values(CompletionType)).required(),
  maxLength: Joi.number().min(100).max(5000).optional(),
  tone: Joi.string().valid(...Object.values(ToneType)).optional(),
  preserveStyle: Joi.boolean().optional()
});

interface ContentCompletionRequest {
  prompt?: string;
  context?: string;
  completionType: CompletionType;
  maxLength?: number;
  tone?: ToneType;
  preserveStyle?: boolean;
}

interface ContentCompletionResponse {
  documentId: string;
  originalContent: string;
  completedContent: string;
  suggestions: string[];
  metadata: {
    completionType: CompletionType;
    wordsAdded: number;
    processingTime: number;
    confidence: number;
  };
  success: boolean;
}

/**
 * Complete document content handler
 */
export async function completeDocumentContent(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const documentId = request.params.id;
  
  logger.info("Document content completion started", { correlationId, documentId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!documentId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document ID is required" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = contentCompletionSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const completionRequest: ContentCompletionRequest = value;
    const startTime = Date.now();

    // Get document
    const document = await db.readItem('documents', documentId, documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Get document content from blob storage or database
    let originalContent = (document as any).extractedText || (document as any).content || "";

    // If no content in database, try to get from blob storage
    if (!originalContent) {
      try {
        const blobServiceClient = new BlobServiceClient(
          process.env.AZURE_STORAGE_CONNECTION_STRING || ""
        );
        const containerClient = blobServiceClient.getContainerClient(
          process.env.DOCUMENT_CONTAINER || "documents"
        );
        const blobClient = containerClient.getBlobClient((document as any).blobName);

        const downloadResponse = await blobClient.download();
        if (downloadResponse.readableStreamBody) {
          const chunks: Buffer[] = [];
          for await (const chunk of downloadResponse.readableStreamBody) {
            chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
          }
          originalContent = Buffer.concat(chunks).toString('utf-8');
        }
      } catch (error) {
        logger.error("Error downloading document content", { error, documentId });
        return addCorsHeaders({
          status: 500,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Failed to retrieve document content" }
        }, request);
      }
    }

    if (!originalContent.trim()) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document has no content to complete" }
      }, request);
    }

    // Perform AI content completion
    const completionResult = await performContentCompletion(
      originalContent,
      completionRequest
    );

    const processingTime = Date.now() - startTime;

    // Create response
    const response: ContentCompletionResponse = {
      documentId,
      originalContent,
      completedContent: completionResult.completedContent,
      suggestions: completionResult.suggestions || [],
      metadata: {
        completionType: completionRequest.completionType,
        wordsAdded: completionResult.wordsAdded || 0,
        processingTime,
        confidence: completionResult.confidence || 0.8
      },
      success: true
    };

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_content_completed",
      userId: user.id,
      organizationId: (document as any).organizationId,
      projectId: (document as any).projectId,
      documentId,
      timestamp: new Date().toISOString(),
      details: {
        completionType: completionRequest.completionType,
        wordsAdded: response.metadata.wordsAdded,
        processingTime,
        tone: completionRequest.tone
      },
      tenantId: user.tenantId
    });

    logger.info("Document content completion completed successfully", {
      correlationId,
      documentId,
      completionType: completionRequest.completionType,
      wordsAdded: response.metadata.wordsAdded,
      processingTime,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Document content completion failed", {
      correlationId,
      documentId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Perform AI content completion (simplified implementation)
 */
async function performContentCompletion(
  originalContent: string,
  request: ContentCompletionRequest
): Promise<{
  completedContent: string;
  suggestions: string[];
  wordsAdded: number;
  confidence: number;
}> {
  // This is a simplified implementation
  // In production, this would integrate with AI services like OpenAI, Azure OpenAI, or DeepSeek
  
  logger.info("Performing content completion", {
    completionType: request.completionType,
    contentLength: originalContent.length,
    tone: request.tone
  });

  const originalWordCount = originalContent.split(/\s+/).length;
  let completedContent = originalContent;
  let suggestions: string[] = [];
  let wordsAdded = 0;

  switch (request.completionType) {
    case CompletionType.CONTINUE:
      const continuation = generateContinuation(originalContent, request);
      completedContent = originalContent + "\n\n" + continuation;
      wordsAdded = continuation.split(/\s+/).length;
      suggestions = [
        "Consider adding more specific examples",
        "You might want to elaborate on key points",
        "Adding supporting evidence could strengthen the content"
      ];
      break;

    case CompletionType.EXPAND:
      completedContent = generateExpansion(originalContent, request);
      wordsAdded = completedContent.split(/\s+/).length - originalWordCount;
      suggestions = [
        "Additional details have been added to clarify concepts",
        "Examples and explanations have been expanded",
        "Consider reviewing the expanded sections for accuracy"
      ];
      break;

    case CompletionType.SUMMARIZE:
      completedContent = generateSummary(originalContent, request);
      wordsAdded = 0; // Summary typically reduces content
      suggestions = [
        "Key points have been condensed",
        "Main themes are highlighted",
        "Consider if any important details were omitted"
      ];
      break;

    case CompletionType.REWRITE:
      completedContent = generateRewrite(originalContent, request);
      wordsAdded = completedContent.split(/\s+/).length - originalWordCount;
      suggestions = [
        "Content has been restructured for better flow",
        "Language has been enhanced for clarity",
        "Review the rewritten content for tone consistency"
      ];
      break;
  }

  return {
    completedContent,
    suggestions,
    wordsAdded: Math.max(0, wordsAdded),
    confidence: 0.85 // Simulated confidence score
  };
}

/**
 * Generate content continuation
 */
function generateContinuation(content: string, request: ContentCompletionRequest): string {
  // Simplified implementation - in production, use AI service
  const tone = request.tone || ToneType.PROFESSIONAL;
  const prompt = request.prompt || "";
  
  return `[AI-Generated Continuation - ${tone} tone]

${prompt ? `Following the guidance: "${prompt}"` : "Continuing the document naturally..."}

This section would contain AI-generated content that:
- Maintains the existing writing style and tone
- Flows naturally from the previous content
- Adds meaningful value to the document
- Stays relevant to the established topic
- Uses appropriate ${tone} language

In a production environment, this would be generated by an advanced AI model that understands context, maintains consistency, and produces high-quality content.`;
}

/**
 * Generate content expansion
 */
function generateExpansion(content: string, request: ContentCompletionRequest): string {
  // Simplified implementation - in production, use AI service
  const expandedSections = content.split('\n\n').map(paragraph => {
    if (paragraph.trim()) {
      return paragraph + "\n\n[Expanded details and examples would be added here by AI]";
    }
    return paragraph;
  });
  
  return expandedSections.join('\n\n');
}

/**
 * Generate content summary
 */
function generateSummary(content: string, request: ContentCompletionRequest): string {
  // Simplified implementation - in production, use AI service
  const wordCount = content.split(/\s+/).length;
  const targetLength = Math.min(wordCount * 0.3, request.maxLength || 500);
  
  return `[AI-Generated Summary - ${request.tone || 'professional'} tone]

This summary captures the key points from the original document:

• Main themes and concepts have been identified
• Important details and facts are preserved
• The content is organized in a clear structure
• The tone remains ${request.tone || 'professional'}

In production, this would be an intelligent summary generated by AI that maintains the essential meaning while significantly reducing length.

Original length: ${wordCount} words
Summary target: ~${Math.round(targetLength)} words`;
}

/**
 * Generate content rewrite
 */
function generateRewrite(content: string, request: ContentCompletionRequest): string {
  // Simplified implementation - in production, use AI service
  return `[AI-Rewritten Content - Enhanced for ${request.tone || 'professional'} tone]

${request.prompt ? `Rewrite guidance applied: "${request.prompt}"` : ""}

This represents a rewritten version of the original content that:
- Improves clarity and readability
- Enhances structure and logical flow
- Maintains the original meaning and intent
- Uses more engaging and appropriate language
- Applies consistent ${request.tone || 'professional'} tone throughout

The rewritten content would preserve all key information while presenting it in a more effective and polished manner.`;
}

// Register functions
app.http('document-complete-content', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{id}/complete-content',
  handler: completeDocumentContent
});
