/**
 * Azure Notification Hubs Integration
 * Handles push notifications through Azure Notification Hubs
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { NotificationHubsClient, createAppleNotification, createFcmV1Notification } from '@azure/notification-hubs';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { publishEvent, EventType } from './event-grid-handlers';

// Notification Hub client
let notificationHubClient: NotificationHubsClient | null = null;

/**
 * Initialize Notification Hub client
 */
function getNotificationHubClient(): NotificationHubsClient {
  if (!notificationHubClient) {
    const connectionString = process.env.NOTIFICATION_HUB_CONNECTION_STRING;
    const hubName = process.env.NOTIFICATION_HUB_NAME;

    if (!connectionString || !hubName) {
      throw new Error('Notification Hub configuration missing');
    }

    notificationHubClient = new NotificationHubsClient(connectionString, hubName);
  }
  return notificationHubClient;
}

/**
 * Send push notification
 */
async function sendPushNotification(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const body = await request.json() as any;
    const {
      platform,
      title,
      message,
      data = {},
      tags = [],
      userId,
      deviceToken,
      badge,
      sound = 'default',
      priority = 'normal'
    } = body;

    if (!platform || !title || !message) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Platform, title, and message are required' }
      }, request);
    }

    const client = getNotificationHubClient();
    let notification;
    let result;

    // Create platform-specific notification
    switch (platform.toLowerCase()) {
      case 'ios':
      case 'apple':
        notification = createAppleNotification({
          body: {
            aps: {
              alert: {
                title: title,
                body: message
              },
              badge: badge || 1,
              sound: sound
            },
            data: data
          }
        });
        break;

      case 'android':
      case 'fcm':
        notification = createFcmV1Notification({
          body: {
            notification: {
              title: title,
              body: message
            },
            data: data,
            android: {
              priority: priority === 'high' ? 'high' : 'normal'
            }
          }
        });
        break;

      default:
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: 'Unsupported platform. Use "ios" or "android"' }
        }, request);
    }

    // Send notification
    let targetTags: string[] = [];
    if (userId) {
      targetTags = [`userId:${userId}`];
    } else if (deviceToken) {
      targetTags = [`deviceToken:${deviceToken}`];
    } else if (tags.length > 0) {
      targetTags = tags;
    }

    if (targetTags.length > 0) {
      result = await client.sendNotification(notification, { tagExpression: targetTags.join(' || ') });
    } else {
      // Broadcast to all - use empty tag expression for broadcast
      result = await client.sendNotification(notification, { tagExpression: "" });
    }

    // Log notification
    const notificationLog = {
      id: `push-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      platform,
      title,
      message,
      data,
      userId,
      deviceToken,
      tags,
      sentBy: authResult.user?.id,
      sentAt: new Date().toISOString(),
      notificationId: result.notificationId,
      trackingId: result.trackingId,
      status: 'sent'
    };

    await db.createItem('push-notifications', notificationLog);

    // Publish notification sent event
    await publishEvent(
      EventType.NOTIFICATION_SENT,
      `push-notifications/${notificationLog.id}`,
      {
        notificationId: notificationLog.id,
        platform,
        userId,
        title,
        hubNotificationId: result.notificationId,
        timestamp: new Date().toISOString()
      }
    );

    logger.info('Push notification sent successfully', {
      platform,
      userId,
      deviceToken,
      notificationId: result.notificationId,
      trackingId: result.trackingId
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        message: 'Push notification sent successfully',
        notificationId: result.notificationId,
        trackingId: result.trackingId,
        logId: notificationLog.id
      }
    }, request);

  } catch (error) {
    logger.error('Failed to send push notification', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to send push notification' }
    }, request);
  }
}

/**
 * Register device for push notifications
 */
async function registerDevice(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const body = await request.json() as any;
    const {
      platform,
      deviceToken,
      tags = [],
      userId
    } = body;

    if (!platform || !deviceToken) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Platform and deviceToken are required' }
      }, request);
    }

    const client = getNotificationHubClient();

    // Prepare registration tags
    const registrationTags = [
      `platform:${platform}`,
      `deviceToken:${deviceToken}`,
      ...tags
    ];

    if (userId) {
      registrationTags.push(`userId:${userId}`);
    }

    // Create or update registration using the generic createRegistration method
    let registration;
    switch (platform.toLowerCase()) {
      case 'ios':
      case 'apple':
        registration = await client.createRegistration({
          type: "Apple",
          deviceToken: deviceToken,
          tags: registrationTags
        } as any);
        break;

      case 'android':
      case 'fcm':
        registration = await client.createRegistration({
          type: "Fcm",
          fcmRegistrationId: deviceToken,
          tags: registrationTags
        } as any);
        break;

      default:
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: 'Unsupported platform. Use "ios" or "android"' }
        }, request);
    }

    // Store device registration
    const deviceRegistration = {
      id: `device-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      userId: userId || null,
      platform,
      deviceToken,
      tags: registrationTags,
      registrationId: registration.registrationId,
      registeredBy: authResult.user?.id,
      registeredAt: new Date().toISOString(),
      status: 'active'
    };

    await db.createItem('device-registrations', deviceRegistration);

    logger.info('Device registered for push notifications', {
      platform,
      userId,
      deviceToken: deviceToken.substring(0, 10) + '...',
      registrationId: registration.registrationId
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        message: 'Device registered successfully',
        registrationId: registration.registrationId,
        deviceId: deviceRegistration.id
      }
    }, request);

  } catch (error) {
    logger.error('Failed to register device', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to register device' }
    }, request);
  }
}

/**
 * Unregister device from push notifications
 */
async function unregisterDevice(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const body = await request.json() as any;
    const { registrationId, deviceToken } = body;

    if (!registrationId && !deviceToken) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Either registrationId or deviceToken is required' }
      }, request);
    }

    const client = getNotificationHubClient();

    if (registrationId) {
      // Delete by registration ID
      await client.deleteRegistration(registrationId);
    } else {
      // Find and delete by device token
      const registrations = client.listRegistrationsByTag(`deviceToken:${deviceToken}`);
      for await (const registration of registrations) {
        if (registration.registrationId) {
          await client.deleteRegistration(registration.registrationId);
        }
      }
    }

    // Update device registration status in database
    const query = registrationId
      ? 'SELECT * FROM c WHERE c.registrationId = @registrationId'
      : 'SELECT * FROM c WHERE c.deviceToken = @deviceToken';

    const parameters = registrationId
      ? [{ name: '@registrationId', value: registrationId }]
      : [{ name: '@deviceToken', value: deviceToken }];

    const deviceRegistrations = await db.queryItems<any>('device-registrations',
      query,
      parameters
    );

    for (const deviceReg of deviceRegistrations) {
      await db.updateItem('device-registrations', {
        ...deviceReg,
        status: 'unregistered',
        unregisteredAt: new Date().toISOString(),
        unregisteredBy: authResult.user?.id
      });
    }

    logger.info('Device unregistered from push notifications', {
      registrationId,
      deviceToken: deviceToken ? deviceToken.substring(0, 10) + '...' : undefined
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        message: 'Device unregistered successfully'
      }
    }, request);

  } catch (error) {
    logger.error('Failed to unregister device', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to unregister device' }
    }, request);
  }
}

/**
 * Get notification statistics
 */
async function getNotificationStats(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const url = new URL(request.url);
    const period = url.searchParams.get('period') || '24h';

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case '1h':
        startDate.setHours(startDate.getHours() - 1);
        break;
      case '24h':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      default:
        startDate.setDate(startDate.getDate() - 1);
    }

    // Get notification statistics
    const stats = await db.queryItems<any>('push-notifications', `
        SELECT
          c.platform,
          COUNT(1) as count,
          c.status
        FROM c
        WHERE c.sentAt >= @startDate AND c.sentAt < @endDate
        GROUP BY c.platform, c.status
      `,
      [startDate.toISOString(), endDate.toISOString()]
    );

    // Get device registration statistics
    const deviceStats = await db.queryItems<any>('device-registrations', `
        SELECT
          c.platform,
          COUNT(1) as count,
          c.status
        FROM c
        GROUP BY c.platform, c.status
      `,
      []
    );

    const response = {
      period,
      dateRange: {
        start: startDate.toISOString(),
        end: endDate.toISOString()
      },
      notifications: stats,
      devices: deviceStats
    };

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    logger.error('Failed to get notification statistics', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to get notification statistics' }
    }, request);
  }
}

// Register functions
app.http('push-notification-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/push/send',
  handler: sendPushNotification
});

app.http('push-notification-register', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/push/register',
  handler: registerDevice
});

app.http('push-notification-unregister', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/push/unregister',
  handler: unregisterDevice
});

app.http('push-notification-stats', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/push/stats',
  handler: getNotificationStats
});
