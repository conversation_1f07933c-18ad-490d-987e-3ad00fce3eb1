/**
 * Enhanced Event Grid Integration Service
 * Provides comprehensive Event Grid integration capabilities with production-ready features:
 * - Advanced filtering and routing
 * - Event batching and throttling
 * - Dead letter handling
 * - Custom retry policies
 * - Event validation and schema registry
 * - Performance monitoring
 * - Multi-topic publishing
 * - Event deduplication
 */

import { EventGridPublisherClient, EventGridEvent } from '@azure/eventgrid';
import { AzureKeyCredential } from '@azure/core-auth';
import { logger } from '../utils/logger';
import { redis } from './redis';
import { db } from './database';

export interface EventGridConfig {
  endpoint: string;
  accessKey: string;
  retryAttempts?: number;
  timeoutMs?: number;
  batchSize?: number;
  throttleMs?: number;
}

export interface CustomEventData {
  eventType: string;
  subject: string;
  data: any;
  dataVersion?: string;
  eventTime?: Date;
  id?: string;
  topic?: string;
  filters?: EventFilter[];
}

export interface EventSubscription {
  name: string;
  eventTypes: string[];
  endpoint: string;
  filters?: EventFilter[];
}

export interface EventFilter {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn';
  value: string | string[];
}

export interface EventSchema {
  eventType: string;
  version: string;
  schema: any; // JSON schema
  required: string[];
}

export interface EventMetrics {
  totalEvents: number;
  successfulEvents: number;
  failedEvents: number;
  averageProcessingTime: number;
  lastEventTime?: Date;
  eventsFiltered: number;
  eventsDeadLettered: number;
  eventsBatched: number;
  throttleCount: number;
}

export interface RetryPolicy {
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff: boolean;
  maxRetryDelay: number;
}

/**
 * Event Grid Integration Service Class
 */
export class EventGridIntegrationService {
  private clients: Map<string, EventGridPublisherClient<any>> = new Map();
  private config: EventGridConfig;
  private metrics: EventMetrics;
  private eventQueue: CustomEventData[] = [];
  private schemas: Map<string, EventSchema> = new Map();
  private isProcessing = false;
  private lastBatchTime = 0;
  private defaultRetryPolicy: RetryPolicy = {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
    maxRetryDelay: 30000
  };

  constructor(config: EventGridConfig) {
    this.config = {
      retryAttempts: 3,
      timeoutMs: 30000,
      batchSize: 10,
      throttleMs: 1000,
      ...config
    };

    this.metrics = {
      totalEvents: 0,
      successfulEvents: 0,
      failedEvents: 0,
      averageProcessingTime: 0,
      eventsFiltered: 0,
      eventsDeadLettered: 0,
      eventsBatched: 0,
      throttleCount: 0
    };

    this.initializeClient();
    this.startBatchProcessing();
    this.startPeriodicTasks();
  }

  /**
   * Initialize Event Grid client
   */
  private initializeClient(): void {
    try {
      // Initialize default client
      const defaultClient = new EventGridPublisherClient(
        this.config.endpoint,
        "EventGrid",
        new AzureKeyCredential(this.config.accessKey)
      );

      this.clients.set('default', defaultClient);

      logger.info('Event Grid client initialized successfully', {
        endpoint: this.config.endpoint
      });
    } catch (error) {
      logger.error('Failed to initialize Event Grid client', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Register event schema for validation
   */
  public registerEventSchema(schema: EventSchema): void {
    this.schemas.set(schema.eventType, schema);
    logger.info('Event schema registered', {
      eventType: schema.eventType,
      version: schema.version
    });
  }

  /**
   * Queue event for batch processing
   */
  public async queueEvent(eventData: CustomEventData): Promise<void> {
    // Validate event
    if (!await this.validateEventData(eventData)) {
      this.metrics.eventsFiltered++;
      return;
    }

    // Apply filters
    if (eventData.filters && !this.applyFilters(eventData, eventData.filters)) {
      this.metrics.eventsFiltered++;
      return;
    }

    // Add to queue
    this.eventQueue.push({
      ...eventData,
      id: eventData.id || this.generateEventId(),
      eventTime: eventData.eventTime || new Date()
    });

    logger.debug('Event queued for batch processing', {
      eventType: eventData.eventType,
      queueSize: this.eventQueue.length
    });

    // Trigger batch processing if queue is full
    if (this.eventQueue.length >= this.config.batchSize!) {
      await this.processBatch();
    }
  }

  /**
   * Publish a single event with enhanced features
   */
  async publishEvent(eventData: CustomEventData, retryPolicy?: RetryPolicy): Promise<string | null> {
    const startTime = Date.now();
    const eventId = eventData.id || this.generateEventId();

    try {
      // Validate event against schema
      if (!await this.validateEventData(eventData)) {
        logger.warn('Event validation failed', { eventType: eventData.eventType, eventId });
        this.metrics.eventsFiltered++;
        return null;
      }

      // Apply filters
      if (eventData.filters && !this.applyFilters(eventData, eventData.filters)) {
        logger.debug('Event filtered out', { eventType: eventData.eventType, eventId });
        this.metrics.eventsFiltered++;
        return null;
      }

      // Check for duplicate
      if (await this.isDuplicateEvent(eventId)) {
        logger.info('Duplicate event detected, skipping', { eventId });
        return eventId;
      }

      const policy = retryPolicy || this.defaultRetryPolicy;
      let attempt = 0;

      while (attempt <= policy.maxRetries) {
        try {
          const client = await this.getClient(
            eventData.topic ? this.getTopicEndpoint(eventData.topic) : this.config.endpoint,
            this.config.accessKey
          );

          const event: EventGridEvent<any> = {
            id: eventId,
            eventType: eventData.eventType,
            subject: eventData.subject,
            eventTime: eventData.eventTime || new Date(),
            data: eventData.data,
            dataVersion: eventData.dataVersion || '1.0'
          };

          await client.send([event]);

          // Mark as published
          await this.markEventAsPublished(eventId);

          const processingTime = Date.now() - startTime;
          this.updateMetrics(true, processingTime);

          logger.info('Event published successfully', {
            eventId,
            eventType: eventData.eventType,
            subject: eventData.subject,
            attempt,
            processingTime
          });

          return eventId;
        } catch (error) {
          attempt++;
          this.metrics.failedEvents++;

          logger.error('Error publishing event', {
            eventType: eventData.eventType,
            eventId,
            attempt,
            error: error instanceof Error ? error.message : String(error)
          });

          if (attempt <= policy.maxRetries) {
            const delay = this.calculateRetryDelay(attempt, policy);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // Send to dead letter if all retries failed
      await this.sendToDeadLetter(eventData, 'max_retries_exceeded');
      return null;
    } catch (error) {
      this.metrics.failedEvents++;
      logger.error('Unexpected error in publishEvent', {
        eventType: eventData.eventType,
        eventId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Publish multiple events in batch
   */
  async publishEvents(events: CustomEventData[]): Promise<string[]> {
    const startTime = Date.now();
    const eventIds: string[] = [];

    try {
      const client = this.clients.get('default');
      if (!client) {
        throw new Error('Event Grid client not initialized');
      }

      const eventGridEvents: EventGridEvent<any>[] = events.map(eventData => {
        const eventId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        eventIds.push(eventId);

        return {
          id: eventId,
          eventType: eventData.eventType,
          subject: eventData.subject,
          eventTime: eventData.eventTime || new Date(),
          data: eventData.data,
          dataVersion: eventData.dataVersion || '1.0'
        };
      });

      await client.send(eventGridEvents);

      const processingTime = Date.now() - startTime;
      this.updateMetrics(true, processingTime, events.length);

      logger.info('Batch events published successfully', {
        eventCount: events.length,
        eventIds,
        processingTime
      });

      return eventIds;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.updateMetrics(false, processingTime, events.length);

      logger.error('Failed to publish batch events', {
        eventCount: events.length,
        error: error instanceof Error ? error.message : String(error),
        processingTime
      });

      throw error;
    }
  }

  /**
   * Publish event with retry logic
   */
  async publishEventWithRetry(eventData: CustomEventData): Promise<string | null> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retryAttempts!; attempt++) {
      try {
        const result = await this.publishEvent(eventData);
        if (result) {
          return result;
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < this.config.retryAttempts!) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
          logger.warn(`Event publish attempt ${attempt} failed, retrying in ${delay}ms`, {
            eventType: eventData.eventType,
            subject: eventData.subject,
            error: lastError.message
          });

          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    if (lastError) {
      throw lastError;
    }
    return null;
  }

  /**
   * Enhanced event validation with schema support
   */
  async validateEventData(eventData: CustomEventData): Promise<boolean> {
    if (!eventData) {
      logger.warn('Event validation failed - eventData is null or undefined');
      return false;
    }

    const requiredFields = ['eventType', 'subject', 'data'];

    for (const field of requiredFields) {
      const value = eventData[field as keyof CustomEventData];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        logger.warn('Event validation failed - missing or empty field', {
          field,
          eventType: eventData.eventType,
          subject: eventData.subject
        });
        return false;
      }
    }

    // Validate event type format
    if (!/^[A-Za-z0-9]+\.[A-Za-z0-9]+$/.test(eventData.eventType)) {
      logger.warn('Event validation failed - invalid eventType format', {
        eventType: eventData.eventType,
        expectedFormat: 'Category.Action (e.g., Document.Uploaded)'
      });
      return false;
    }

    // Validate data is an object
    if (typeof eventData.data !== 'object' || eventData.data === null) {
      logger.warn('Event validation failed - data must be an object', {
        eventType: eventData.eventType,
        subject: eventData.subject,
        dataType: typeof eventData.data
      });
      return false;
    }

    // Schema validation
    const schema = this.schemas.get(eventData.eventType);
    if (schema) {
      try {
        for (const field of schema.required) {
          if (!eventData.data || !(field in eventData.data)) {
            logger.warn('Event validation failed - missing required schema field', {
              field,
              eventType: eventData.eventType,
              schema: schema.version
            });
            return false;
          }
        }
      } catch (error) {
        logger.warn('Event validation failed - schema validation error', {
          eventType: eventData.eventType,
          error: error instanceof Error ? error.message : String(error)
        });
        return false;
      }
    }

    return true;
  }

  /**
   * Apply event filters
   */
  private applyFilters(eventData: CustomEventData, filters: EventFilter[]): boolean {
    return filters.every(filter => this.evaluateFilter(eventData, filter));
  }

  /**
   * Evaluate a single filter
   */
  private evaluateFilter(eventData: CustomEventData, filter: EventFilter): boolean {
    // Get the field value from the event data
    const fieldValue = this.getFieldValue(eventData, filter.field);

    if (fieldValue === undefined || fieldValue === null) {
      return false;
    }

    const filterValue = filter.value;
    const fieldStr = String(fieldValue).toLowerCase();

    switch (filter.operator) {
      case 'equals':
        return fieldStr === String(filterValue).toLowerCase();
      case 'contains':
        return fieldStr.includes(String(filterValue).toLowerCase());
      case 'startsWith':
        return fieldStr.startsWith(String(filterValue).toLowerCase());
      case 'endsWith':
        return fieldStr.endsWith(String(filterValue).toLowerCase());
      case 'in':
        return Array.isArray(filterValue) &&
               filterValue.some(v => String(v).toLowerCase() === fieldStr);
      case 'notIn':
        return Array.isArray(filterValue) &&
               !filterValue.some(v => String(v).toLowerCase() === fieldStr);
      default:
        return true;
    }
  }

  /**
   * Get field value from event data using dot notation
   */
  private getFieldValue(eventData: CustomEventData, fieldPath: string): any {
    const parts = fieldPath.split('.');
    let value: any = eventData;

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Update metrics
   */
  private updateMetrics(success: boolean, processingTime: number, eventCount: number = 1): void {
    this.metrics.totalEvents += eventCount;

    if (success) {
      this.metrics.successfulEvents += eventCount;
    } else {
      this.metrics.failedEvents += eventCount;
    }

    // Update average processing time
    const totalProcessingTime = this.metrics.averageProcessingTime * (this.metrics.totalEvents - eventCount) + processingTime;
    this.metrics.averageProcessingTime = totalProcessingTime / this.metrics.totalEvents;

    this.metrics.lastEventTime = new Date();
  }

  /**
   * Get current metrics
   */
  getMetrics(): EventMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalEvents: 0,
      successfulEvents: 0,
      failedEvents: 0,
      averageProcessingTime: 0,
      eventsFiltered: 0,
      eventsDeadLettered: 0,
      eventsBatched: 0,
      throttleCount: 0
    };
  }

  // Enhanced helper methods
  private generateEventId(): string {
    return `evt-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }

  private async getClient(endpoint: string, accessKey: string): Promise<EventGridPublisherClient<any>> {
    const clientKey = `${endpoint}:${accessKey}`;

    if (!this.clients.has(clientKey)) {
      const client = new EventGridPublisherClient(endpoint, "EventGrid", new AzureKeyCredential(accessKey));
      this.clients.set(clientKey, client);
    }

    return this.clients.get(clientKey)!;
  }

  private getTopicEndpoint(topic: string): string {
    return this.config.endpoint; // Simplified - could be enhanced to support multiple topics
  }

  private async isDuplicateEvent(eventId: string): Promise<boolean> {
    const key = `eventgrid:published:${eventId}`;
    return await redis.exists(key);
  }

  private async markEventAsPublished(eventId: string): Promise<void> {
    const key = `eventgrid:published:${eventId}`;
    await redis.setex(key, 3600, 'published');
  }

  private calculateRetryDelay(attempt: number, policy: RetryPolicy): number {
    if (!policy.exponentialBackoff) {
      return policy.retryDelay;
    }

    const delay = policy.retryDelay * Math.pow(2, attempt - 1);
    return Math.min(delay, policy.maxRetryDelay);
  }

  private async sendToDeadLetter(eventData: CustomEventData, reason: string): Promise<void> {
    try {
      await db.createItem('dead-letter-events', {
        id: `dl-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        originalEventId: eventData.id,
        eventType: eventData.eventType,
        subject: eventData.subject,
        data: eventData.data,
        reason,
        timestamp: new Date().toISOString()
      });

      this.metrics.eventsDeadLettered++;
    } catch (error) {
      logger.error('Failed to send event to dead letter', {
        eventType: eventData.eventType,
        eventId: eventData.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private startBatchProcessing(): void {
    setInterval(async () => {
      if (this.eventQueue.length > 0 && !this.isProcessing) {
        const now = Date.now();
        if (now - this.lastBatchTime >= this.config.throttleMs!) {
          await this.processBatch();
        }
      }
    }, this.config.throttleMs! / 2);
  }

  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    this.lastBatchTime = Date.now();

    try {
      const batchSize = Math.min(this.eventQueue.length, this.config.batchSize!);
      const batch = this.eventQueue.splice(0, batchSize);

      if (batch.length > 0) {
        await this.publishEvents(batch);
        this.metrics.eventsBatched += batch.length;
      }
    } catch (error) {
      logger.error('Error processing event batch', {
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      this.isProcessing = false;
    }
  }

  private startPeriodicTasks(): void {
    // Periodic metrics publishing
    setInterval(async () => {
      try {
        await this.publishEvent({
          eventType: 'EventGrid.Metrics',
          subject: 'eventgrid/metrics',
          data: this.getMetrics()
        });
      } catch (error) {
        // Don't log errors for metrics publishing to avoid recursion
      }
    }, 60000); // Every minute
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const testEvent: CustomEventData = {
        eventType: 'Test.HealthCheck',
        subject: 'health/check',
        data: {
          test: true,
          timestamp: new Date().toISOString()
        }
      };

      await this.publishEvent(testEvent);
      return true;
    } catch (error) {
      logger.error('Event Grid health check failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }
}

/**
 * Create Event Grid Integration Service instance
 */
export function createEventGridIntegration(): EventGridIntegrationService {
  const config: EventGridConfig = {
    endpoint: process.env.EVENT_GRID_TOPIC_ENDPOINT!,
    accessKey: process.env.EVENT_GRID_TOPIC_KEY!,
    retryAttempts: parseInt(process.env.EVENT_GRID_RETRY_ATTEMPTS || '3'),
    timeoutMs: parseInt(process.env.EVENT_GRID_TIMEOUT_MS || '30000')
  };

  if (!config.endpoint || !config.accessKey) {
    throw new Error('Event Grid configuration missing - check EVENT_GRID_TOPIC_ENDPOINT and EVENT_GRID_TOPIC_KEY');
  }

  return new EventGridIntegrationService(config);
}

// Export singleton instance
export const eventGridIntegration = createEventGridIntegration();
