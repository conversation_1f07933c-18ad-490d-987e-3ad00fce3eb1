# Function ID Duplicate Analysis

## CONFIRMED DUPLICATES FOUND:

### 1. "workflow-execution-start" ✅ FIXED
- `src/functions/workflow-execution.ts` → **Renamed to** `workflow-start`
- `src/functions/workflow-execution-start.ts` → **Kept as** `workflow-execution-start`
- `old-arch/src/workflow-service/execution/start/index.ts` → **Disabled**

### 2. "user-preferences-update" ✅ FIXED
- `src/functions/user-preferences.ts` → **Kept as** `user-preferences-update`
- `src/functions/user-management.ts` → **Renamed to** `user-profile-preferences-update`
- `old-arch/src/user-service/preferences/update/index.ts` → **Disabled**

### 3. "template-generate" ✅ FIXED
- `src/functions/template-generate.ts` → **Kept as** `template-generate`
- `src/functions/document-templates.ts` → **Renamed to** `document-template-generate`
- `old-arch/src/template-service/generate/index.ts` → **Disabled**

### 4. "email-send" ✅ FIXED
- `src/functions/email-service.ts` → **Kept as** `email-send`
- `src/functions/email-automation.ts` → **Renamed to** `email-automation-send`

### 5. "email-template-create" ❌ NEEDS FIX
- `src/functions/email-service.ts` → **Keep as** `email-template-create`
- `src/functions/email-automation.ts` → **Rename to** `email-automation-template-create`

### 6. "document-complete-content" ✅ FIXED
- `old-arch/src/document-service/complete-content/index.ts` → **Disabled**

### 7. "ai-intelligent-search" ✅ FIXED
- `old-arch/src/ai-service/intelligent-search/index.ts` → **Disabled**

## POTENTIAL DUPLICATES TO INVESTIGATE:

### Template-related functions:
- `template-create` (document-templates.ts vs old-arch)
- `workflow-template-create` vs `workflow-templates-create`
- `project-template-create`

### Email-related functions:
- `email-list` (appears in multiple files)

### Search-related functions:
- `search` vs `search-suggestions`

### Notification functions:
- `push-notification-*` variations

## ANALYSIS METHODOLOGY:
1. Extract all app.http() registrations
2. Group by function ID
3. Identify exact duplicates
4. Rename or disable duplicates
5. Rebuild and test

## NEXT STEPS:
1. Fix remaining "email-template-create" duplicate
2. Investigate potential duplicates
3. Create comprehensive test
