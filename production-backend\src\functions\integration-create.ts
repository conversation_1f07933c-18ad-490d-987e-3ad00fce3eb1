/**
 * Integration Create Function
 * Handles creation and management of external integrations
 * Migrated from old-arch/src/integration-service/create/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Integration types and providers
enum IntegrationType {
  WEBHOOK = 'webhook',
  API = 'api',
  DATABASE = 'database',
  STORAGE = 'storage',
  NOTIFICATION = 'notification',
  AUTHENTICATION = 'authentication',
  ANALYTICS = 'analytics'
}

enum IntegrationProvider {
  SLACK = 'slack',
  TEAMS = 'teams',
  DISCORD = 'discord',
  ZAPIER = 'zapier',
  SALESFORCE = 'salesforce',
  HUBSPOT = 'hubspot',
  GOOGLE_DRIVE = 'google_drive',
  DROPBOX = 'dropbox',
  ONEDRIVE = 'onedrive',
  AWS_S3 = 'aws_s3',
  AZURE_BLOB = 'azure_blob',
  CUSTOM = 'custom'
}

enum IntegrationStatus {
  PENDING = 'pending',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  EXPIRED = 'expired'
}

// Validation schema
const createIntegrationSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(IntegrationType)).required(),
  provider: Joi.string().valid(...Object.values(IntegrationProvider)).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  configuration: Joi.object({
    apiKey: Joi.string().optional(),
    apiSecret: Joi.string().optional(),
    accessToken: Joi.string().optional(),
    refreshToken: Joi.string().optional(),
    webhookUrl: Joi.string().uri().optional(),
    baseUrl: Joi.string().uri().optional(),
    scopes: Joi.array().items(Joi.string()).optional(),
    customFields: Joi.object().optional()
  }).required(),
  settings: Joi.object({
    syncEnabled: Joi.boolean().default(true),
    syncInterval: Joi.number().min(60).max(86400).default(3600), // 1 hour default
    retryAttempts: Joi.number().min(0).max(10).default(3),
    timeout: Joi.number().min(5000).max(300000).default(30000), // 30 seconds
    enableLogging: Joi.boolean().default(true),
    enableNotifications: Joi.boolean().default(true)
  }).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).default([])
});

interface CreateIntegrationRequest {
  name: string;
  description?: string;
  type: IntegrationType;
  provider: IntegrationProvider;
  organizationId: string;
  projectId?: string;
  configuration: {
    apiKey?: string;
    apiSecret?: string;
    accessToken?: string;
    refreshToken?: string;
    webhookUrl?: string;
    baseUrl?: string;
    scopes?: string[];
    customFields?: any;
  };
  settings?: {
    syncEnabled?: boolean;
    syncInterval?: number;
    retryAttempts?: number;
    timeout?: number;
    enableLogging?: boolean;
    enableNotifications?: boolean;
  };
  tags: string[];
}

/**
 * Create integration handler
 */
export async function createIntegration(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create integration started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createIntegrationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const integrationRequest: CreateIntegrationRequest = value;

    // Verify organization access
    const organization = await db.readItem('organizations', integrationRequest.organizationId, integrationRequest.organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user has integration management permissions
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, integrationRequest.organizationId, 'ACTIVE']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    const userMembership = memberships[0] as any;
    const canManageIntegrations = userMembership.role === 'OWNER' || userMembership.role === 'ADMIN';

    if (!canManageIntegrations) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to manage integrations" }
      }, request);
    }

    // Check integration limits for organization tier
    const orgData = organization as any;
    if (await isIntegrationLimitReached(integrationRequest.organizationId, orgData.tier)) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: "Integration limit reached for this organization tier",
          tier: orgData.tier
        }
      }, request);
    }

    // Validate integration configuration
    const configValidation = await validateIntegrationConfiguration(
      integrationRequest.type,
      integrationRequest.provider,
      integrationRequest.configuration
    );

    if (!configValidation.valid) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: "Invalid integration configuration",
          details: configValidation.errors
        }
      }, request);
    }

    // Create integration
    const integrationId = uuidv4();
    const now = new Date().toISOString();

    // Encrypt sensitive configuration data
    const encryptedConfiguration = await encryptSensitiveData(integrationRequest.configuration);

    const integration = {
      id: integrationId,
      name: integrationRequest.name,
      description: integrationRequest.description || "",
      type: integrationRequest.type,
      provider: integrationRequest.provider,
      status: IntegrationStatus.PENDING,
      organizationId: integrationRequest.organizationId,
      projectId: integrationRequest.projectId,
      configuration: encryptedConfiguration,
      settings: {
        syncEnabled: integrationRequest.settings?.syncEnabled ?? true,
        syncInterval: integrationRequest.settings?.syncInterval ?? 3600,
        retryAttempts: integrationRequest.settings?.retryAttempts ?? 3,
        timeout: integrationRequest.settings?.timeout ?? 30000,
        enableLogging: integrationRequest.settings?.enableLogging ?? true,
        enableNotifications: integrationRequest.settings?.enableNotifications ?? true
      },
      tags: integrationRequest.tags,
      createdBy: user.id,
      createdAt: now,
      updatedBy: user.id,
      updatedAt: now,
      lastSyncAt: null,
      syncStatus: null,
      errorMessage: null,
      metadata: {
        connectionAttempts: 0,
        lastConnectionAttempt: null,
        successfulSyncs: 0,
        failedSyncs: 0,
        totalDataSynced: 0
      },
      tenantId: user.tenantId
    };

    await db.createItem('integrations', integration);

    // Test the integration connection
    const connectionResult = await testIntegrationConnection(integration);

    // Update integration status based on connection test
    const updatedIntegration = {
      ...integration,
      status: connectionResult.success ? IntegrationStatus.CONNECTED : IntegrationStatus.ERROR,
      errorMessage: connectionResult.error || null,
      metadata: {
        ...integration.metadata,
        connectionAttempts: 1,
        lastConnectionAttempt: now
      },
      updatedAt: now
    };

    await db.updateItem('integrations', updatedIntegration);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "integration_created",
      userId: user.id,
      organizationId: integrationRequest.organizationId,
      projectId: integrationRequest.projectId,
      timestamp: now,
      details: {
        integrationId,
        integrationName: integrationRequest.name,
        type: integrationRequest.type,
        provider: integrationRequest.provider,
        status: updatedIntegration.status,
        connectionSuccess: connectionResult.success,
        organizationName: orgData.name
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'IntegrationCreated',
      aggregateId: integrationId,
      aggregateType: 'Integration',
      version: 1,
      data: {
        integration: {
          ...updatedIntegration,
          configuration: '[ENCRYPTED]' // Don't include sensitive data in events
        },
        createdBy: user.id,
        connectionResult
      },
      userId: user.id,
      organizationId: integrationRequest.organizationId,
      tenantId: user.tenantId
    });

    // Send notification
    const notificationMessage = connectionResult.success
      ? `Integration "${integrationRequest.name}" has been created and connected successfully.`
      : `Integration "${integrationRequest.name}" has been created but connection failed. Please check the configuration.`;

    await notificationService.sendNotification({
      userId: user.id,
      type: 'INTEGRATION_CREATED',
      title: connectionResult.success ? 'Integration connected successfully' : 'Integration created with connection issues',
      message: notificationMessage,
      priority: connectionResult.success ? 'normal' : 'high',
      metadata: {
        integrationId,
        integrationName: integrationRequest.name,
        type: integrationRequest.type,
        provider: integrationRequest.provider,
        status: updatedIntegration.status,
        organizationId: integrationRequest.organizationId,
        organizationName: orgData.name
      },
      organizationId: integrationRequest.organizationId,
      projectId: integrationRequest.projectId
    });

    logger.info("Integration created successfully", {
      correlationId,
      integrationId,
      integrationName: integrationRequest.name,
      type: integrationRequest.type,
      provider: integrationRequest.provider,
      status: updatedIntegration.status,
      organizationId: integrationRequest.organizationId,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: integrationId,
        name: updatedIntegration.name,
        type: updatedIntegration.type,
        provider: updatedIntegration.provider,
        status: updatedIntegration.status,
        organizationId: integrationRequest.organizationId,
        organizationName: orgData.name,
        connectionSuccess: connectionResult.success,
        errorMessage: connectionResult.error,
        message: "Integration created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create integration failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check if integration limit is reached for organization
 */
async function isIntegrationLimitReached(organizationId: string, tier: string): Promise<boolean> {
  try {
    const integrationCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
    const result = await db.queryItems('integrations', integrationCountQuery, [organizationId]);
    const currentCount = Number(result[0]) || 0;

    // Define tier limits
    const limits: { [key: string]: number } = {
      'FREE': 3,
      'PROFESSIONAL': 15,
      'ENTERPRISE': -1 // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];
    return limit > 0 && currentCount >= limit;

  } catch (error) {
    logger.error('Failed to check integration limit', { error, organizationId });
    return false;
  }
}

/**
 * Validate integration configuration
 */
async function validateIntegrationConfiguration(
  type: IntegrationType,
  provider: IntegrationProvider,
  configuration: any
): Promise<{ valid: boolean; errors: string[] }> {
  const errors: string[] = [];

  // Provider-specific validation
  switch (provider) {
    case IntegrationProvider.SLACK:
      if (!configuration.accessToken && !configuration.webhookUrl) {
        errors.push('Slack integration requires either accessToken or webhookUrl');
      }
      break;

    case IntegrationProvider.SALESFORCE:
      if (!configuration.apiKey || !configuration.apiSecret) {
        errors.push('Salesforce integration requires apiKey and apiSecret');
      }
      break;

    case IntegrationProvider.AWS_S3:
      if (!configuration.accessToken || !configuration.apiSecret) {
        errors.push('AWS S3 integration requires accessToken (Access Key ID) and apiSecret (Secret Access Key)');
      }
      break;

    case IntegrationProvider.CUSTOM:
      if (type === IntegrationType.WEBHOOK && !configuration.webhookUrl) {
        errors.push('Custom webhook integration requires webhookUrl');
      }
      if (type === IntegrationType.API && !configuration.baseUrl) {
        errors.push('Custom API integration requires baseUrl');
      }
      break;
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Encrypt sensitive configuration data
 */
async function encryptSensitiveData(configuration: any): Promise<any> {
  // In production, implement proper encryption
  // For now, we'll just mask sensitive fields
  const sensitiveFields = ['apiKey', 'apiSecret', 'accessToken', 'refreshToken'];
  const encrypted = { ...configuration };

  for (const field of sensitiveFields) {
    if (encrypted[field]) {
      // In production, use proper encryption like Azure Key Vault
      encrypted[field] = `[ENCRYPTED:${encrypted[field].substring(0, 4)}...]`;
    }
  }

  return encrypted;
}

/**
 * Test integration connection
 */
async function testIntegrationConnection(integration: any): Promise<{ success: boolean; error?: string }> {
  try {
    // Simplified connection test - in production, implement actual API calls
    logger.info("Testing integration connection", {
      integrationId: integration.id,
      type: integration.type,
      provider: integration.provider
    });

    // Simulate connection test based on provider
    switch (integration.provider) {
      case IntegrationProvider.SLACK:
        // In production, test Slack API connection
        return { success: true };

      case IntegrationProvider.SALESFORCE:
        // In production, test Salesforce API connection
        return { success: true };

      case IntegrationProvider.CUSTOM:
        // In production, test custom endpoint
        if (integration.configuration.webhookUrl || integration.configuration.baseUrl) {
          return { success: true };
        }
        return { success: false, error: 'No valid endpoint configured' };

      default:
        return { success: true };
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error('Integration connection test failed', {
      error: errorMessage,
      integrationId: integration.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}

// Register functions
app.http('integration-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'integrations',
  handler: createIntegration
});
