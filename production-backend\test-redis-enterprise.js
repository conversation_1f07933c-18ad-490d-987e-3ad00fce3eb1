/**
 * Redis Enterprise Test Script for Enhanced Services
 * Tests Redis Enterprise cluster connectivity and cache patterns
 */

const redis = require('redis');

// Redis Enterprise configuration
const config = {
  hostname: process.env.REDIS_HOSTNAME || 'hepzbackend.eastus.redis.azure.net',
  port: process.env.REDIS_PORT || 10000,
  password: process.env.REDIS_KEY || '',
  tls: true,
  socket: {
    tls: true,
    rejectUnauthorized: false
  }
};

console.log('🔴 Testing Redis Enterprise Cluster...');
console.log('=====================================');
console.log(`Host: ${config.hostname}:${config.port}`);
console.log(`TLS: ${config.tls ? 'Enabled' : 'Disabled'}`);

async function testRedisEnterpriseConnection() {
  console.log('\n🔗 Testing Redis Enterprise Connection...');
  
  try {
    const client = redis.createClient(config);
    
    client.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });
    
    await client.connect();
    console.log('✅ Connected to Redis Enterprise cluster successfully!');
    
    // Test basic ping
    const pong = await client.ping();
    console.log(`✅ Ping response: ${pong}`);
    
    // Get server info
    const info = await client.info('server');
    console.log('✅ Server info retrieved');
    
    await client.disconnect();
    return true;
  } catch (error) {
    console.log(`❌ Connection failed: ${error.message}`);
    return false;
  }
}

async function testCachePatterns() {
  console.log('\n🔄 Testing Cache Patterns...');
  
  try {
    const client = redis.createClient(config);
    await client.connect();
    
    // Test 1: User Profile Caching
    console.log('Testing user profile caching...');
    const userId = 'test-user-123';
    const userProfile = {
      id: userId,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      displayName: 'John Doe',
      jobTitle: 'Software Engineer',
      department: 'Engineering',
      organizationId: 'org-456',
      cachedAt: new Date().toISOString()
    };
    
    const userKey = `user:${userId}:profile`;
    await client.setEx(userKey, 3600, JSON.stringify(userProfile)); // 1 hour TTL
    
    const cachedProfile = await client.get(userKey);
    if (cachedProfile) {
      const parsed = JSON.parse(cachedProfile);
      console.log(`✅ User profile cached: ${parsed.displayName}`);
    }
    
    // Test 2: Project Details Caching
    console.log('Testing project details caching...');
    const projectId = 'project-789';
    const projectData = {
      id: projectId,
      name: 'Test Project',
      description: 'A test project for Redis Enterprise',
      organizationId: 'org-456',
      documentCount: 15,
      workflowCount: 3,
      memberCount: 8,
      storageUsed: 2048000,
      visibility: 'private',
      createdAt: '2024-01-01T00:00:00Z',
      cachedAt: new Date().toISOString()
    };
    
    const projectKey = `project:${projectId}:details`;
    await client.setEx(projectKey, 1800, JSON.stringify(projectData)); // 30 minutes TTL
    
    const cachedProject = await client.get(projectKey);
    if (cachedProject) {
      const parsed = JSON.parse(cachedProject);
      console.log(`✅ Project details cached: ${parsed.name} (${parsed.documentCount} docs)`);
    }
    
    // Test 3: Organization Caching
    console.log('Testing organization caching...');
    const orgId = 'org-456';
    const orgData = {
      id: orgId,
      name: 'Test Organization',
      description: 'A test organization',
      memberCount: 25,
      projectCount: 12,
      storageUsed: 10240000,
      subscription: 'enterprise',
      cachedAt: new Date().toISOString()
    };
    
    const orgKey = `org:${orgId}:details`;
    await client.setEx(orgKey, 1800, JSON.stringify(orgData)); // 30 minutes TTL
    
    const cachedOrg = await client.get(orgKey);
    if (cachedOrg) {
      const parsed = JSON.parse(cachedOrg);
      console.log(`✅ Organization cached: ${parsed.name} (${parsed.memberCount} members)`);
    }
    
    // Test 4: Cache Invalidation
    console.log('Testing cache invalidation...');
    await client.del(userKey);
    await client.del(projectKey);
    await client.del(orgKey);
    
    const deletedUser = await client.get(userKey);
    const deletedProject = await client.get(projectKey);
    const deletedOrg = await client.get(orgKey);
    
    if (!deletedUser && !deletedProject && !deletedOrg) {
      console.log('✅ Cache invalidation successful');
    }
    
    await client.disconnect();
    return true;
  } catch (error) {
    console.log(`❌ Cache patterns test failed: ${error.message}`);
    return false;
  }
}

async function testAdvancedFeatures() {
  console.log('\n⚡ Testing Advanced Redis Enterprise Features...');
  
  try {
    const client = redis.createClient(config);
    await client.connect();
    
    // Test 1: Hash operations for complex data
    console.log('Testing hash operations...');
    const sessionKey = 'session:user123:data';
    await client.hSet(sessionKey, {
      userId: 'user123',
      sessionId: 'sess_abc123',
      loginTime: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...'
    });
    
    const sessionData = await client.hGetAll(sessionKey);
    if (sessionData && sessionData.userId) {
      console.log(`✅ Hash operations: Session for ${sessionData.userId}`);
    }
    
    // Test 2: List operations for activity logs
    console.log('Testing list operations...');
    const activityKey = 'activity:user123:log';
    const activities = [
      JSON.stringify({ action: 'login', timestamp: new Date().toISOString() }),
      JSON.stringify({ action: 'view_document', documentId: 'doc123', timestamp: new Date().toISOString() }),
      JSON.stringify({ action: 'update_profile', timestamp: new Date().toISOString() })
    ];
    
    for (const activity of activities) {
      await client.lPush(activityKey, activity);
    }
    
    const recentActivities = await client.lRange(activityKey, 0, 4); // Get last 5 activities
    if (recentActivities.length > 0) {
      console.log(`✅ List operations: ${recentActivities.length} activities logged`);
    }
    
    // Test 3: Set operations for tags/categories
    console.log('Testing set operations...');
    const tagsKey = 'document:doc123:tags';
    await client.sAdd(tagsKey, ['important', 'finance', 'quarterly', 'report']);
    
    const tags = await client.sMembers(tagsKey);
    if (tags.length > 0) {
      console.log(`✅ Set operations: Document has ${tags.length} tags`);
    }
    
    // Test 4: Sorted set for leaderboards/rankings
    console.log('Testing sorted set operations...');
    const leaderboardKey = 'leaderboard:document_views';
    await client.zAdd(leaderboardKey, [
      { score: 150, value: 'user123' },
      { score: 89, value: 'user456' },
      { score: 234, value: 'user789' },
      { score: 67, value: 'user101' }
    ]);
    
    const topUsers = await client.zRevRange(leaderboardKey, 0, 2); // Top 3 users
    if (topUsers.length > 0) {
      console.log(`✅ Sorted set operations: Top user is ${topUsers[0]}`);
    }
    
    // Clean up test data
    await client.del(sessionKey);
    await client.del(activityKey);
    await client.del(tagsKey);
    await client.del(leaderboardKey);
    
    await client.disconnect();
    return true;
  } catch (error) {
    console.log(`❌ Advanced features test failed: ${error.message}`);
    return false;
  }
}

async function testPerformance() {
  console.log('\n🚀 Testing Redis Enterprise Performance...');
  
  try {
    const client = redis.createClient(config);
    await client.connect();
    
    const iterations = 100;
    const testData = {
      id: 'perf-test',
      data: 'x'.repeat(1000), // 1KB of data
      timestamp: new Date().toISOString()
    };
    
    // Test write performance
    console.log(`Testing write performance (${iterations} operations)...`);
    const writeStart = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      await client.set(`perf:write:${i}`, JSON.stringify({ ...testData, iteration: i }));
    }
    
    const writeTime = Date.now() - writeStart;
    const writeOpsPerSec = Math.round((iterations / writeTime) * 1000);
    console.log(`✅ Write performance: ${writeOpsPerSec} ops/sec`);
    
    // Test read performance
    console.log(`Testing read performance (${iterations} operations)...`);
    const readStart = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      await client.get(`perf:write:${i}`);
    }
    
    const readTime = Date.now() - readStart;
    const readOpsPerSec = Math.round((iterations / readTime) * 1000);
    console.log(`✅ Read performance: ${readOpsPerSec} ops/sec`);
    
    // Clean up performance test data
    for (let i = 0; i < iterations; i++) {
      await client.del(`perf:write:${i}`);
    }
    
    await client.disconnect();
    return true;
  } catch (error) {
    console.log(`❌ Performance test failed: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('Starting comprehensive Redis Enterprise test suite...\n');
  
  const results = {
    connection: await testRedisEnterpriseConnection(),
    cachePatterns: await testCachePatterns(),
    advancedFeatures: await testAdvancedFeatures(),
    performance: await testPerformance()
  };
  
  console.log('\n📊 Redis Enterprise Test Results:');
  console.log('=================================');
  
  const passed = Object.values(results).filter(r => r).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, result]) => {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${test.padEnd(20)}: ${status}`);
  });
  
  console.log(`\nOverall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('\n🎉 All Redis Enterprise tests passed! Cluster is ready for production.');
  } else {
    console.log('\n⚠️  Some tests failed. Check Redis Enterprise configuration.');
  }
  
  return passed === total;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testRedisEnterpriseConnection,
  testCachePatterns,
  testAdvancedFeatures,
  testPerformance,
  runAllTests
};
