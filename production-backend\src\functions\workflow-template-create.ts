/**
 * Workflow Template Create Function
 * Handles creation of workflow templates for reuse across projects
 * Migrated from old-arch/src/workflow-service/templates/create/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Validation schema
const createWorkflowTemplateSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(1000).optional(),
  category: Joi.string().max(50).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  definition: Joi.object({
    version: Joi.string().required(),
    steps: Joi.array().items(Joi.object({
      id: Joi.string().required(),
      name: Joi.string().required(),
      description: Joi.string().optional(),
      type: Joi.string().required(),
      action: Joi.object().required(),
      assignedTo: Joi.array().items(Joi.string()).optional(),
      dependencies: Joi.array().items(Joi.string()).optional(),
      conditions: Joi.array().optional(),
      timeout: Joi.number().optional(),
      position: Joi.object({
        x: Joi.number().required(),
        y: Joi.number().required()
      }).required()
    })).min(1).required(),
    triggers: Joi.array().items(Joi.object({
      id: Joi.string().required(),
      type: Joi.string().required(),
      conditions: Joi.array().required(),
      enabled: Joi.boolean().default(true)
    })).optional(),
    variables: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      type: Joi.string().required(),
      defaultValue: Joi.any().optional(),
      description: Joi.string().optional(),
      required: Joi.boolean().default(false)
    })).optional(),
    settings: Joi.object({
      allowParallelExecution: Joi.boolean().default(false),
      maxRetries: Joi.number().min(0).max(10).default(3),
      defaultTimeout: Joi.number().min(1).default(60),
      autoArchiveAfterDays: Joi.number().min(1).optional(),
      requireApprovalForChanges: Joi.boolean().default(false),
      enableAuditLog: Joi.boolean().default(true)
    }).optional()
  }).required(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).default([]),
  isPublic: Joi.boolean().default(false),
  isDefault: Joi.boolean().default(false)
});

interface CreateWorkflowTemplateRequest {
  name: string;
  description?: string;
  category: string;
  organizationId: string;
  projectId?: string;
  definition: any;
  tags: string[];
  isPublic: boolean;
  isDefault: boolean;
}

/**
 * Create workflow template handler
 */
export async function createWorkflowTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create workflow template started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createWorkflowTemplateSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const templateRequest: CreateWorkflowTemplateRequest = value;

    // Verify organization access
    const organization = await db.readItem('organizations', templateRequest.organizationId, templateRequest.organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user is a member of the organization
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, templateRequest.organizationId, 'ACTIVE']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // If projectId is provided, verify project access
    if (templateRequest.projectId) {
      const project = await db.readItem('projects', templateRequest.projectId, templateRequest.projectId);
      if (!project) {
        return addCorsHeaders({
          status: 404,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Project not found" }
        }, request);
      }

      if ((project as any).organizationId !== templateRequest.organizationId) {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Project does not belong to the specified organization" }
        }, request);
      }
    }

    // Check template limits for organization tier
    const orgData = organization as any;
    if (await isTemplateCountLimitReached(templateRequest.organizationId, orgData.tier)) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: "Template limit reached for this organization tier",
          tier: orgData.tier
        }
      }, request);
    }

    // Create workflow template
    const templateId = uuidv4();
    const now = new Date().toISOString();

    const workflowTemplate = {
      id: templateId,
      name: templateRequest.name,
      description: templateRequest.description || "",
      category: templateRequest.category,
      definition: templateRequest.definition,
      organizationId: templateRequest.organizationId,
      projectId: templateRequest.projectId,
      isPublic: templateRequest.isPublic,
      isDefault: templateRequest.isDefault,
      createdBy: user.id,
      createdAt: now,
      updatedBy: user.id,
      updatedAt: now,
      version: "1.0.0",
      tags: templateRequest.tags,
      usageCount: 0,
      rating: 0,
      metadata: {
        stepCount: templateRequest.definition.steps?.length || 0,
        triggerCount: templateRequest.definition.triggers?.length || 0,
        variableCount: templateRequest.definition.variables?.length || 0,
        complexity: calculateTemplateComplexity(templateRequest.definition)
      },
      tenantId: user.tenantId
    };

    await db.createItem('workflow-templates', workflowTemplate);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "workflow_template_created",
      userId: user.id,
      organizationId: templateRequest.organizationId,
      projectId: templateRequest.projectId,
      timestamp: now,
      details: {
        templateId,
        templateName: templateRequest.name,
        category: templateRequest.category,
        stepCount: workflowTemplate.metadata.stepCount,
        isPublic: templateRequest.isPublic,
        isDefault: templateRequest.isDefault
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'WorkflowTemplateCreated',
      aggregateId: templateId,
      aggregateType: 'WorkflowTemplate',
      version: 1,
      data: {
        template: workflowTemplate,
        createdBy: user.id,
        organizationName: orgData.name
      },
      userId: user.id,
      organizationId: templateRequest.organizationId,
      tenantId: user.tenantId
    });

    // Send notification for public templates
    if (templateRequest.isPublic) {
      await notificationService.sendNotification({
        userId: user.id,
        type: 'WORKFLOW_TEMPLATE_CREATED',
        title: 'Public Workflow Template Created',
        message: `Your workflow template "${templateRequest.name}" has been created and is now available to the community.`,
        priority: 'normal',
        metadata: {
          templateId,
          templateName: templateRequest.name,
          category: templateRequest.category,
          organizationId: templateRequest.organizationId,
          isPublic: true
        },
        organizationId: templateRequest.organizationId,
        projectId: templateRequest.projectId
      });
    }

    logger.info("Workflow template created successfully", {
      correlationId,
      templateId,
      templateName: templateRequest.name,
      category: templateRequest.category,
      organizationId: templateRequest.organizationId,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: templateId,
        name: workflowTemplate.name,
        description: workflowTemplate.description,
        category: workflowTemplate.category,
        version: workflowTemplate.version,
        organizationId: templateRequest.organizationId,
        projectId: templateRequest.projectId,
        stepCount: workflowTemplate.metadata.stepCount,
        complexity: workflowTemplate.metadata.complexity,
        isPublic: workflowTemplate.isPublic,
        isDefault: workflowTemplate.isDefault,
        message: "Workflow template created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create workflow template failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check if template count limit is reached for organization
 */
async function isTemplateCountLimitReached(organizationId: string, tier: string): Promise<boolean> {
  try {
    const templateCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
    const result = await db.queryItems('workflow-templates', templateCountQuery, [organizationId]);
    const currentCount = Number(result[0]) || 0;

    // Define tier limits
    const limits: { [key: string]: number } = {
      'FREE': 5,
      'PROFESSIONAL': 25,
      'ENTERPRISE': -1 // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];
    return limit > 0 && currentCount >= limit;

  } catch (error) {
    logger.error('Failed to check template count limit', { error, organizationId });
    return false;
  }
}

/**
 * Calculate template complexity based on definition
 */
function calculateTemplateComplexity(definition: any): 'LOW' | 'MEDIUM' | 'HIGH' {
  const stepCount = definition.steps?.length || 0;
  const triggerCount = definition.triggers?.length || 0;
  const variableCount = definition.variables?.length || 0;

  // Calculate complexity score
  let complexityScore = 0;
  complexityScore += stepCount * 2;
  complexityScore += triggerCount * 3;
  complexityScore += variableCount * 1;

  // Check for advanced features
  const hasConditionalSteps = definition.steps?.some((step: any) => step.conditions?.length > 0);
  const hasParallelExecution = definition.settings?.allowParallelExecution;
  const hasDependencies = definition.steps?.some((step: any) => step.dependencies?.length > 0);

  if (hasConditionalSteps) complexityScore += 5;
  if (hasParallelExecution) complexityScore += 5;
  if (hasDependencies) complexityScore += 3;

  if (complexityScore <= 10) return 'LOW';
  if (complexityScore <= 25) return 'MEDIUM';
  return 'HIGH';
}

// Register functions
app.http('workflow-template-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflow-templates/create',
  handler: createWorkflowTemplate
});
