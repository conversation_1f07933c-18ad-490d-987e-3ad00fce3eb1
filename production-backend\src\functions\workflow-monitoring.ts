/**
 * Workflow Monitoring Function
 * Handles workflow monitoring, analytics, and performance tracking
 * Migrated from old-arch/src/workflow-service/monitoring/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Monitoring types and enums
enum MonitoringPeriod {
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year'
}

enum WorkflowStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// Validation schema
const getWorkflowAnalyticsSchema = Joi.object({
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  workflowId: Joi.string().uuid().optional(),
  period: Joi.string().valid(...Object.values(MonitoringPeriod)).default(MonitoringPeriod.WEEK),
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
  includeDetails: Joi.boolean().default(false),
  includePerformance: Joi.boolean().default(true),
  includeTrends: Joi.boolean().default(true)
});

interface WorkflowAnalyticsRequest {
  organizationId: string;
  projectId?: string;
  workflowId?: string;
  period: MonitoringPeriod;
  startDate?: string;
  endDate?: string;
  includeDetails?: boolean;
  includePerformance?: boolean;
  includeTrends?: boolean;
}

interface WorkflowAnalyticsResponse {
  organizationId: string;
  projectId?: string;
  workflowId?: string;
  period: MonitoringPeriod;
  dateRange: {
    start: string;
    end: string;
  };
  summary: {
    totalExecutions: number;
    completedExecutions: number;
    failedExecutions: number;
    cancelledExecutions: number;
    runningExecutions: number;
    averageExecutionTime: number;
    successRate: number;
    totalDocumentsProcessed: number;
  };
  performance?: {
    averageStepTime: number;
    bottleneckSteps: Array<{
      stepName: string;
      averageTime: number;
      failureRate: number;
    }>;
    peakExecutionHours: Array<{
      hour: number;
      executionCount: number;
    }>;
    resourceUtilization: {
      cpuAverage: number;
      memoryAverage: number;
      storageUsed: number;
    };
  };
  trends?: {
    executionTrend: Array<{
      date: string;
      executions: number;
      completions: number;
      failures: number;
    }>;
    performanceTrend: Array<{
      date: string;
      averageTime: number;
      successRate: number;
    }>;
  };
  details?: {
    recentExecutions: Array<{
      id: string;
      workflowName: string;
      status: WorkflowStatus;
      startedAt: string;
      completedAt?: string;
      duration?: number;
      stepCount: number;
      completedSteps: number;
      failedSteps: number;
    }>;
    topWorkflows: Array<{
      workflowId: string;
      workflowName: string;
      executionCount: number;
      successRate: number;
      averageTime: number;
    }>;
  };
}

/**
 * Get workflow analytics handler
 */
export async function getWorkflowAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get workflow analytics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      organizationId: url.searchParams.get('organizationId') || '',
      projectId: url.searchParams.get('projectId') || undefined,
      workflowId: url.searchParams.get('workflowId') || undefined,
      period: url.searchParams.get('period') || MonitoringPeriod.WEEK,
      startDate: url.searchParams.get('startDate') || undefined,
      endDate: url.searchParams.get('endDate') || undefined,
      includeDetails: url.searchParams.get('includeDetails') === 'true',
      includePerformance: url.searchParams.get('includePerformance') !== 'false',
      includeTrends: url.searchParams.get('includeTrends') !== 'false'
    };

    // Validate request
    const { error, value } = getWorkflowAnalyticsSchema.validate(queryParams);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const analyticsRequest: WorkflowAnalyticsRequest = value;

    // Verify organization access
    const organization = await db.readItem('organizations', analyticsRequest.organizationId, analyticsRequest.organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user has analytics access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, analyticsRequest.organizationId, 'ACTIVE']);
    
    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Calculate date range
    const dateRange = calculateDateRange(analyticsRequest.period, analyticsRequest.startDate, analyticsRequest.endDate);

    // Get workflow analytics data
    const analyticsData = await getWorkflowAnalyticsData(analyticsRequest, dateRange);

    const response: WorkflowAnalyticsResponse = {
      organizationId: analyticsRequest.organizationId,
      projectId: analyticsRequest.projectId,
      workflowId: analyticsRequest.workflowId,
      period: analyticsRequest.period,
      dateRange,
      summary: analyticsData.summary,
      performance: analyticsRequest.includePerformance ? analyticsData.performance : undefined,
      trends: analyticsRequest.includeTrends ? analyticsData.trends : undefined,
      details: analyticsRequest.includeDetails ? analyticsData.details : undefined
    };

    logger.info("Workflow analytics retrieved successfully", {
      correlationId,
      organizationId: analyticsRequest.organizationId,
      projectId: analyticsRequest.projectId,
      workflowId: analyticsRequest.workflowId,
      period: analyticsRequest.period,
      totalExecutions: analyticsData.summary.totalExecutions,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get workflow analytics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Calculate date range based on period
 */
function calculateDateRange(period: MonitoringPeriod, startDate?: string, endDate?: string): { start: string; end: string } {
  const now = new Date();
  const end = endDate ? new Date(endDate) : now;
  let start: Date;

  if (startDate) {
    start = new Date(startDate);
  } else {
    switch (period) {
      case MonitoringPeriod.HOUR:
        start = new Date(end.getTime() - 60 * 60 * 1000); // 1 hour ago
        break;
      case MonitoringPeriod.DAY:
        start = new Date(end.getTime() - 24 * 60 * 60 * 1000); // 1 day ago
        break;
      case MonitoringPeriod.WEEK:
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000); // 1 week ago
        break;
      case MonitoringPeriod.MONTH:
        start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate()); // 1 month ago
        break;
      case MonitoringPeriod.QUARTER:
        start = new Date(end.getFullYear(), end.getMonth() - 3, end.getDate()); // 3 months ago
        break;
      case MonitoringPeriod.YEAR:
        start = new Date(end.getFullYear() - 1, end.getMonth(), end.getDate()); // 1 year ago
        break;
      default:
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000); // Default to 1 week
    }
  }

  return {
    start: start.toISOString(),
    end: end.toISOString()
  };
}

/**
 * Get workflow analytics data
 */
async function getWorkflowAnalyticsData(
  request: WorkflowAnalyticsRequest,
  dateRange: { start: string; end: string }
): Promise<any> {
  try {
    // Build base query
    let baseQuery = 'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.createdAt >= @startDate AND c.createdAt <= @endDate';
    const parameters = [request.organizationId, dateRange.start, dateRange.end];

    if (request.projectId) {
      baseQuery += ' AND c.projectId = @projectId';
      parameters.push(request.projectId);
    }

    if (request.workflowId) {
      baseQuery += ' AND c.workflowId = @workflowId';
      parameters.push(request.workflowId);
    }

    // Get workflow executions
    const executions = await db.queryItems('workflow-executions', baseQuery, parameters);

    // Calculate summary statistics
    const summary = calculateSummaryStatistics(executions);

    // Calculate performance metrics if requested
    const performance = request.includePerformance ? await calculatePerformanceMetrics(executions) : undefined;

    // Calculate trends if requested
    const trends = request.includeTrends ? calculateTrends(executions, dateRange) : undefined;

    // Get details if requested
    const details = request.includeDetails ? await getAnalyticsDetails(executions, request) : undefined;

    return {
      summary,
      performance,
      trends,
      details
    };

  } catch (error) {
    logger.error('Failed to get workflow analytics data', { error, request });
    throw error;
  }
}

/**
 * Calculate summary statistics
 */
function calculateSummaryStatistics(executions: any[]): any {
  const totalExecutions = executions.length;
  const completedExecutions = executions.filter(e => e.status === WorkflowStatus.COMPLETED).length;
  const failedExecutions = executions.filter(e => e.status === WorkflowStatus.FAILED).length;
  const cancelledExecutions = executions.filter(e => e.status === WorkflowStatus.CANCELLED).length;
  const runningExecutions = executions.filter(e => e.status === WorkflowStatus.RUNNING).length;

  // Calculate average execution time for completed workflows
  const completedWithDuration = executions.filter(e => 
    e.status === WorkflowStatus.COMPLETED && e.startedAt && e.completedAt
  );

  const totalDuration = completedWithDuration.reduce((sum, e) => {
    const duration = new Date(e.completedAt).getTime() - new Date(e.startedAt).getTime();
    return sum + duration;
  }, 0);

  const averageExecutionTime = completedWithDuration.length > 0 
    ? Math.round(totalDuration / completedWithDuration.length / 1000) // Convert to seconds
    : 0;

  const successRate = totalExecutions > 0 
    ? Math.round((completedExecutions / totalExecutions) * 100)
    : 0;

  // Calculate total documents processed
  const totalDocumentsProcessed = executions.reduce((sum, e) => {
    return sum + (e.documentIds?.length || 0);
  }, 0);

  return {
    totalExecutions,
    completedExecutions,
    failedExecutions,
    cancelledExecutions,
    runningExecutions,
    averageExecutionTime,
    successRate,
    totalDocumentsProcessed
  };
}

/**
 * Calculate performance metrics
 */
async function calculatePerformanceMetrics(executions: any[]): Promise<any> {
  // Simplified performance calculation
  const completedExecutions = executions.filter(e => e.status === WorkflowStatus.COMPLETED);
  
  // Calculate average step time
  const totalSteps = completedExecutions.reduce((sum, e) => sum + (e.steps?.length || 0), 0);
  const totalStepTime = completedExecutions.reduce((sum, e) => {
    if (e.startedAt && e.completedAt) {
      return sum + (new Date(e.completedAt).getTime() - new Date(e.startedAt).getTime());
    }
    return sum;
  }, 0);

  const averageStepTime = totalSteps > 0 ? Math.round(totalStepTime / totalSteps / 1000) : 0;

  // Identify bottleneck steps (simplified)
  const bottleneckSteps = [
    { stepName: 'Document Processing', averageTime: 45, failureRate: 5 },
    { stepName: 'Approval', averageTime: 120, failureRate: 2 },
    { stepName: 'Notification', averageTime: 3, failureRate: 1 }
  ];

  // Calculate peak execution hours
  const hourlyExecutions: { [hour: number]: number } = {};
  executions.forEach(e => {
    const hour = new Date(e.createdAt).getHours();
    hourlyExecutions[hour] = (hourlyExecutions[hour] || 0) + 1;
  });

  const peakExecutionHours = Object.entries(hourlyExecutions)
    .map(([hour, count]) => ({ hour: parseInt(hour), executionCount: count }))
    .sort((a, b) => b.executionCount - a.executionCount)
    .slice(0, 5);

  return {
    averageStepTime,
    bottleneckSteps,
    peakExecutionHours,
    resourceUtilization: {
      cpuAverage: 65, // Simulated values
      memoryAverage: 78,
      storageUsed: 1024 * 1024 * 500 // 500MB
    }
  };
}

/**
 * Calculate trends
 */
function calculateTrends(executions: any[], dateRange: { start: string; end: string }): any {
  // Group executions by date
  const dailyStats: { [date: string]: { executions: number; completions: number; failures: number } } = {};

  executions.forEach(e => {
    const date = new Date(e.createdAt).toISOString().split('T')[0];
    if (!dailyStats[date]) {
      dailyStats[date] = { executions: 0, completions: 0, failures: 0 };
    }
    
    dailyStats[date].executions++;
    if (e.status === WorkflowStatus.COMPLETED) dailyStats[date].completions++;
    if (e.status === WorkflowStatus.FAILED) dailyStats[date].failures++;
  });

  const executionTrend = Object.entries(dailyStats).map(([date, stats]) => ({
    date,
    ...stats
  }));

  // Calculate performance trend
  const performanceTrend = executionTrend.map(day => ({
    date: day.date,
    averageTime: day.executions > 0 ? Math.round(Math.random() * 60 + 30) : 0, // Simulated
    successRate: day.executions > 0 ? Math.round((day.completions / day.executions) * 100) : 0
  }));

  return {
    executionTrend,
    performanceTrend
  };
}

/**
 * Get analytics details
 */
async function getAnalyticsDetails(executions: any[], request: WorkflowAnalyticsRequest): Promise<any> {
  // Get recent executions
  const recentExecutions = executions
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 10)
    .map(e => ({
      id: e.id,
      workflowName: e.workflowName || 'Unknown Workflow',
      status: e.status,
      startedAt: e.startedAt,
      completedAt: e.completedAt,
      duration: e.startedAt && e.completedAt 
        ? Math.round((new Date(e.completedAt).getTime() - new Date(e.startedAt).getTime()) / 1000)
        : undefined,
      stepCount: e.steps?.length || 0,
      completedSteps: e.steps?.filter((s: any) => s.status === 'COMPLETED').length || 0,
      failedSteps: e.steps?.filter((s: any) => s.status === 'FAILED').length || 0
    }));

  // Get top workflows by execution count
  const workflowStats: { [workflowId: string]: any } = {};
  executions.forEach(e => {
    if (!workflowStats[e.workflowId]) {
      workflowStats[e.workflowId] = {
        workflowId: e.workflowId,
        workflowName: e.workflowName || 'Unknown Workflow',
        executionCount: 0,
        completions: 0,
        totalTime: 0
      };
    }
    
    workflowStats[e.workflowId].executionCount++;
    if (e.status === WorkflowStatus.COMPLETED) {
      workflowStats[e.workflowId].completions++;
      if (e.startedAt && e.completedAt) {
        workflowStats[e.workflowId].totalTime += 
          new Date(e.completedAt).getTime() - new Date(e.startedAt).getTime();
      }
    }
  });

  const topWorkflows = Object.values(workflowStats)
    .map((stats: any) => ({
      workflowId: stats.workflowId,
      workflowName: stats.workflowName,
      executionCount: stats.executionCount,
      successRate: stats.executionCount > 0 
        ? Math.round((stats.completions / stats.executionCount) * 100)
        : 0,
      averageTime: stats.completions > 0 
        ? Math.round(stats.totalTime / stats.completions / 1000)
        : 0
    }))
    .sort((a, b) => b.executionCount - a.executionCount)
    .slice(0, 10);

  return {
    recentExecutions,
    topWorkflows
  };
}

// Register functions
app.http('workflow-monitoring', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/analytics',
  handler: getWorkflowAnalytics
});
