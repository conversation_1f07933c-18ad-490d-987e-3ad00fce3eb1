/**
 * Permission Management Function
 * Handles advanced permission operations like granting, revoking, and checking permissions
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Permission types enum
enum PermissionType {
  READ = 'READ',
  WRITE = 'WRITE',
  DELETE = 'DELETE',
  ADMIN = 'ADMIN',
  SHARE = 'SHARE',
  COMMENT = 'COMMENT',
  APPROVE = 'APPROVE',
  EXECUTE = 'EXECUTE'
}

// Resource types enum
enum ResourceType {
  DOCUMENT = 'DOCUMENT',
  PROJECT = 'PROJECT',
  ORGANIZATION = 'ORGANIZATION',
  WORKFLOW = 'WORKFLOW',
  TEMPLATE = 'TEMPLATE'
}

// Validation schemas
const grantPermissionSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  resourceType: Joi.string().valid(...Object.values(ResourceType)).required(),
  resourceId: Joi.string().uuid().required(),
  permissions: Joi.array().items(Joi.string().valid(...Object.values(PermissionType))).min(1).required(),
  expiresAt: Joi.date().iso().optional(),
  reason: Joi.string().max(500).optional()
});

const revokePermissionSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  resourceType: Joi.string().valid(...Object.values(ResourceType)).required(),
  resourceId: Joi.string().uuid().required(),
  permissions: Joi.array().items(Joi.string().valid(...Object.values(PermissionType))).optional(),
  reason: Joi.string().max(500).optional()
});

const checkPermissionSchema = Joi.object({
  userId: Joi.string().uuid().optional(),
  resourceType: Joi.string().valid(...Object.values(ResourceType)).required(),
  resourceId: Joi.string().uuid().required(),
  permission: Joi.string().valid(...Object.values(PermissionType)).required()
});

const batchCheckSchema = Joi.object({
  userId: Joi.string().uuid().optional(),
  checks: Joi.array().items(
    Joi.object({
      resourceType: Joi.string().valid(...Object.values(ResourceType)).required(),
      resourceId: Joi.string().uuid().required(),
      permission: Joi.string().valid(...Object.values(PermissionType)).required()
    })
  ).min(1).max(50).required()
});

/**
 * Grant permission handler
 */
export async function grantPermission(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Grant permission started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = grantPermissionSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { userId, resourceType, resourceId, permissions, expiresAt, reason } = value;

    // Check if granter has permission to grant permissions on this resource
    const canGrant = await checkUserPermission(user.id, resourceType, resourceId, PermissionType.ADMIN);
    if (!canGrant) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to grant access" }
      }, request);
    }

    // Check if target user exists
    const targetUser = await db.readItem('users', userId, userId);
    if (!targetUser) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Target user not found" }
      }, request);
    }

    // Check if resource exists
    const resource = await getResource(resourceType, resourceId);
    if (!resource) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Resource not found" }
      }, request);
    }

    // Get existing permissions for this user and resource
    const existingPermissionQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.resourceType = @resourceType AND c.resourceId = @resourceId';
    const existingPermissions = await db.queryItems('permissions', existingPermissionQuery, [userId, resourceType, resourceId]);

    let permissionRecord;
    if (existingPermissions.length > 0) {
      // Update existing permission record
      const existing = existingPermissions[0] as any;
      const updatedPermissions = Array.from(new Set([...existing.permissions, ...permissions]));
      
      permissionRecord = {
        ...existing,
        id: existing.id,
        permissions: updatedPermissions,
        expiresAt: expiresAt || existing.expiresAt,
        updatedBy: user.id,
        updatedAt: new Date().toISOString(),
        lastGrantedBy: user.id,
        lastGrantedAt: new Date().toISOString(),
        grantReason: reason
      };
      
      await db.updateItem('permissions', permissionRecord);
    } else {
      // Create new permission record
      const permissionId = uuidv4();
      permissionRecord = {
        id: permissionId,
        userId,
        resourceType,
        resourceId,
        permissions,
        grantedBy: user.id,
        grantedAt: new Date().toISOString(),
        expiresAt,
        grantReason: reason,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tenantId: user.tenantId
      };
      
      await db.createItem('permissions', permissionRecord);
    }

    // Create permission audit record
    await db.createItem('permission-audit', {
      id: uuidv4(),
      action: 'GRANT',
      userId,
      resourceType,
      resourceId,
      permissions,
      performedBy: user.id,
      performedAt: new Date().toISOString(),
      reason,
      expiresAt,
      tenantId: user.tenantId
    });

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "permission_granted",
      userId: user.id,
      timestamp: new Date().toISOString(),
      details: {
        targetUserId: userId,
        resourceType,
        resourceId,
        permissions,
        reason
      },
      tenantId: user.tenantId
    });

    logger.info("Permission granted successfully", {
      correlationId,
      grantedBy: user.id,
      targetUserId: userId,
      resourceType,
      resourceId,
      permissions
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        userId,
        resourceType,
        resourceId,
        permissions: permissionRecord.permissions,
        expiresAt: permissionRecord.expiresAt,
        message: "Permissions granted successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Grant permission failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check permission handler
 */
export async function checkPermission(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Check permission started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = checkPermissionSchema.validate(queryParams);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { userId, resourceType, resourceId, permission } = value;
    const targetUserId = userId || user.id;

    // Check if requesting user can check permissions for the target user
    if (targetUserId !== user.id && !user.roles?.includes('admin')) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Can only check your own permissions" }
      }, request);
    }

    // Check the permission
    const hasPermission = await checkUserPermission(targetUserId, resourceType, resourceId, permission);

    // Get permission details if they have access
    let permissionDetails = null;
    if (hasPermission) {
      const permissionQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.resourceType = @resourceType AND c.resourceId = @resourceId';
      const permissions = await db.queryItems('permissions', permissionQuery, [targetUserId, resourceType, resourceId]);
      
      if (permissions.length > 0) {
        const perm = permissions[0] as any;
        permissionDetails = {
          permissions: perm.permissions,
          grantedBy: perm.grantedBy,
          grantedAt: perm.grantedAt,
          expiresAt: perm.expiresAt
        };
      }
    }

    logger.info("Permission checked successfully", {
      correlationId,
      checkedBy: user.id,
      targetUserId,
      resourceType,
      resourceId,
      permission,
      hasPermission
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        userId: targetUserId,
        resourceType,
        resourceId,
        permission,
        hasPermission,
        details: permissionDetails
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Check permission failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Batch check permissions handler
 */
export async function batchCheckPermissions(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Batch check permissions started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = batchCheckSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { userId, checks } = value;
    const targetUserId = userId || user.id;

    // Check if requesting user can check permissions for the target user
    if (targetUserId !== user.id && !user.roles?.includes('admin')) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Can only check your own permissions" }
      }, request);
    }

    // Perform batch permission checks
    const results = await Promise.all(
      checks.map(async (check: any) => {
        const hasPermission = await checkUserPermission(
          targetUserId,
          check.resourceType,
          check.resourceId,
          check.permission
        );

        return {
          resourceType: check.resourceType,
          resourceId: check.resourceId,
          permission: check.permission,
          hasPermission
        };
      })
    );

    logger.info("Batch permission check completed", {
      correlationId,
      checkedBy: user.id,
      targetUserId,
      checksCount: checks.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        userId: targetUserId,
        results,
        totalChecks: checks.length
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Batch check permissions failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check if user has specific permission on resource
 */
async function checkUserPermission(userId: string, resourceType: string, resourceId: string, permission: string): Promise<boolean> {
  try {
    // Check direct permissions
    const permissionQuery = `
      SELECT * FROM c 
      WHERE c.userId = @userId 
      AND c.resourceType = @resourceType 
      AND c.resourceId = @resourceId 
      AND c.isActive = true
      AND (c.expiresAt IS NULL OR c.expiresAt > @now)
    `;
    const permissions = await db.queryItems('permissions', permissionQuery, [userId, resourceType, resourceId, new Date().toISOString()]);

    if (permissions.length > 0) {
      const userPermissions = (permissions[0] as any).permissions || [];
      if (userPermissions.includes(permission) || userPermissions.includes(PermissionType.ADMIN)) {
        return true;
      }
    }

    // Check inherited permissions (e.g., organization admin, resource owner)
    return await checkInheritedPermissions(userId, resourceType, resourceId, permission);

  } catch (error) {
    logger.error("Error checking user permission", { userId, resourceType, resourceId, permission, error });
    return false;
  }
}

/**
 * Check inherited permissions
 */
async function checkInheritedPermissions(userId: string, resourceType: string, resourceId: string, permission: string): Promise<boolean> {
  try {
    // Get the resource to check ownership
    const resource = await getResource(resourceType, resourceId);
    if (!resource) return false;

    // Check if user is the owner/creator
    if ((resource as any).createdBy === userId) {
      return true;
    }

    // Check organization-level permissions
    if ((resource as any).organizationId) {
      const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [userId, (resource as any).organizationId, 'active']);
      
      if (memberships.length > 0) {
        const membership = memberships[0] as any;
        // Organization admins have all permissions
        if (membership.role === 'ADMIN') {
          return true;
        }
        // Members have read access by default
        if (permission === PermissionType.READ && membership.role === 'MEMBER') {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    logger.error("Error checking inherited permissions", { userId, resourceType, resourceId, permission, error });
    return false;
  }
}

/**
 * Get resource by type and ID
 */
async function getResource(resourceType: string, resourceId: string): Promise<any> {
  const containerMap: { [key: string]: string } = {
    [ResourceType.DOCUMENT]: 'documents',
    [ResourceType.PROJECT]: 'projects',
    [ResourceType.ORGANIZATION]: 'organizations',
    [ResourceType.WORKFLOW]: 'workflows',
    [ResourceType.TEMPLATE]: 'templates'
  };

  const container = containerMap[resourceType];
  if (!container) return null;

  try {
    return await db.readItem(container, resourceId, resourceId);
  } catch (error) {
    return null;
  }
}

// Register functions
app.http('permission-grant', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'permissions/grant',
  handler: grantPermission
});

app.http('permission-check', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'permissions/check',
  handler: checkPermission
});

app.http('permission-batch-check', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'permissions/batch-check',
  handler: batchCheckPermissions
});
