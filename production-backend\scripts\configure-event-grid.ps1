# Azure Event Grid Configuration Script
# This script sets up comprehensive Event Grid infrastructure for the application

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroup = "docucontext",
    
    [Parameter(Mandatory=$true)]
    [string]$Location = "eastus",
    
    [Parameter(Mandatory=$true)]
    [string]$FunctionAppName,
    
    [Parameter(Mandatory=$false)]
    [string]$StorageAccountName = "stdocucontex900520441468",
    
    [Parameter(Mandatory=$false)]
    [string]$EventGridTopicName = "hepzeg"
)

Write-Host "🚀 Starting Azure Event Grid Configuration..." -ForegroundColor Green
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Yellow
Write-Host "Location: $Location" -ForegroundColor Yellow
Write-Host "Function App: $FunctionAppName" -ForegroundColor Yellow

# Get subscription ID
$subscriptionId = az account show --query id --output tsv
Write-Host "Subscription ID: $subscriptionId" -ForegroundColor Yellow

# Get Function App details
Write-Host "📋 Getting Function App details..." -ForegroundColor Blue
$functionAppId = az functionapp show --name $FunctionAppName --resource-group $ResourceGroup --query id --output tsv
$functionAppUrl = az functionapp show --name $FunctionAppName --resource-group $ResourceGroup --query defaultHostName --output tsv

if (-not $functionAppId) {
    Write-Error "Function App '$FunctionAppName' not found in resource group '$ResourceGroup'"
    exit 1
}

Write-Host "Function App ID: $functionAppId" -ForegroundColor Green
Write-Host "Function App URL: https://$functionAppUrl" -ForegroundColor Green

# 1. Create Event Grid System Topic for Storage Account
Write-Host "📦 Creating Event Grid System Topic for Storage Account..." -ForegroundColor Blue
$storageAccountId = "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.Storage/storageAccounts/$StorageAccountName"
$systemTopicName = "$StorageAccountName-events"

az eventgrid system-topic create `
    --name $systemTopicName `
    --resource-group $ResourceGroup `
    --source $storageAccountId `
    --topic-type "Microsoft.Storage.StorageAccounts" `
    --location $Location

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Storage System Topic created: $systemTopicName" -ForegroundColor Green
} else {
    Write-Host "⚠️ Storage System Topic may already exist or failed to create" -ForegroundColor Yellow
}

# 2. Create Event Grid Subscriptions for Storage Events
Write-Host "📬 Creating Event Grid Subscriptions for Storage Events..." -ForegroundColor Blue

# Blob Created Events Subscription
$blobCreatedSubscription = "blob-created-events"
az eventgrid event-subscription create `
    --name $blobCreatedSubscription `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$systemTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Microsoft.Storage.BlobCreated" `
    --subject-begins-with "/blobServices/default/containers/documents/" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Blob Created subscription created: $blobCreatedSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Blob Created subscription may already exist or failed to create" -ForegroundColor Yellow
}

# Blob Deleted Events Subscription
$blobDeletedSubscription = "blob-deleted-events"
az eventgrid event-subscription create `
    --name $blobDeletedSubscription `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$systemTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Microsoft.Storage.BlobDeleted" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Blob Deleted subscription created: $blobDeletedSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Blob Deleted subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 3. Create Custom Event Grid Subscriptions for Application Events
Write-Host "🎯 Creating Custom Event Grid Subscriptions..." -ForegroundColor Blue

# Document Processing Events
$documentEventsSubscription = "document-processing-events"
az eventgrid event-subscription create `
    --name $documentEventsSubscription `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Document.Uploaded" "Document.Processed" "Document.Shared" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Document Events subscription created: $documentEventsSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Document Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# Workflow Events
$workflowEventsSubscription = "workflow-events"
az eventgrid event-subscription create `
    --name $workflowEventsSubscription `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Workflow.Started" "Workflow.Completed" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Workflow Events subscription created: $workflowEventsSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Workflow Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# User Events
$userEventsSubscription = "user-events"
az eventgrid event-subscription create `
    --name $userEventsSubscription `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "User.Registered" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ User Events subscription created: $userEventsSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ User Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# System Events
$systemEventsSubscription = "system-events"
az eventgrid event-subscription create `
    --name $systemEventsSubscription `
    --source-resource-id "/subscriptions/$subscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "https://$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "System.HealthCheck" "Performance.Alert" "Analytics.Generated" `
    --max-delivery-attempts 3 `
    --event-ttl 1440

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ System Events subscription created: $systemEventsSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ System Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 4. Configure Dead Letter Storage
Write-Host "💀 Configuring Dead Letter Storage..." -ForegroundColor Blue
$deadLetterContainer = "dead-letter-events"

# Create dead letter container if it doesn't exist
az storage container create `
    --name $deadLetterContainer `
    --account-name $StorageAccountName `
    --auth-mode login

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Dead letter container created: $deadLetterContainer" -ForegroundColor Green
} else {
    Write-Host "⚠️ Dead letter container may already exist" -ForegroundColor Yellow
}

# 5. Test Event Grid Configuration
Write-Host "🧪 Testing Event Grid Configuration..." -ForegroundColor Blue
$testEventData = @{
    id = "test-$(Get-Date -Format 'yyyyMMddHHmmss')"
    eventType = "Test.ConfigurationCheck"
    subject = "test/eventgrid/configuration"
    eventTime = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
    data = @{
        test = $true
        timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        message = "Event Grid configuration test"
    }
    dataVersion = "1.0"
} | ConvertTo-Json -Depth 3

# Get Event Grid topic endpoint and key
$topicEndpoint = az eventgrid topic show --name $EventGridTopicName --resource-group $ResourceGroup --query "endpoint" --output tsv
$topicKey = az eventgrid topic key list --name $EventGridTopicName --resource-group $ResourceGroup --query "key1" --output tsv

Write-Host "Event Grid Topic Endpoint: $topicEndpoint" -ForegroundColor Green
Write-Host "Event Grid Topic Key: $($topicKey.Substring(0,10))..." -ForegroundColor Green

Write-Host "✅ Azure Event Grid Configuration completed!" -ForegroundColor Green
Write-Host "📝 Configuration Summary:" -ForegroundColor Yellow
Write-Host "   - Event Grid Topic: $EventGridTopicName" -ForegroundColor White
Write-Host "   - Storage System Topic: $systemTopicName" -ForegroundColor White
Write-Host "   - Event Subscriptions: 5 created" -ForegroundColor White
Write-Host "   - Dead Letter Container: $deadLetterContainer" -ForegroundColor White
Write-Host "   - Function App Webhook: https://$functionAppUrl/api/eventgrid/webhook" -ForegroundColor White

Write-Host "🔄 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Deploy your Azure Functions with Event Grid triggers" -ForegroundColor White
Write-Host "   2. Test event publishing and subscription" -ForegroundColor White
Write-Host "   3. Monitor Event Grid metrics in Azure Portal" -ForegroundColor White
Write-Host "   4. Configure additional event filters as needed" -ForegroundColor White
