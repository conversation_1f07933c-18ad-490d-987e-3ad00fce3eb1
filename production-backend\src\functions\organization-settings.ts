/**
 * Organization Settings Function
 * Handles organization settings, configuration, and policy management
 * Migrated from old-arch/src/organization-service/settings/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Validation schemas
const updateOrganizationSettingsSchema = Joi.object({
  organizationId: Joi.string().uuid().required(),
  general: Joi.object({
    allowPublicProjects: Joi.boolean().optional(),
    requireApprovalForNewMembers: Joi.boolean().optional(),
    enableGuestAccess: Joi.boolean().optional(),
    defaultProjectVisibility: Joi.string().valid('private', 'organization', 'public').optional(),
    defaultDocumentRetentionDays: Joi.number().min(1).max(3650).optional(),
    enableAuditLog: Joi.boolean().optional()
  }).optional(),
  security: Joi.object({
    enforcePasswordPolicy: Joi.boolean().optional(),
    passwordPolicy: Joi.object({
      minLength: Joi.number().min(6).max(128).optional(),
      requireUppercase: Joi.boolean().optional(),
      requireLowercase: Joi.boolean().optional(),
      requireNumbers: Joi.boolean().optional(),
      requireSpecialChars: Joi.boolean().optional(),
      maxAge: Joi.number().min(1).max(365).optional()
    }).optional(),
    requireTwoFactor: Joi.boolean().optional(),
    sessionTimeoutMinutes: Joi.number().min(5).max(1440).optional(),
    allowedIpRanges: Joi.array().items(Joi.string()).optional(),
    enableDeviceTracking: Joi.boolean().optional(),
    requireApprovalForSensitiveActions: Joi.boolean().optional()
  }).optional(),
  features: Joi.object({
    aiAnalysis: Joi.boolean().optional(),
    advancedWorkflows: Joi.boolean().optional(),
    bulkProcessing: Joi.boolean().optional(),
    apiAccess: Joi.boolean().optional(),
    customBranding: Joi.boolean().optional(),
    advancedAnalytics: Joi.boolean().optional(),
    integrations: Joi.boolean().optional(),
    webhooks: Joi.boolean().optional()
  }).optional(),
  limits: Joi.object({
    maxProjects: Joi.number().min(1).optional(),
    maxMembers: Joi.number().min(1).optional(),
    maxStorageGB: Joi.number().min(1).optional(),
    maxFileSize: Joi.number().min(1).optional(),
    maxApiCallsPerMonth: Joi.number().min(1).optional(),
    maxWorkflowExecutionsPerMonth: Joi.number().min(1).optional()
  }).optional(),
  branding: Joi.object({
    logoUrl: Joi.string().uri().optional(),
    primaryColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional(),
    secondaryColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional(),
    accentColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional(),
    customDomain: Joi.string().domain().optional(),
    organizationName: Joi.string().max(100).optional()
  }).optional(),
  notifications: Joi.object({
    enableEmailNotifications: Joi.boolean().optional(),
    enableSlackIntegration: Joi.boolean().optional(),
    enableTeamsIntegration: Joi.boolean().optional(),
    defaultNotificationSettings: Joi.object({
      documentUploaded: Joi.boolean().optional(),
      documentProcessed: Joi.boolean().optional(),
      workflowAssigned: Joi.boolean().optional(),
      memberAdded: Joi.boolean().optional(),
      projectCreated: Joi.boolean().optional()
    }).optional(),
    adminNotifications: Joi.object({
      newMemberRequests: Joi.boolean().optional(),
      securityAlerts: Joi.boolean().optional(),
      systemUpdates: Joi.boolean().optional(),
      usageAlerts: Joi.boolean().optional()
    }).optional()
  }).optional(),
  integrations: Joi.object({
    enabledProviders: Joi.array().items(Joi.string()).optional(),
    ssoProvider: Joi.string().optional(),
    ssoConfiguration: Joi.object().optional(),
    webhookEndpoints: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      url: Joi.string().uri().required(),
      events: Joi.array().items(Joi.string()).required(),
      secret: Joi.string().optional()
    })).optional()
  }).optional(),
  compliance: Joi.object({
    dataRetentionPolicy: Joi.object({
      enabled: Joi.boolean().optional(),
      retentionDays: Joi.number().min(1).max(3650).optional(),
      archiveBeforeDelete: Joi.boolean().optional(),
      autoDeleteAfterRetention: Joi.boolean().optional()
    }).optional(),
    auditSettings: Joi.object({
      logAllActions: Joi.boolean().optional(),
      logRetentionDays: Joi.number().min(1).max(2555).optional(), // 7 years max
      enableRealTimeAlerts: Joi.boolean().optional()
    }).optional(),
    privacySettings: Joi.object({
      allowDataExport: Joi.boolean().optional(),
      allowDataDeletion: Joi.boolean().optional(),
      requireConsentForAnalytics: Joi.boolean().optional()
    }).optional()
  }).optional()
});

interface OrganizationSettings {
  general: {
    allowPublicProjects: boolean;
    requireApprovalForNewMembers: boolean;
    enableGuestAccess: boolean;
    defaultProjectVisibility: 'private' | 'organization' | 'public';
    defaultDocumentRetentionDays: number;
    enableAuditLog: boolean;
  };
  security: {
    enforcePasswordPolicy: boolean;
    passwordPolicy: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
      maxAge: number;
    };
    requireTwoFactor: boolean;
    sessionTimeoutMinutes: number;
    allowedIpRanges: string[];
    enableDeviceTracking: boolean;
    requireApprovalForSensitiveActions: boolean;
  };
  features: {
    aiAnalysis: boolean;
    advancedWorkflows: boolean;
    bulkProcessing: boolean;
    apiAccess: boolean;
    customBranding: boolean;
    advancedAnalytics: boolean;
    integrations: boolean;
    webhooks: boolean;
  };
  limits: {
    maxProjects: number;
    maxMembers: number;
    maxStorageGB: number;
    maxFileSize: number;
    maxApiCallsPerMonth: number;
    maxWorkflowExecutionsPerMonth: number;
  };
  branding: {
    logoUrl?: string;
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    customDomain?: string;
    organizationName?: string;
  };
  notifications: {
    enableEmailNotifications: boolean;
    enableSlackIntegration: boolean;
    enableTeamsIntegration: boolean;
    defaultNotificationSettings: {
      documentUploaded: boolean;
      documentProcessed: boolean;
      workflowAssigned: boolean;
      memberAdded: boolean;
      projectCreated: boolean;
    };
    adminNotifications: {
      newMemberRequests: boolean;
      securityAlerts: boolean;
      systemUpdates: boolean;
      usageAlerts: boolean;
    };
  };
  integrations: {
    enabledProviders: string[];
    ssoProvider?: string;
    ssoConfiguration?: any;
    webhookEndpoints: Array<{
      name: string;
      url: string;
      events: string[];
      secret?: string;
    }>;
  };
  compliance: {
    dataRetentionPolicy: {
      enabled: boolean;
      retentionDays: number;
      archiveBeforeDelete: boolean;
      autoDeleteAfterRetention: boolean;
    };
    auditSettings: {
      logAllActions: boolean;
      logRetentionDays: number;
      enableRealTimeAlerts: boolean;
    };
    privacySettings: {
      allowDataExport: boolean;
      allowDataDeletion: boolean;
      requireConsentForAnalytics: boolean;
    };
  };
}

/**
 * Get organization settings handler
 */
export async function getOrganizationSettings(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const organizationId = request.params.organizationId;
  
  logger.info("Get organization settings started", { correlationId, organizationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization ID is required" }
      }, request);
    }

    // Check if user has permission to view organization settings
    const hasPermission = await checkOrganizationSettingsPermission(organizationId, user.id, 'VIEW');
    if (!hasPermission) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization settings" }
      }, request);
    }

    // Get organization
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    const organizationData = organization as any;

    // Get organization settings or return defaults
    const settings = organizationData.settings || getDefaultOrganizationSettings(organizationData.tier);

    logger.info("Organization settings retrieved successfully", {
      correlationId,
      organizationId,
      userId: user.id,
      hasCustomSettings: !!organizationData.settings
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        organizationId,
        organizationName: organizationData.name,
        tier: organizationData.tier,
        settings,
        lastUpdated: organizationData.updatedAt,
        isDefault: !organizationData.settings
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get organization settings failed", {
      correlationId,
      organizationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update organization settings handler
 */
export async function updateOrganizationSettings(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update organization settings started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updateOrganizationSettingsSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const settingsUpdate = value;
    const organizationId = settingsUpdate.organizationId;

    // Check if user has permission to update organization settings
    const hasPermission = await checkOrganizationSettingsPermission(organizationId, user.id, 'UPDATE');
    if (!hasPermission) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to update organization settings" }
      }, request);
    }

    // Get organization
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    const organizationData = organization as any;
    const now = new Date().toISOString();

    // Get current settings or defaults
    const currentSettings = organizationData.settings || getDefaultOrganizationSettings(organizationData.tier);

    // Validate settings against organization tier
    const validationResult = validateSettingsForTier(settingsUpdate, organizationData.tier);
    if (!validationResult.valid) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Settings validation failed', 
          message: validationResult.errors.join(', ')
        }
      }, request);
    }

    // Deep merge settings
    const updatedSettings = deepMergeSettings(currentSettings, settingsUpdate);

    // Update organization with new settings
    const updatedOrganization = {
      ...organizationData,
      settings: updatedSettings,
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('organizations', updatedOrganization);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "organization_settings_updated",
      userId: user.id,
      organizationId,
      timestamp: now,
      details: {
        updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'organizationId'),
        organizationName: organizationData.name,
        tier: organizationData.tier
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'OrganizationSettingsUpdated',
      aggregateId: organizationId,
      aggregateType: 'Organization',
      version: 1,
      data: {
        organization: organizationData,
        previousSettings: currentSettings,
        newSettings: updatedSettings,
        updatedBy: user.id
      },
      userId: user.id,
      organizationId,
      tenantId: user.tenantId
    });

    logger.info("Organization settings updated successfully", {
      correlationId,
      organizationId,
      updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'organizationId'),
      updatedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        organizationId,
        organizationName: organizationData.name,
        settings: updatedSettings,
        updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'organizationId'),
        updatedAt: now,
        updatedBy: user.id,
        message: "Organization settings updated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update organization settings failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationSettingsPermission(organizationId: string, userId: string, action: string): Promise<boolean> {
  try {
    // Check if user is an owner or admin of the organization
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    
    if (memberships.length === 0) {
      return false;
    }

    const membership = memberships[0] as any;
    
    switch (action) {
      case 'VIEW':
        return ['OWNER', 'ADMIN', 'MEMBER'].includes(membership.role);
      case 'UPDATE':
        return ['OWNER', 'ADMIN'].includes(membership.role);
      default:
        return false;
    }
  } catch (error) {
    logger.error('Failed to check organization settings permission', { error, organizationId, userId, action });
    return false;
  }
}

function getDefaultOrganizationSettings(tier: string): OrganizationSettings {
  const tierLimits = getTierLimits(tier);
  
  return {
    general: {
      allowPublicProjects: false,
      requireApprovalForNewMembers: true,
      enableGuestAccess: false,
      defaultProjectVisibility: 'private',
      defaultDocumentRetentionDays: 365,
      enableAuditLog: tier !== 'FREE'
    },
    security: {
      enforcePasswordPolicy: tier !== 'FREE',
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: false,
        maxAge: 90
      },
      requireTwoFactor: false,
      sessionTimeoutMinutes: 480, // 8 hours
      allowedIpRanges: [],
      enableDeviceTracking: tier === 'ENTERPRISE',
      requireApprovalForSensitiveActions: tier === 'ENTERPRISE'
    },
    features: {
      aiAnalysis: tier !== 'FREE',
      advancedWorkflows: tier === 'ENTERPRISE',
      bulkProcessing: tier !== 'FREE',
      apiAccess: tier !== 'FREE',
      customBranding: tier === 'ENTERPRISE',
      advancedAnalytics: tier !== 'FREE',
      integrations: tier !== 'FREE',
      webhooks: tier !== 'FREE'
    },
    limits: tierLimits,
    branding: {
      primaryColor: '#007bff',
      secondaryColor: '#6c757d',
      accentColor: '#28a745'
    },
    notifications: {
      enableEmailNotifications: true,
      enableSlackIntegration: tier !== 'FREE',
      enableTeamsIntegration: tier !== 'FREE',
      defaultNotificationSettings: {
        documentUploaded: true,
        documentProcessed: true,
        workflowAssigned: true,
        memberAdded: true,
        projectCreated: true
      },
      adminNotifications: {
        newMemberRequests: true,
        securityAlerts: true,
        systemUpdates: true,
        usageAlerts: true
      }
    },
    integrations: {
      enabledProviders: [],
      webhookEndpoints: []
    },
    compliance: {
      dataRetentionPolicy: {
        enabled: false,
        retentionDays: 365,
        archiveBeforeDelete: true,
        autoDeleteAfterRetention: false
      },
      auditSettings: {
        logAllActions: tier !== 'FREE',
        logRetentionDays: tier === 'ENTERPRISE' ? 2555 : 365, // 7 years for enterprise
        enableRealTimeAlerts: tier === 'ENTERPRISE'
      },
      privacySettings: {
        allowDataExport: true,
        allowDataDeletion: true,
        requireConsentForAnalytics: false
      }
    }
  };
}

function getTierLimits(tier: string): any {
  switch (tier) {
    case 'FREE':
      return {
        maxProjects: 3,
        maxMembers: 5,
        maxStorageGB: 1,
        maxFileSize: 10, // MB
        maxApiCallsPerMonth: 1000,
        maxWorkflowExecutionsPerMonth: 100
      };
    case 'PROFESSIONAL':
      return {
        maxProjects: 50,
        maxMembers: 50,
        maxStorageGB: 100,
        maxFileSize: 100, // MB
        maxApiCallsPerMonth: 50000,
        maxWorkflowExecutionsPerMonth: 5000
      };
    case 'ENTERPRISE':
      return {
        maxProjects: -1, // Unlimited
        maxMembers: -1, // Unlimited
        maxStorageGB: 1000,
        maxFileSize: 1000, // MB
        maxApiCallsPerMonth: -1, // Unlimited
        maxWorkflowExecutionsPerMonth: -1 // Unlimited
      };
    default:
      return getTierLimits('FREE');
  }
}

function validateSettingsForTier(settings: any, tier: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const tierLimits = getTierLimits(tier);

  // Validate feature access based on tier
  if (settings.features) {
    if (tier === 'FREE') {
      if (settings.features.advancedWorkflows) {
        errors.push('Advanced workflows not available in FREE tier');
      }
      if (settings.features.customBranding) {
        errors.push('Custom branding not available in FREE tier');
      }
      if (settings.features.apiAccess) {
        errors.push('API access not available in FREE tier');
      }
    }
  }

  // Validate limits
  if (settings.limits) {
    Object.entries(settings.limits).forEach(([key, value]) => {
      const tierLimit = tierLimits[key];
      if (tierLimit !== -1 && (value as number) > tierLimit) {
        errors.push(`${key} exceeds tier limit of ${tierLimit}`);
      }
    });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

function deepMergeSettings(current: any, updates: any): any {
  const result = { ...current };
  
  for (const key in updates) {
    if (key === 'organizationId') continue; // Skip organizationId
    
    if (updates[key] !== null && typeof updates[key] === 'object' && !Array.isArray(updates[key])) {
      result[key] = deepMergeSettings(current[key] || {}, updates[key]);
    } else {
      result[key] = updates[key];
    }
  }
  
  return result;
}

// Register functions
app.http('organization-settings-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}/settings',
  handler: getOrganizationSettings
});

app.http('organization-settings-update', {
  methods: ['PUT', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/settings',
  handler: updateOrganizationSettings
});
