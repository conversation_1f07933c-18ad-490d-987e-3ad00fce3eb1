/**
 * Event Grid Storage Events Trigger
 * Native Event Grid trigger for Azure Storage events
 */

import { app, EventGridEvent, InvocationContext } from '@azure/functions';
import { logger } from '../shared/utils/logger';
import { db } from '../shared/services/database';
import { eventGridIntegration } from '../shared/services/event-grid-integration';

// Import EventType enum directly to avoid circular dependency
enum EventType {
  DOCUMENT_UPLOADED = 'Document.Uploaded',
  DOCUMENT_PROCESSED = 'Document.Processed',
  DOCUMENT_SHARED = 'Document.Shared',
  WORKFLOW_STARTED = 'Workflow.Started',
  WORKFLOW_COMPLETED = 'Workflow.Completed',
  USER_REGISTERED = 'User.Registered',
  NOTIFICATION_SENT = 'Notification.Sent',
  ANALYTICS_GENERATED = 'Analytics.Generated',
  SYSTEM_HEALTH_CHECK = 'System.HealthCheck',
  PERFORMANCE_ALERT = 'Performance.Alert'
}

/**
 * Publish event using Event Grid integration service
 */
async function publishEvent(eventType: EventType, subject: string, data: any): Promise<void> {
  try {
    await eventGridIntegration.publishEvent({
      eventType,
      subject,
      data
    });
  } catch (error) {
    logger.error('Failed to publish event from storage trigger', {
      eventType,
      subject,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Handle Storage Event Grid events with native trigger
 */
async function storageEventGridTrigger(event: EventGridEvent, context: InvocationContext): Promise<void> {
  logger.info('Storage Event Grid trigger activated', {
    eventType: event.eventType,
    subject: event.subject,
    eventId: event.id,
    invocationId: context.invocationId
  });

  try {
    await processStorageEvent(event);
    logger.info('Storage event processed successfully', {
      eventId: event.id,
      eventType: event.eventType
    });
  } catch (error) {
    logger.error('Error processing storage event', {
      eventId: event.id,
      eventType: event.eventType,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error; // Re-throw to trigger retry mechanism
  }
}

/**
 * Process storage events
 */
async function processStorageEvent(event: EventGridEvent): Promise<void> {
  logger.info('Processing storage event', {
    eventType: event.eventType,
    subject: event.subject,
    eventId: event.id
  });

  switch (event.eventType) {
    case 'Microsoft.Storage.BlobCreated':
      await handleBlobCreatedEvent(event);
      break;
    case 'Microsoft.Storage.BlobDeleted':
      await handleBlobDeletedEvent(event);
      break;
    default:
      logger.info('Unhandled storage event type', { eventType: event.eventType });
  }
}

/**
 * Handle blob created event
 */
async function handleBlobCreatedEvent(event: EventGridEvent): Promise<void> {
  const blobUrl = event.subject || '';
  const blobName = blobUrl.split('/').pop() || 'unknown';

  logger.info('Blob created event received', { blobName, blobUrl });

  try {
    // Trigger document processing if it's in the documents container
    if (blobUrl.includes('/documents/')) {
      await publishEvent(
        EventType.DOCUMENT_UPLOADED,
        `documents/${blobName}`,
        {
          blobName,
          blobUrl,
          timestamp: new Date().toISOString(),
          autoProcessing: true,
          eventSource: 'storage-trigger'
        }
      );

      // Update document status in database if document exists
      try {
        const documents = await db.queryItems<any>('documents',
          'SELECT * FROM c WHERE c.fileName = @fileName',
          [{ name: '@fileName', value: blobName }]
        );

        for (const doc of documents) {
          await db.updateItem('documents', {
            ...doc,
            status: 'uploaded',
            blobUrl: blobUrl,
            uploadedAt: new Date().toISOString()
          });
        }
      } catch (dbError) {
        logger.warn('Could not update document in database', {
          blobName,
          error: dbError instanceof Error ? dbError.message : String(dbError)
        });
      }
    }

    // Handle processed files
    if (blobUrl.includes('/processed/')) {
      await publishEvent(
        EventType.DOCUMENT_PROCESSED,
        `processed/${blobName}`,
        {
          blobName,
          blobUrl,
          timestamp: new Date().toISOString(),
          eventSource: 'storage-trigger'
        }
      );
    }

    // Handle thumbnails
    if (blobUrl.includes('/thumbnails/')) {
      logger.info('Thumbnail created', { blobName, blobUrl });
    }

  } catch (error) {
    logger.error('Error handling blob created event', {
      blobName,
      blobUrl,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * Handle blob deleted event
 */
async function handleBlobDeletedEvent(event: EventGridEvent): Promise<void> {
  const blobUrl = event.subject;
  const blobName = blobUrl.split('/').pop();

  logger.info('Blob deleted event received', { blobName, blobUrl });

  try {
    // Update document status in database
    const documents = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.fileName = @fileName OR c.blobName = @blobName',
      [
        { name: '@fileName', value: blobName },
        { name: '@blobName', value: blobName }
      ]
    );

    for (const doc of documents) {
      await db.updateItem('documents', {
        ...doc,
        status: 'deleted',
        deletedAt: new Date().toISOString()
      });
    }

    // Publish deletion event
    if (documents.length > 0) {
      await publishEvent(
        EventType.DOCUMENT_PROCESSED,
        `documents/${blobName}/deleted`,
        {
          blobName,
          blobUrl,
          documentIds: documents.map(d => d.id),
          timestamp: new Date().toISOString(),
          eventSource: 'storage-trigger'
        }
      );
    }

  } catch (error) {
    logger.error('Error handling blob deleted event', {
      blobName,
      blobUrl,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

// Register the Event Grid trigger
app.eventGrid('storageEventGridTrigger', {
  handler: storageEventGridTrigger
});
