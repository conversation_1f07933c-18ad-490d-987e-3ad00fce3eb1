/**
 * Workflow Templates Function
 * Handles workflow template management (create, read, update, delete)
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const createTemplateSchema = Joi.object({
  name: Joi.string().required().max(100),
  description: Joi.string().max(500).optional(),
  projectId: Joi.string().uuid().required(),
  definition: Joi.object({
    steps: Joi.array().items(
      Joi.object({
        name: Joi.string().required(),
        description: Joi.string().optional(),
        type: Joi.string().valid('approval', 'review', 'task', 'notification').required(),
        order: Joi.number().integer().min(1).required(),
        assigneeRole: Joi.string().optional(),
        durationDays: Joi.number().integer().min(1).optional(),
        isRequired: Joi.boolean().default(true),
        conditions: Joi.object().optional()
      })
    ).min(1).required(),
    settings: Joi.object({
      autoStart: Joi.boolean().default(false),
      allowParallel: Joi.boolean().default(false),
      notifyOnCompletion: Joi.boolean().default(true)
    }).optional()
  }).required(),
  isDefault: Joi.boolean().default(false),
  documentTypes: Joi.array().items(Joi.string()).optional(),
  organizationId: Joi.string().uuid().required()
});

const updateTemplateSchema = Joi.object({
  name: Joi.string().max(100).optional(),
  description: Joi.string().max(500).optional(),
  definition: Joi.object().optional(),
  isDefault: Joi.boolean().optional(),
  documentTypes: Joi.array().items(Joi.string()).optional(),
  isActive: Joi.boolean().optional()
});

const getTemplatesSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  projectId: Joi.string().uuid().optional(),
  isActive: Joi.boolean().optional(),
  isDefault: Joi.boolean().optional()
});

interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  projectId: string;
  organizationId: string;
  definition: {
    steps: Array<{
      name: string;
      description?: string;
      type: string;
      order: number;
      assigneeRole?: string;
      durationDays?: number;
      isRequired: boolean;
      conditions?: any;
    }>;
    settings?: {
      autoStart: boolean;
      allowParallel: boolean;
      notifyOnCompletion: boolean;
    };
  };
  isDefault: boolean;
  isActive: boolean;
  documentTypes: string[];
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  tenantId?: string;
}

/**
 * Create workflow template handler
 */
export async function createWorkflowTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create workflow template started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createTemplateSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const templateData = value;

    // Check if this is set as default and update other templates if needed
    if (templateData.isDefault) {
      const query = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.isDefault = true';
      const existingDefaultTemplates = await db.queryItems('workflow-templates', query, [templateData.projectId]);

      // Update existing default templates to not be default
      for (const template of existingDefaultTemplates) {
        const updatedTemplate = {
          ...(template as any),
          id: (template as any).id,
          isDefault: false,
          updatedBy: user.id,
          updatedAt: new Date().toISOString()
        };
        await db.updateItem('workflow-templates', updatedTemplate);
      }
    }

    // Create template
    const templateId = uuidv4();
    const template: WorkflowTemplate = {
      id: templateId,
      name: templateData.name,
      description: templateData.description || "",
      projectId: templateData.projectId,
      organizationId: templateData.organizationId,
      definition: templateData.definition,
      isDefault: templateData.isDefault || false,
      isActive: true,
      documentTypes: templateData.documentTypes || [],
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      tenantId: user.tenantId
    };

    await db.createItem('workflow-templates', template);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "workflow_template_created",
      userId: user.id,
      organizationId: templateData.organizationId,
      projectId: templateData.projectId,
      templateId: templateId,
      timestamp: new Date().toISOString(),
      details: {
        templateName: template.name,
        isDefault: template.isDefault,
        stepsCount: template.definition.steps.length
      },
      tenantId: user.tenantId
    });

    logger.info("Workflow template created successfully", {
      correlationId,
      templateId,
      userId: user.id,
      projectId: templateData.projectId
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: template.id,
        name: template.name,
        description: template.description,
        projectId: template.projectId,
        organizationId: template.organizationId,
        definition: template.definition,
        isDefault: template.isDefault,
        isActive: template.isActive,
        documentTypes: template.documentTypes,
        createdBy: template.createdBy,
        createdAt: template.createdAt,
        updatedBy: template.updatedBy,
        updatedAt: template.updatedAt,
        _links: {
          self: { href: `/workflow-templates/${templateId}` },
          createWorkflow: { href: `/workflows?templateId=${templateId}` }
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create workflow template failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get workflow template handler
 */
export async function getWorkflowTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const templateId = request.params.id;

  if (!templateId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Template ID is required' }
    }, request);
  }

  logger.info("Get workflow template started", { correlationId, templateId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get template
    const template = await db.readItem('workflow-templates', templateId, templateId);
    if (!template) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Template not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (template as any).createdBy === user.id ||
      (template as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    logger.info("Workflow template retrieved successfully", {
      correlationId,
      templateId,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        ...(template as any),
        _links: {
          self: { href: `/workflow-templates/${templateId}` },
          createWorkflow: { href: `/workflows?templateId=${templateId}` },
          update: { href: `/workflow-templates/${templateId}` },
          delete: { href: `/workflow-templates/${templateId}` }
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get workflow template failed", {
      correlationId,
      templateId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * List workflow templates handler
 */
export async function listWorkflowTemplates(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List workflow templates started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getTemplatesSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, projectId, isActive, isDefault } = value;

    // Build query
    let query = 'SELECT * FROM c WHERE 1=1';
    const parameters: any[] = [];

    // Add tenant isolation
    if (user.tenantId) {
      query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
      parameters.push(user.tenantId, user.id);
    }

    // Add filters
    if (projectId) {
      query += ' AND c.projectId = @projectId';
      parameters.push(projectId);
    }

    if (isActive !== undefined) {
      query += ' AND c.isActive = @isActive';
      parameters.push(isActive);
    }

    if (isDefault !== undefined) {
      query += ' AND c.isDefault = @isDefault';
      parameters.push(isDefault);
    }

    query += ' ORDER BY c.createdAt DESC';

    // Execute query with pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${query} OFFSET ${offset} LIMIT ${limit}`;

    const templates = await db.queryItems('workflow-templates', paginatedQuery, parameters);

    // Get total count
    const countQuery = query.replace('SELECT * FROM c', 'SELECT VALUE COUNT(1) FROM c');
    const countResult = await db.queryItems('workflow-templates', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    logger.info("Workflow templates retrieved successfully", {
      correlationId,
      userId: user.id,
      count: templates.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        templates,
        pagination: {
          page,
          limit,
          total,
          hasMore: (page * limit) < total
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List workflow templates failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Combined workflow templates handler
 */
async function handleWorkflowTemplates(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await createWorkflowTemplate(request, context);
    case 'GET':
      return await listWorkflowTemplates(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('workflow-templates', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflow-templates',
  handler: handleWorkflowTemplates
});

app.http('workflow-templates-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflow-templates/{id}',
  handler: getWorkflowTemplate
});
