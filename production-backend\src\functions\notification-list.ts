/**
 * Notification List Function
 * Handles listing notifications for the authenticated user
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schema
const listNotificationsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  isRead: Joi.boolean().optional(),
  type: Joi.string().optional(),
  priority: Joi.string().valid('LOW', 'MEDIUM', 'HIGH', 'URGENT').optional(),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional(),
  includeExpired: Joi.boolean().default(false)
});

/**
 * List notifications handler
 */
export async function listNotifications(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List notifications started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listNotificationsSchema.validate(queryParams);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, isRead, type, priority, organizationId, projectId, includeExpired } = value;

    // Build query to get notifications for the user
    let queryText = 'SELECT * FROM c WHERE c.recipientId = @userId';
    const parameters: any[] = [user.id];

    // Add tenant isolation
    if (user.tenantId) {
      queryText += ' AND (c.tenantId = @tenantId OR c.tenantId IS NULL)';
      parameters.push(user.tenantId);
    }

    // Filter by read status if provided
    if (isRead !== undefined) {
      queryText += ' AND c.isRead = @isRead';
      parameters.push(isRead);
    }

    // Filter by type if provided
    if (type) {
      queryText += ' AND c.type = @type';
      parameters.push(type);
    }

    // Filter by priority if provided
    if (priority) {
      queryText += ' AND c.priority = @priority';
      parameters.push(priority);
    }

    // Filter by organization if provided
    if (organizationId) {
      queryText += ' AND c.organizationId = @organizationId';
      parameters.push(organizationId);
    }

    // Filter by project if provided
    if (projectId) {
      queryText += ' AND c.projectId = @projectId';
      parameters.push(projectId);
    }

    // Filter out expired notifications unless explicitly requested
    if (!includeExpired) {
      queryText += ' AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
      parameters.push(new Date().toISOString());
    }

    // Add ordering (unread first, then by creation date)
    queryText += ' ORDER BY c.isRead ASC, c.createdAt DESC';

    // Get total count for pagination
    const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
    const countResult = await db.queryItems('notifications', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    // Add pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;

    // Execute query
    const notifications = await db.queryItems('notifications', paginatedQuery, parameters);

    // Enrich notifications with sender information
    const enrichedNotifications = await Promise.all(
      notifications.map(async (notification: any) => {
        let senderName = 'System';
        let senderEmail = '';

        if (notification.senderId) {
          try {
            const sender = await db.readItem('users', notification.senderId, notification.senderId);
            if (sender) {
              senderName = (sender as any).name || (sender as any).email || 'Unknown User';
              senderEmail = (sender as any).email || '';
            }
          } catch (error) {
            // Sender might not exist anymore
          }
        }

        return {
          ...notification,
          senderName,
          senderEmail,
          isExpired: notification.expiresAt ? new Date(notification.expiresAt) < new Date() : false
        };
      })
    );

    // Get unread count for the user
    const unreadCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.recipientId = @userId AND c.isRead = false AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
    const unreadCountResult = await db.queryItems('notifications', unreadCountQuery, [user.id, new Date().toISOString()]);
    const unreadCount = Number(unreadCountResult[0]) || 0;

    logger.info("Notifications listed successfully", {
      correlationId,
      userId: user.id,
      count: notifications.length,
      unreadCount,
      page,
      limit
    });

    // Create response
    const response = {
      items: enrichedNotifications,
      total,
      unreadCount,
      page,
      limit,
      hasMore: page * limit < total
    };

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List notifications failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('notification-list', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications',
  handler: listNotifications
});
