/**
 * Project Create Function
 * Handles creating new projects within organizations
 * Enhanced with notification service and event system integration
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { listProjects } from './project-list';
import { notificationService } from '../shared/services/notification';

import { redis } from '../shared/services/redis';
import { serviceBusEnhanced } from '../shared/services/service-bus';
import { publishEvent, EventType } from './event-grid-handlers';

// Project visibility enum
enum ProjectVisibility {
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION',
  PUBLIC = 'PUBLIC'
}

// User roles enum
enum UserRole {
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
  VIEWER = 'VIEWER'
}

// Validation schema
const createProjectSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  organizationId: Joi.string().uuid().required(),
  visibility: Joi.string().valid(...Object.values(ProjectVisibility)).default(ProjectVisibility.PRIVATE),
  tags: Joi.array().items(Joi.string().max(50)).max(10).default([])
});

interface Project {
  id: string;
  organizationId: string;
  name: string;
  description: string;
  visibility: ProjectVisibility;
  tags: string[];
  documentIds: string[];
  workflowIds: string[];
  memberIds: string[];
  teamIds: string[];
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  settings: {
    defaultDocumentTags: string[];
    defaultWorkflowId: string | null;
    autoProcessing: boolean;
  };
  tenantId?: string;
}

/**
 * Create project handler
 */
export async function createProject(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create project started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createProjectSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { name, description, organizationId, visibility, tags } = value;

    // Check if organization exists and user has access
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user is a member of the organization
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "You must be a member of the organization to create projects" }
      }, request);
    }

    // Check project limit for organization
    const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
    const projectCountResult = await db.queryItems('projects', projectCountQuery, [organizationId]);
    const projectCount = Number(projectCountResult[0]) || 0;

    const maxProjects = (organization as any).settings?.maxProjects || 3;
    if (projectCount >= maxProjects) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: "Project limit reached for this organization tier",
          limit: maxProjects,
          current: projectCount
        }
      }, request);
    }

    // Create project
    const projectId = uuidv4();
    const project: Project = {
      id: projectId,
      organizationId,
      name,
      description: description || "",
      visibility,
      tags,
      documentIds: [],
      workflowIds: [],
      memberIds: [user.id],
      teamIds: [],
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      settings: {
        defaultDocumentTags: [],
        defaultWorkflowId: null,
        autoProcessing: true
      },
      tenantId: user.tenantId
    };

    await db.createItem('projects', project);

    // Add project to organization's project list
    const updatedOrganization = {
      ...(organization as any),
      id: organizationId,
      projectIds: [...((organization as any).projectIds || []), projectId],
      updatedAt: new Date().toISOString(),
      updatedBy: user.id
    };
    await db.updateItem('organizations', updatedOrganization);

    // Create project membership for creator
    const projectMembership = {
      id: uuidv4(),
      userId: user.id,
      projectId,
      organizationId,
      role: UserRole.ADMIN,
      joinedAt: new Date().toISOString(),
      invitedBy: user.id,
      permissions: [],
      tenantId: user.tenantId
    };
    await db.createItem('project-members', projectMembership);

    // Cache project data in Redis
    const projectWithStats = {
      ...project,
      documentCount: 0,
      workflowCount: 0,
      memberCount: 1,
      storageUsed: 0,
      organizationName: (organization as any).name,
      userRole: 'admin'
    };
    await redis.setJson(`project:${projectId}:details`, projectWithStats, 1800); // 30 minutes cache

    // Invalidate organization cache
    await redis.del(`org:${organizationId}:details`);
    await redis.del(`user:${user.id}:projects`);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "project_created",
      userId: user.id,
      organizationId,
      projectId,
      timestamp: new Date().toISOString(),
      details: {
        projectName: name,
        visibility,
        organizationName: (organization as any).name
      },
      tenantId: user.tenantId
    });

    // Publish Event Grid event
    await publishEvent(
      EventType.PROJECT_CREATED,
      `projects/${projectId}/created`,
      {
        projectId,
        projectName: name,
        organizationId,
        organizationName: (organization as any).name,
        visibility,
        createdBy: user.id,
        memberCount: 1,
        timestamp: new Date().toISOString()
      }
    );

    // Send Service Bus message for workflow orchestration
    await serviceBusEnhanced.sendToQueue('analytics-events', {
      body: {
        eventType: 'project_created',
        projectId,
        projectName: name,
        organizationId,
        organizationName: (organization as any).name,
        visibility,
        createdBy: user.id,
        timestamp: new Date().toISOString()
      },
      messageId: `project-create-${projectId}-${Date.now()}`,
      correlationId: `project-${projectId}`,
      subject: 'project.created'
    });

    // Send notification
    await notificationService.sendNotification({
      userId: user.id,
      type: 'PROJECT_CREATED',
      title: 'Project created successfully!',
      message: `Your project "${name}" has been created in ${(organization as any).name}. You can now start adding documents and workflows.`,
      priority: 'normal',
      metadata: {
        projectId,
        projectName: name,
        organizationId,
        organizationName: (organization as any).name,
        visibility
      },
      organizationId,
      projectId
    });

    logger.info("Project created successfully", {
      correlationId,
      projectId,
      userId: user.id,
      organizationId,
      visibility
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: projectId,
        name,
        organizationId,
        visibility,
        message: "Project created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create project failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Combined projects handler
 */
async function handleProjects(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await createProject(request, context);
    case 'GET':
      return await listProjects(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('projects', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects',
  handler: handleProjects
});
