/**
 * Project Members Management Function
 * Handles project member management, role assignments, and permissions
 * Migrated from old-arch/src/project-service/members/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Project member types and enums
enum ProjectRole {
  OWNER = 'OWNER',
  MANAGER = 'MANAGER',
  CONTRIBUTOR = 'CONTRIBUTOR',
  VIEWER = 'VIEWER'
}

enum MemberStatus {
  ACTIVE = 'ACTIVE',
  INVITED = 'INVITED',
  SUSPENDED = 'SUSPENDED',
  REMOVED = 'REMOVED'
}

// Validation schemas
const addMemberSchema = Joi.object({
  projectId: Joi.string().uuid().required(),
  userId: Joi.string().uuid().optional(),
  email: Joi.string().email().optional(),
  role: Joi.string().valid(...Object.values(ProjectRole)).default(ProjectRole.CONTRIBUTOR),
  permissions: Joi.array().items(Joi.string()).optional(),
  message: Joi.string().max(500).optional(),
  expiresAt: Joi.string().isoDate().optional()
}).xor('userId', 'email');

const updateMemberSchema = Joi.object({
  projectId: Joi.string().uuid().required(),
  memberId: Joi.string().uuid().required(),
  role: Joi.string().valid(...Object.values(ProjectRole)).optional(),
  permissions: Joi.array().items(Joi.string()).optional(),
  status: Joi.string().valid(...Object.values(MemberStatus)).optional()
});

const removeMemberSchema = Joi.object({
  projectId: Joi.string().uuid().required(),
  memberId: Joi.string().uuid().required(),
  reason: Joi.string().max(500).optional()
});

interface AddMemberRequest {
  projectId: string;
  userId?: string;
  email?: string;
  role: ProjectRole;
  permissions?: string[];
  message?: string;
  expiresAt?: string;
}

interface UpdateMemberRequest {
  projectId: string;
  memberId: string;
  role?: ProjectRole;
  permissions?: string[];
  status?: MemberStatus;
}

interface RemoveMemberRequest {
  projectId: string;
  memberId: string;
  reason?: string;
}

/**
 * Get project members handler
 */
export async function getProjectMembers(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const projectId = request.params.projectId;

  logger.info("Get project members started", { correlationId, projectId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!projectId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Project ID is required" }
      }, request);
    }

    // Verify project exists and user has access
    const project = await db.readItem('projects', projectId, projectId);
    if (!project) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Project not found" }
      }, request);
    }

    // Check if user has permission to view project members
    const hasAccess = await checkProjectAccess(projectId, user.id, 'VIEW');
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to project" }
      }, request);
    }

    // Get project members
    const membersQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.status != @removedStatus';
    const members = await db.queryItems('project-members', membersQuery, [projectId, MemberStatus.REMOVED]);

    // Enrich members with user information
    const enrichedMembers = await Promise.all(
      members.map(async (member: any) => {
        const userInfo = await db.readItem('users', member.userId, member.userId);
        return {
          id: member.id,
          userId: member.userId,
          projectId: member.projectId,
          role: member.role,
          permissions: member.permissions || [],
          status: member.status,
          joinedAt: member.createdAt,
          expiresAt: member.expiresAt,
          addedBy: member.addedBy,
          user: userInfo ? {
            id: (userInfo as any).id,
            email: (userInfo as any).email,
            displayName: (userInfo as any).displayName,
            firstName: (userInfo as any).firstName,
            lastName: (userInfo as any).lastName,
            avatarUrl: (userInfo as any).avatarUrl
          } : null
        };
      })
    );

    // Parse query parameters for filtering
    const url = new URL(request.url);
    const roleFilter = url.searchParams.get('role');
    const statusFilter = url.searchParams.get('status');

    // Apply filters
    let filteredMembers = enrichedMembers;
    if (roleFilter) {
      filteredMembers = filteredMembers.filter(m => m.role === roleFilter);
    }
    if (statusFilter) {
      filteredMembers = filteredMembers.filter(m => m.status === statusFilter);
    }

    logger.info("Project members retrieved successfully", {
      correlationId,
      projectId,
      memberCount: filteredMembers.length,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        projectId,
        members: filteredMembers,
        totalCount: filteredMembers.length,
        filters: {
          role: roleFilter,
          status: statusFilter
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get project members failed", {
      correlationId,
      projectId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Add project member handler
 */
export async function addProjectMember(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Add project member started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = addMemberSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const memberRequest: AddMemberRequest = value;

    // Verify project exists and user has permission to add members
    const project = await db.readItem('projects', memberRequest.projectId, memberRequest.projectId);
    if (!project) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Project not found" }
      }, request);
    }

    const hasPermission = await checkProjectAccess(memberRequest.projectId, user.id, 'MANAGE_MEMBERS');
    if (!hasPermission) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to add project members" }
      }, request);
    }

    // Resolve user ID if email is provided
    let targetUserId = memberRequest.userId;
    if (memberRequest.email && !targetUserId) {
      const userQuery = 'SELECT * FROM c WHERE c.email = @email';
      const users = await db.queryItems('users', userQuery, [memberRequest.email.toLowerCase()]);
      if (users.length > 0) {
        targetUserId = (users[0] as any).id;
      }
    }

    // Check if user is already a member
    if (targetUserId) {
      const existingMemberQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status != @removedStatus';
      const existingMembers = await db.queryItems('project-members', existingMemberQuery, [memberRequest.projectId, targetUserId, MemberStatus.REMOVED]);

      if (existingMembers.length > 0) {
        return addCorsHeaders({
          status: 409,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "User is already a member of this project" }
        }, request);
      }
    }

    // Check member limits for organization tier
    const projectData = project as any;
    if (await isMemberLimitReached(memberRequest.projectId, projectData.organizationId)) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Project member limit reached for this organization tier" }
      }, request);
    }

    // Create project member record
    const memberId = uuidv4();
    const now = new Date().toISOString();

    const projectMember = {
      id: memberId,
      projectId: memberRequest.projectId,
      userId: targetUserId,
      email: memberRequest.email,
      role: memberRequest.role,
      permissions: memberRequest.permissions || getDefaultRolePermissions(memberRequest.role),
      status: targetUserId ? MemberStatus.ACTIVE : MemberStatus.INVITED,
      expiresAt: memberRequest.expiresAt,
      addedBy: user.id,
      createdAt: now,
      updatedAt: now,
      organizationId: projectData.organizationId,
      tenantId: user.tenantId
    };

    await db.createItem('project-members', projectMember);

    // Update project member count
    const updatedProject = {
      ...projectData,
      memberIds: [...(projectData.memberIds || []), targetUserId || memberRequest.email],
      metadata: {
        ...projectData.metadata,
        memberCount: (projectData.metadata?.memberCount || 0) + 1
      },
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('projects', updatedProject);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "project_member_added",
      userId: user.id,
      organizationId: projectData.organizationId,
      projectId: memberRequest.projectId,
      timestamp: now,
      details: {
        memberId,
        targetUserId,
        targetEmail: memberRequest.email,
        role: memberRequest.role,
        projectName: projectData.name,
        isInvitation: !targetUserId
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'ProjectMemberAdded',
      aggregateId: memberId,
      aggregateType: 'ProjectMember',
      version: 1,
      data: {
        member: projectMember,
        project: projectData,
        addedBy: user.id
      },
      userId: user.id,
      organizationId: projectData.organizationId,
      tenantId: user.tenantId
    });

    // Send notification
    if (targetUserId) {
      await notificationService.sendNotification({
        userId: targetUserId,
        type: 'PROJECT_MEMBER_ADDED',
        title: 'Added to project',
        message: `You have been added to the project "${projectData.name}" as ${memberRequest.role}.`,
        priority: 'normal',
        metadata: {
          projectId: memberRequest.projectId,
          projectName: projectData.name,
          role: memberRequest.role,
          addedBy: user.id,
          organizationId: projectData.organizationId
        },
        organizationId: projectData.organizationId,
        projectId: memberRequest.projectId
      });
    }

    logger.info("Project member added successfully", {
      correlationId,
      memberId,
      projectId: memberRequest.projectId,
      targetUserId,
      targetEmail: memberRequest.email,
      role: memberRequest.role,
      addedBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: memberId,
        projectId: memberRequest.projectId,
        projectName: projectData.name,
        userId: targetUserId,
        email: memberRequest.email,
        role: memberRequest.role,
        permissions: projectMember.permissions,
        status: projectMember.status,
        addedBy: user.id,
        addedAt: now,
        message: "Project member added successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Add project member failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update project member handler
 */
export async function updateProjectMember(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update project member started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updateMemberSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const updateRequest: UpdateMemberRequest = value;

    // Verify project and member exist
    const [project, member] = await Promise.all([
      db.readItem('projects', updateRequest.projectId, updateRequest.projectId),
      db.readItem('project-members', updateRequest.memberId, updateRequest.memberId)
    ]);

    if (!project) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Project not found" }
      }, request);
    }

    if (!member) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Project member not found" }
      }, request);
    }

    // Check permissions
    const hasPermission = await checkProjectAccess(updateRequest.projectId, user.id, 'MANAGE_MEMBERS');
    if (!hasPermission) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to update project members" }
      }, request);
    }

    const memberData = member as any;
    const projectData = project as any;
    const now = new Date().toISOString();

    // Update member
    const updatedMember = {
      ...memberData,
      role: updateRequest.role || memberData.role,
      permissions: updateRequest.permissions || memberData.permissions,
      status: updateRequest.status || memberData.status,
      updatedAt: now,
      updatedBy: user.id
    };

    await db.updateItem('project-members', updatedMember);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "project_member_updated",
      userId: user.id,
      organizationId: projectData.organizationId,
      projectId: updateRequest.projectId,
      timestamp: now,
      details: {
        memberId: updateRequest.memberId,
        targetUserId: memberData.userId,
        previousRole: memberData.role,
        newRole: updatedMember.role,
        previousStatus: memberData.status,
        newStatus: updatedMember.status,
        projectName: projectData.name
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'ProjectMemberUpdated',
      aggregateId: updateRequest.memberId,
      aggregateType: 'ProjectMember',
      version: 1,
      data: {
        member: updatedMember,
        previousData: memberData,
        project: projectData,
        updatedBy: user.id
      },
      userId: user.id,
      organizationId: projectData.organizationId,
      tenantId: user.tenantId
    });

    // Send notification if role changed
    if (updateRequest.role && updateRequest.role !== memberData.role && memberData.userId) {
      await notificationService.sendNotification({
        userId: memberData.userId,
        type: 'PROJECT_ROLE_UPDATED',
        title: 'Project role updated',
        message: `Your role in project "${projectData.name}" has been updated to ${updateRequest.role}.`,
        priority: 'normal',
        metadata: {
          projectId: updateRequest.projectId,
          projectName: projectData.name,
          previousRole: memberData.role,
          newRole: updateRequest.role,
          updatedBy: user.id,
          organizationId: projectData.organizationId
        },
        organizationId: projectData.organizationId,
        projectId: updateRequest.projectId
      });
    }

    logger.info("Project member updated successfully", {
      correlationId,
      memberId: updateRequest.memberId,
      projectId: updateRequest.projectId,
      targetUserId: memberData.userId,
      roleChanged: updateRequest.role !== memberData.role,
      updatedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: updatedMember.id,
        projectId: updateRequest.projectId,
        projectName: projectData.name,
        userId: updatedMember.userId,
        role: updatedMember.role,
        permissions: updatedMember.permissions,
        status: updatedMember.status,
        updatedBy: user.id,
        updatedAt: now,
        message: "Project member updated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update project member failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkProjectAccess(projectId: string, userId: string, action: string): Promise<boolean> {
  try {
    // Check if user is a member of the project
    const membershipQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('project-members', membershipQuery, [projectId, userId, MemberStatus.ACTIVE]);

    if (memberships.length === 0) {
      return false;
    }

    const membership = memberships[0] as any;

    // Check role-based permissions
    switch (action) {
      case 'VIEW':
        return true; // All members can view
      case 'MANAGE_MEMBERS':
        return membership.role === ProjectRole.OWNER || membership.role === ProjectRole.MANAGER;
      default:
        return false;
    }
  } catch (error) {
    logger.error('Failed to check project access', { error, projectId, userId, action });
    return false;
  }
}

async function isMemberLimitReached(projectId: string, organizationId: string): Promise<boolean> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) return false;

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Get current member count
    const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId AND c.status != @removedStatus';
    const result = await db.queryItems('project-members', memberCountQuery, [projectId, MemberStatus.REMOVED]);
    const currentCount = Number(result[0]) || 0;

    // Define tier limits
    const limits: { [key: string]: number } = {
      'FREE': 5,
      'PROFESSIONAL': 50,
      'ENTERPRISE': -1 // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];
    return limit > 0 && currentCount >= limit;

  } catch (error) {
    logger.error('Failed to check member limit', { error, projectId, organizationId });
    return false;
  }
}

function getDefaultRolePermissions(role: ProjectRole): string[] {
  switch (role) {
    case ProjectRole.OWNER:
      return ['*']; // All permissions
    case ProjectRole.MANAGER:
      return ['manage_project', 'manage_members', 'create_workflow', 'manage_workflow'];
    case ProjectRole.CONTRIBUTOR:
      return ['view_project', 'upload_document', 'update_document', 'execute_workflow'];
    case ProjectRole.VIEWER:
      return ['view_project', 'view_document', 'comment_document'];
    default:
      return ['view_project'];
  }
}

// Register functions
app.http('project-members-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/{projectId}/members',
  handler: getProjectMembers
});

app.http('project-members-add', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/members',
  handler: addProjectMember
});

app.http('project-members-update', {
  methods: ['PUT', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/members/update',
  handler: updateProjectMember
});
