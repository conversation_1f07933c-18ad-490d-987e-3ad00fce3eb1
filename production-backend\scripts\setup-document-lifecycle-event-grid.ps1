#!/usr/bin/env pwsh

# Azure Event Grid Setup for Complete Document Lifecycle
# This script creates all necessary Event Grid subscriptions for document-related functions

param(
    [string]$ResourceGroup = "docucontext",
    [string]$FunctionAppName = "hepzlogic",
    [string]$EventGridTopicName = "hepzeg",
    [string]$StorageSystemTopicName = "stdocucontex900520441468-events",
    [string]$SubscriptionId = "a7b6fcd5-038d-41ed-9d87-2098820fd014"
)

Write-Host "🚀 Setting up Event Grid subscriptions for Document Lifecycle" -ForegroundColor Green
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Yellow
Write-Host "Function App: $FunctionAppName" -ForegroundColor Yellow
Write-Host "Event Grid Topic: $EventGridTopicName" -ForegroundColor Yellow

# Get Function App URL
$functionAppUrl = "https://$FunctionAppName.azurewebsites.net"
Write-Host "Function App URL: $functionAppUrl" -ForegroundColor Cyan

# 1. Document Lifecycle Events Subscription
Write-Host "`n📄 Creating Document Lifecycle Events Subscription..." -ForegroundColor Blue

$documentLifecycleSubscription = "document-lifecycle-events"
az eventgrid event-subscription create `
    --name $documentLifecycleSubscription `
    --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Document.Uploaded" "Document.Processed" "Document.Shared" "Document.VersionCreated" "Document.VersionRestored" "Document.Archived" "Document.Deleted" `
    --max-delivery-attempts 3 `
    --event-ttl 1440 `
    --advanced-filter eventType StringIn Document.Uploaded Document.Processed Document.Shared Document.VersionCreated Document.VersionRestored Document.Archived Document.Deleted

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Document Lifecycle Events subscription created: $documentLifecycleSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Document Lifecycle Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 2. AI Document Analysis Events Subscription
Write-Host "`n🤖 Creating AI Document Analysis Events Subscription..." -ForegroundColor Blue

$aiAnalysisSubscription = "ai-document-analysis-events"
az eventgrid event-subscription create `
    --name $aiAnalysisSubscription `
    --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Document.AIAnalysisStarted" "Document.AIAnalysisCompleted" "Document.AIAnalysisFailed" `
    --max-delivery-attempts 3 `
    --event-ttl 1440 `
    --advanced-filter eventType StringIn Document.AIAnalysisStarted Document.AIAnalysisCompleted Document.AIAnalysisFailed

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ AI Document Analysis Events subscription created: $aiAnalysisSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ AI Document Analysis Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 3. Document Processing Events Subscription
Write-Host "`n⚙️ Creating Document Processing Events Subscription..." -ForegroundColor Blue

$processingSubscription = "document-processing-events"
az eventgrid event-subscription create `
    --name $processingSubscription `
    --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Document.ProcessingStarted" "Document.ProcessingCompleted" "Document.ProcessingFailed" "Document.TextExtracted" "Document.ThumbnailGenerated" `
    --max-delivery-attempts 3 `
    --event-ttl 1440 `
    --advanced-filter eventType StringIn Document.ProcessingStarted Document.ProcessingCompleted Document.ProcessingFailed Document.TextExtracted Document.ThumbnailGenerated

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Document Processing Events subscription created: $processingSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Document Processing Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 4. Storage Events Subscription (Blob Created/Deleted)
Write-Host "`n💾 Creating Storage Events Subscription..." -ForegroundColor Blue

$storageEventsSubscription = "storage-blob-events"
az eventgrid event-subscription create `
    --name $storageEventsSubscription `
    --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$StorageSystemTopicName" `
    --endpoint "$functionAppUrl/api/eventgrid/storage-webhook" `
    --endpoint-type webhook `
    --included-event-types "Microsoft.Storage.BlobCreated" "Microsoft.Storage.BlobDeleted" `
    --max-delivery-attempts 3 `
    --event-ttl 1440 `
    --subject-begins-with "/blobServices/default/containers/documents/"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Storage Events subscription created: $storageEventsSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Storage Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 5. Document Collaboration Events Subscription
Write-Host "`n👥 Creating Document Collaboration Events Subscription..." -ForegroundColor Blue

$collaborationSubscription = "document-collaboration-events"
az eventgrid event-subscription create `
    --name $collaborationSubscription `
    --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Document.Shared" "Document.ShareUpdated" "Document.ShareDeleted" "Document.CollaborationStarted" "Document.CollaborationEnded" `
    --max-delivery-attempts 3 `
    --event-ttl 1440 `
    --advanced-filter eventType StringIn Document.Shared Document.ShareUpdated Document.ShareDeleted Document.CollaborationStarted Document.CollaborationEnded

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Document Collaboration Events subscription created: $collaborationSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Document Collaboration Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 6. Document Search and Analytics Events Subscription
Write-Host "`n🔍 Creating Document Search and Analytics Events Subscription..." -ForegroundColor Blue

$searchAnalyticsSubscription = "document-search-analytics-events"
az eventgrid event-subscription create `
    --name $searchAnalyticsSubscription `
    --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "Search.Performed" "Analytics.Generated" "Document.Viewed" "Document.Downloaded" `
    --max-delivery-attempts 3 `
    --event-ttl 1440 `
    --advanced-filter eventType StringIn Search.Performed Analytics.Generated Document.Viewed Document.Downloaded

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Document Search and Analytics Events subscription created: $searchAnalyticsSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ Document Search and Analytics Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 7. System and Monitoring Events Subscription
Write-Host "`n📊 Creating System and Monitoring Events Subscription..." -ForegroundColor Blue

$systemMonitoringSubscription = "system-monitoring-events"
az eventgrid event-subscription create `
    --name $systemMonitoringSubscription `
    --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" `
    --endpoint "$functionAppUrl/api/eventgrid/webhook" `
    --endpoint-type webhook `
    --included-event-types "System.HealthCheck" "Performance.Alert" "System.MaintenanceStarted" "System.MaintenanceCompleted" `
    --max-delivery-attempts 3 `
    --event-ttl 1440 `
    --advanced-filter eventType StringIn System.HealthCheck Performance.Alert System.MaintenanceStarted System.MaintenanceCompleted

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ System and Monitoring Events subscription created: $systemMonitoringSubscription" -ForegroundColor Green
} else {
    Write-Host "⚠️ System and Monitoring Events subscription may already exist or failed to create" -ForegroundColor Yellow
}

# 8. List all Event Grid Subscriptions
Write-Host "`n📋 Listing all Event Grid Subscriptions..." -ForegroundColor Blue

Write-Host "`nCustom Topic Subscriptions:" -ForegroundColor Yellow
az eventgrid event-subscription list --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/topics/$EventGridTopicName" --query "[].{Name:name, Endpoint:destination.endpointUrl, EventTypes:filter.includedEventTypes}" --output table

Write-Host "`nStorage System Topic Subscriptions:" -ForegroundColor Yellow
az eventgrid event-subscription list --source-resource-id "/subscriptions/$SubscriptionId/resourceGroups/$ResourceGroup/providers/Microsoft.EventGrid/systemTopics/$StorageSystemTopicName" --query "[].{Name:name, Endpoint:destination.endpointUrl, EventTypes:filter.includedEventTypes}" --output table

Write-Host "`n✅ Event Grid setup for Document Lifecycle completed!" -ForegroundColor Green
Write-Host "📝 All document-related events will now be routed to the Function App for processing." -ForegroundColor Cyan
