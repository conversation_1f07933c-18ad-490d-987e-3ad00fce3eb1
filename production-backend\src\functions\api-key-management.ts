/**
 * API Key Management Function
 * Handles API key creation, management, and authentication
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// API key scopes enum
enum ApiKeyScope {
  READ = 'READ',
  WRITE = 'WRITE',
  DELETE = 'DELETE',
  ADMIN = 'ADMIN',
  DOCUMENTS_READ = 'DOCUMENTS_READ',
  DOCUMENTS_WRITE = 'DOCUMENTS_WRITE',
  WORKFLOWS_READ = 'WORKFLOWS_READ',
  WORKFLOWS_EXECUTE = 'WORKFLOWS_EXECUTE',
  ANALYTICS_READ = 'ANALYTICS_READ',
  WEBHOOKS_MANAGE = 'WEBHOOKS_MANAGE'
}

// Validation schemas
const createApiKeySchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  scopes: Joi.array().items(Joi.string().valid(...Object.values(ApiKeyScope))).min(1).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  expiresAt: Joi.date().iso().optional(),
  rateLimits: Joi.object({
    requestsPerMinute: Joi.number().integer().min(1).max(10000).default(1000),
    requestsPerHour: Joi.number().integer().min(1).max(100000).default(10000),
    requestsPerDay: Joi.number().integer().min(1).max(1000000).default(100000)
  }).optional(),
  allowedIps: Joi.array().items(Joi.string().ip()).optional(),
  allowedDomains: Joi.array().items(Joi.string().domain()).optional()
});


const listApiKeysSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  organizationId: Joi.string().uuid().required(),
  isActive: Joi.boolean().optional(),
  search: Joi.string().max(100).optional()
});

/**
 * Create API key handler
 */
export async function createApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create API key started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createApiKeySchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const apiKeyData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, apiKeyData.organizationId, 'active']);

    if (memberships.length === 0 || (memberships[0] as any).role !== 'ADMIN') {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization admins can create API keys" }
      }, request);
    }

    // Generate API key
    const apiKeyId = uuidv4();
    const keyValue = generateApiKey();
    const hashedKey = await hashApiKey(keyValue);

    // Create API key record
    const apiKey = {
      id: apiKeyId,
      name: apiKeyData.name,
      description: apiKeyData.description || "",
      keyHash: hashedKey,
      keyPrefix: keyValue.substring(0, 8) + '...',
      scopes: apiKeyData.scopes,
      organizationId: apiKeyData.organizationId,
      projectId: apiKeyData.projectId,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      expiresAt: apiKeyData.expiresAt,
      isActive: true,
      rateLimits: apiKeyData.rateLimits || {
        requestsPerMinute: 1000,
        requestsPerHour: 10000,
        requestsPerDay: 100000
      },
      allowedIps: apiKeyData.allowedIps || [],
      allowedDomains: apiKeyData.allowedDomains || [],
      usage: {
        totalRequests: 0,
        lastUsedAt: null,
        requestsToday: 0,
        requestsThisHour: 0,
        requestsThisMinute: 0
      },
      tenantId: user.tenantId
    };

    await db.createItem('api-keys', apiKey);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "api_key_created",
      userId: user.id,
      organizationId: apiKeyData.organizationId,
      projectId: apiKeyData.projectId,
      apiKeyId,
      timestamp: new Date().toISOString(),
      details: {
        apiKeyName: apiKey.name,
        scopes: apiKey.scopes,
        expiresAt: apiKey.expiresAt,
        rateLimits: apiKey.rateLimits
      },
      tenantId: user.tenantId
    });

    logger.info("API key created successfully", {
      correlationId,
      apiKeyId,
      userId: user.id,
      organizationId: apiKeyData.organizationId,
      scopes: apiKeyData.scopes
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: apiKeyId,
        name: apiKey.name,
        key: keyValue, // Only returned once during creation
        keyPrefix: apiKey.keyPrefix,
        scopes: apiKey.scopes,
        expiresAt: apiKey.expiresAt,
        rateLimits: apiKey.rateLimits,
        message: "API key created successfully. Save this key securely - it won't be shown again."
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create API key failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * List API keys handler
 */
export async function listApiKeys(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List API keys started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listApiKeysSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, organizationId, isActive, search } = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Build query
    let queryText = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const parameters: any[] = [organizationId];

    if (isActive !== undefined) {
      queryText += ' AND c.isActive = @isActive';
      parameters.push(isActive);
    }

    if (search) {
      queryText += ' AND (CONTAINS(LOWER(c.name), LOWER(@search)) OR CONTAINS(LOWER(c.description), LOWER(@search)))';
      parameters.push(search);
    }

    // Add ordering
    queryText += ' ORDER BY c.createdAt DESC';

    // Get total count
    const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
    const countResult = await db.queryItems('api-keys', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    // Add pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;

    // Execute query
    const apiKeys = await db.queryItems('api-keys', paginatedQuery, parameters);

    // Remove sensitive information
    const sanitizedApiKeys = apiKeys.map((apiKey: any) => {
      const sanitized = { ...apiKey };
      delete sanitized.keyHash;
      return sanitized;
    });

    logger.info("API keys listed successfully", {
      correlationId,
      userId: user.id,
      organizationId,
      count: apiKeys.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        organizationId,
        items: sanitizedApiKeys,
        total,
        page,
        limit,
        hasMore: page * limit < total
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List API keys failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Revoke API key handler
 */
export async function revokeApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const apiKeyId = request.params.apiKeyId;

  if (!apiKeyId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'API Key ID is required' }
    }, request);
  }

  logger.info("Revoke API key started", { correlationId, apiKeyId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get API key
    const apiKey = await db.readItem('api-keys', apiKeyId, apiKeyId);
    if (!apiKey) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "API key not found" }
      }, request);
    }

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, (apiKey as any).organizationId, 'active']);

    if (memberships.length === 0 || (memberships[0] as any).role !== 'ADMIN') {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization admins can revoke API keys" }
      }, request);
    }

    // Revoke API key
    const updatedApiKey = {
      ...(apiKey as any),
      id: apiKeyId,
      isActive: false,
      revokedBy: user.id,
      revokedAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('api-keys', updatedApiKey);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "api_key_revoked",
      userId: user.id,
      organizationId: (apiKey as any).organizationId,
      projectId: (apiKey as any).projectId,
      apiKeyId,
      timestamp: new Date().toISOString(),
      details: {
        apiKeyName: (apiKey as any).name,
        revokedBy: user.id
      },
      tenantId: user.tenantId
    });

    logger.info("API key revoked successfully", {
      correlationId,
      apiKeyId,
      userId: user.id,
      organizationId: (apiKey as any).organizationId
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: apiKeyId,
        name: (apiKey as any).name,
        revokedAt: updatedApiKey.revokedAt,
        message: "API key revoked successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Revoke API key failed", {
      correlationId,
      apiKeyId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Generate API key
 */
function generateApiKey(): string {
  const prefix = 'hepz_';
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = prefix;

  for (let i = 0; i < 40; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return result;
}

/**
 * Hash API key using bcrypt (production implementation)
 */
async function hashApiKey(apiKey: string): Promise<string> {
  const bcrypt = require('bcrypt');
  const saltRounds = 12; // High security for API keys

  try {
    const hashedKey = await bcrypt.hash(apiKey, saltRounds);
    logger.info('API key hashed successfully', {
      keyLength: apiKey.length,
      hashLength: hashedKey.length
    });
    return hashedKey;
  } catch (error) {
    logger.error('Failed to hash API key', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw new Error('API key hashing failed');
  }
}

/**
 * Validate API key (for authentication middleware)
 */
export async function validateApiKey(apiKey: string): Promise<any> {
  const bcrypt = require('bcrypt');

  try {
    // Get all active API keys for comparison
    const keyQuery = 'SELECT * FROM c WHERE c.isActive = true AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
    const keys = await db.queryItems('api-keys', keyQuery, [new Date().toISOString()]);

    // Find matching key by comparing with bcrypt
    let matchedKey: any = null;
    for (const key of keys) {
      const keyData = key as any;
      try {
        const isMatch = await bcrypt.compare(apiKey, keyData.keyHash);
        if (isMatch) {
          matchedKey = keyData;
          break;
        }
      } catch (compareError) {
        logger.warn('Failed to compare API key hash', {
          keyId: keyData.id,
          error: compareError instanceof Error ? compareError.message : String(compareError)
        });
        continue;
      }
    }

    if (!matchedKey) {
      logger.warn('API key validation failed - no matching key found');
      return null;
    }

    // Update usage statistics
    const updatedKey = {
      ...matchedKey,
      id: matchedKey.id,
      usage: {
        ...matchedKey.usage,
        totalRequests: (matchedKey.usage?.totalRequests || 0) + 1,
        lastUsedAt: new Date().toISOString()
      },
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('api-keys', updatedKey);

    logger.info('API key validated successfully', {
      keyId: matchedKey.id,
      organizationId: matchedKey.organizationId
    });

    return matchedKey;
  } catch (error) {
    logger.error("API key validation failed", {
      error: error instanceof Error ? error.message : String(error)
    });
    return null;
  }
}

/**
 * Combined API keys handler
 */
async function handleApiKeys(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await createApiKey(request, context);
    case 'GET':
      return await listApiKeys(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('api-keys', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'api-keys',
  handler: handleApiKeys
});

app.http('api-key-revoke', {
  methods: ['DELETE', 'OPTIONS'],
  authLevel: 'function',
  route: 'api-keys/{apiKeyId}',
  handler: revokeApiKey
});
