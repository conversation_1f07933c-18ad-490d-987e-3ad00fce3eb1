/**
 * Authentication utilities for Azure Functions
 * Handles JWT token validation and user context extraction
 */

import { HttpRequest } from '@azure/functions';
import * as jwt from 'jsonwebtoken';
import * as jwks from 'jwks-rsa';
import { logger } from './logger';

export interface UserContext {
  id: string;
  email: string;
  name?: string;
  tenantId?: string;
  organizationId?: string;
  roles?: string[];
  permissions?: string[];
}

export interface AuthResult {
  success: boolean;
  user?: UserContext;
  error?: string;
}

/**
 * Extract and validate JWT token from request
 */
export function extractToken(request: HttpRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader) {
    return null;
  }

  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

/**
 * Validate JWT token and extract user context
 */
export async function validateToken(token: string): Promise<AuthResult> {
  try {
    // Production JWT validation with Azure AD B2C
    const tenantId = process.env.AZURE_AD_B2C_TENANT_ID;
    const clientId = process.env.AZURE_AD_B2C_CLIENT_ID;

    if (!tenantId || !clientId) {
      logger.error('Azure AD B2C configuration missing');
      return {
        success: false,
        error: 'Authentication service not configured'
      };
    }

    // Production: Verify JWT signature using Azure AD B2C public keys
    let payload: any;
    try {
      // Create JWKS client for Azure AD B2C
      const jwksClient = new jwks.JwksClient({
        jwksUri: `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${tenantId}/discovery/v2.0/keys`,
        cache: true,
        cacheMaxEntries: 5,
        cacheMaxAge: 600000, // 10 minutes
        timeout: 30000,
        jwksRequestsPerMinute: 5
      });

      // Decode token header to get key ID
      const decoded = jwt.decode(token, { complete: true }) as any;
      if (!decoded || !decoded.header || !decoded.header.kid) {
        return {
          success: false,
          error: 'Invalid token structure'
        };
      }

      // Get signing key from JWKS
      const key = await jwksClient.getSigningKey(decoded.header.kid);
      const signingKey = key.getPublicKey();

      // Verify token signature and extract payload
      payload = jwt.verify(token, signingKey, {
        audience: clientId,
        issuer: `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${tenantId}/v2.0/`,
        algorithms: ['RS256']
      }) as any;

      logger.info('JWT signature verified successfully', {
        kid: decoded.header.kid,
        alg: decoded.header.alg
      });

    } catch (verifyError) {
      logger.error('JWT signature verification failed', {
        error: verifyError instanceof Error ? verifyError.message : String(verifyError)
      });
      return {
        success: false,
        error: 'Token signature verification failed'
      };
    }

    // Token is already validated by jwt.verify() above, including expiration, audience, and issuer

    // Extract user information from token
    const user: UserContext = {
      id: payload.sub || payload.oid || payload.userId || payload.id,
      email: payload.email || payload.emails?.[0] || payload.preferred_username,
      name: payload.name || `${payload.given_name || ''} ${payload.family_name || ''}`.trim(),
      tenantId: payload.tid || tenantId,
      organizationId: payload.organizationId || payload.orgId,
      roles: payload.roles || [],
      permissions: payload.permissions || []
    };

    if (!user.id || !user.email) {
      return {
        success: false,
        error: 'Token missing required user information'
      };
    }

    logger.info('Token validated successfully', {
      userId: user.id,
      email: user.email,
      roles: user.roles?.length || 0,
      permissions: user.permissions?.length || 0
    });

    return {
      success: true,
      user
    };
  } catch (error) {
    logger.error('Token validation failed', { error: error instanceof Error ? error.message : String(error) });
    return {
      success: false,
      error: 'Token validation failed'
    };
  }
}

/**
 * Authenticate request and extract user context
 */
export async function authenticateRequest(request: HttpRequest): Promise<AuthResult> {
  const token = extractToken(request);

  if (!token) {
    return {
      success: false,
      error: 'No authentication token provided'
    };
  }

  return await validateToken(token);
}

/**
 * Check if user has required role
 */
export function hasRole(user: UserContext, requiredRole: string): boolean {
  return user.roles?.includes(requiredRole) || false;
}

/**
 * Check if user has required permission
 */
export function hasPermission(user: UserContext, requiredPermission: string): boolean {
  return user.permissions?.includes(requiredPermission) || false;
}

/**
 * Create authentication middleware
 */
export function requireAuth(handler: (request: HttpRequest, context: any, user: UserContext) => Promise<any>) {
  return async (request: HttpRequest, context: any) => {
    const authResult = await authenticateRequest(request);

    if (!authResult.success || !authResult.user) {
      return {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        },
        jsonBody: {
          error: 'Unauthorized',
          message: authResult.error || 'Authentication required'
        }
      };
    }

    return await handler(request, context, authResult.user);
  };
}
