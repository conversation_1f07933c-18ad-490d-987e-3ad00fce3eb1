/**
 * User Tenants Function
 * Handles user tenant management and switching between organizations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schemas
const switchTenantSchema = Joi.object({
  organizationId: Joi.string().uuid().required()
});

const inviteUserSchema = Joi.object({
  email: Joi.string().email().required(),
  organizationId: Joi.string().uuid().required(),
  role: Joi.string().valid('ADMIN', 'MEMBER', 'VIEWER').default('MEMBER'),
  message: Joi.string().max(500).optional()
});

/**
 * Get user tenants handler
 */
export async function getUserTenants(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const userId = request.params.userId;
  
  logger.info("Get user tenants started", { correlationId, userId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // If userId is provided, check if user can access that user's tenants
    const targetUserId = userId || user.id;
    
    if (targetUserId !== user.id && !user.roles?.includes('admin')) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Get user's organization memberships
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [targetUserId, 'active']);

    // Get organization details for each membership
    const tenants = await Promise.all(
      memberships.map(async (membership: any) => {
        try {
          const organization = await db.readItem('organizations', membership.organizationId, membership.organizationId);
          if (organization) {
            // Get member count
            const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status';
            const memberCountResult = await db.queryItems('organization-members', memberCountQuery, [membership.organizationId, 'active']);
            const memberCount = Number(memberCountResult[0]) || 0;

            // Get project count
            const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
            const projectCountResult = await db.queryItems('projects', projectCountQuery, [membership.organizationId]);
            const projectCount = Number(projectCountResult[0]) || 0;

            return {
              organizationId: membership.organizationId,
              name: (organization as any).name,
              description: (organization as any).description,
              tier: (organization as any).tier,
              role: membership.role,
              joinedAt: membership.joinedAt,
              permissions: membership.permissions || [],
              memberCount,
              projectCount,
              isCurrentTenant: membership.organizationId === user.tenantId
            };
          }
        } catch (error) {
          // Organization might not exist or user might not have access
          return null;
        }
      })
    );

    // Filter out null results
    const validTenants = tenants.filter(tenant => tenant !== null);

    logger.info("User tenants retrieved successfully", {
      correlationId,
      userId: targetUserId,
      tenantCount: validTenants.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        userId: targetUserId,
        tenants: validTenants,
        currentTenantId: user.tenantId
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get user tenants failed", {
      correlationId,
      userId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Switch tenant handler
 */
export async function switchTenant(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Switch tenant started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = switchTenantSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { organizationId } = value;

    // Check if user is a member of the target organization
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "You are not a member of this organization" }
      }, request);
    }

    // Get organization details
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Update user's current tenant
    const currentUser = await db.readItem('users', user.id, user.id);
    if (currentUser) {
      const updatedUser = {
        ...(currentUser as any),
        id: user.id,
        tenantId: organizationId,
        currentOrganizationId: organizationId,
        lastTenantSwitch: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      await db.updateItem('users', updatedUser);
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "tenant_switched",
      userId: user.id,
      organizationId,
      timestamp: new Date().toISOString(),
      details: {
        previousTenantId: user.tenantId,
        newTenantId: organizationId,
        organizationName: (organization as any).name
      },
      tenantId: organizationId
    });

    logger.info("Tenant switched successfully", {
      correlationId,
      userId: user.id,
      previousTenantId: user.tenantId,
      newTenantId: organizationId
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        organizationId,
        organizationName: (organization as any).name,
        role: (memberships[0] as any).role,
        message: "Tenant switched successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Switch tenant failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Invite user to organization handler
 */
export async function inviteUserToOrganization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Invite user to organization started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = inviteUserSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { email, organizationId, role, message } = value;

    // Check if user has permission to invite to this organization
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0 || (memberships[0] as any).role !== 'ADMIN') {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization admins can invite users" }
      }, request);
    }

    // Get organization details
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user is already invited or a member
    const existingMemberQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND (c.email = @email OR c.userId IN (SELECT VALUE u.id FROM users u WHERE u.email = @email))';
    const existingMembers = await db.queryItems('organization-members', existingMemberQuery, [organizationId, email]);

    if (existingMembers.length > 0) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User is already a member or has been invited" }
      }, request);
    }

    // Create invitation
    const invitationId = uuidv4();
    const invitation = {
      id: invitationId,
      organizationId,
      email,
      role,
      invitedBy: user.id,
      invitedAt: new Date().toISOString(),
      status: 'PENDING',
      message,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      tenantId: user.tenantId
    };

    await db.createItem('organization-invitations', invitation);

    // Send invitation notification (simplified)
    // In production, this would send an email
    logger.info("Organization invitation would be sent via email", {
      correlationId,
      email,
      organizationId,
      invitedBy: user.id
    });

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "user_invited_to_organization",
      userId: user.id,
      organizationId,
      timestamp: new Date().toISOString(),
      details: {
        invitedEmail: email,
        role,
        organizationName: (organization as any).name,
        invitationId
      },
      tenantId: user.tenantId
    });

    logger.info("User invited to organization successfully", {
      correlationId,
      email,
      organizationId,
      role,
      invitedBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        invitationId,
        email,
        organizationId,
        organizationName: (organization as any).name,
        role,
        expiresAt: invitation.expiresAt,
        message: "Invitation sent successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Invite user to organization failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('user-tenants-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/{userId?}/tenants',
  handler: getUserTenants
});

app.http('user-tenants-switch', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'users/tenants/switch',
  handler: switchTenant
});

app.http('user-tenants-invite', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}/invite',
  handler: inviteUserToOrganization
});
