/**
 * Organization Analytics Function
 * Handles organization-level analytics, reporting, and insights
 * Migrated from old-arch/src/analytics-service/organization/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Analytics types and enums
enum TimePeriod {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year'
}

enum AnalyticsMetric {
  PROJECTS = 'projects',
  MEMBERS = 'members',
  DOCUMENTS = 'documents',
  STORAGE = 'storage',
  ACTIVITIES = 'activities',
  WORKFLOWS = 'workflows'
}

// Validation schemas
const getOrganizationAnalyticsSchema = Joi.object({
  organizationId: Joi.string().uuid().required(),
  period: Joi.string().valid(...Object.values(TimePeriod)).default(TimePeriod.MONTH),
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
  metrics: Joi.array().items(Joi.string().valid(...Object.values(AnalyticsMetric))).optional(),
  includeComparisons: Joi.boolean().default(true),
  includeTrends: Joi.boolean().default(true),
  includeBreakdowns: Joi.boolean().default(true)
});

interface OrganizationAnalyticsRequest {
  organizationId: string;
  period: TimePeriod;
  startDate?: string;
  endDate?: string;
  metrics?: AnalyticsMetric[];
  includeComparisons?: boolean;
  includeTrends?: boolean;
  includeBreakdowns?: boolean;
}

interface TimeSeriesData {
  series: Array<{ date: string; value: number; label?: string }>;
  total: number;
  average: number;
  change: number;
}

interface OrganizationAnalyticsResponse {
  organizationId: string;
  organizationName: string;
  period: TimePeriod;
  dateRange: { start: string; end: string };
  summary: {
    totalProjects: number;
    totalMembers: number;
    totalDocuments: number;
    totalActivities: number;
    storageUsedGB: number;
    activeProjects: number;
    activeMembers: number;
    completedWorkflows: number;
  };
  trends?: {
    projectsCreated: TimeSeriesData;
    memberGrowth: TimeSeriesData;
    documentsUploaded: TimeSeriesData;
    storageGrowth: TimeSeriesData;
    activityTrend: TimeSeriesData;
  };
  breakdowns?: {
    projectsByStatus: Array<{ status: string; count: number; percentage: number }>;
    membersByRole: Array<{ role: string; count: number; percentage: number }>;
    documentsByType: Array<{ type: string; count: number; percentage: number }>;
    activitiesByType: Array<{ type: string; count: number; percentage: number }>;
  };
  comparisons?: {
    previousPeriod: {
      projects: { current: number; previous: number; change: number };
      members: { current: number; previous: number; change: number };
      documents: { current: number; previous: number; change: number };
      activities: { current: number; previous: number; change: number };
    };
  };
  insights: {
    mostActiveProjects: Array<{ id: string; name: string; activityCount: number }>;
    mostActiveMembers: Array<{ id: string; name: string; activityCount: number }>;
    storageUsage: { used: number; total: number; percentage: number };
    growthRate: number;
    engagementScore: number;
  };
}

/**
 * Get organization analytics handler
 */
export async function getOrganizationAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get organization analytics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      organizationId: url.searchParams.get('organizationId') || '',
      period: url.searchParams.get('period') || TimePeriod.MONTH,
      startDate: url.searchParams.get('startDate') || undefined,
      endDate: url.searchParams.get('endDate') || undefined,
      metrics: url.searchParams.get('metrics')?.split(',') || undefined,
      includeComparisons: url.searchParams.get('includeComparisons') !== 'false',
      includeTrends: url.searchParams.get('includeTrends') !== 'false',
      includeBreakdowns: url.searchParams.get('includeBreakdowns') !== 'false'
    };

    // Validate request
    const { error, value } = getOrganizationAnalyticsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const analyticsRequest: OrganizationAnalyticsRequest = value;

    // Check if user has analytics access
    const hasAccess = await checkOrganizationAnalyticsAccess(analyticsRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization analytics" }
      }, request);
    }

    // Get organization
    const organization = await db.readItem('organizations', analyticsRequest.organizationId, analyticsRequest.organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    const organizationData = organization as any;

    // Calculate date range
    const dateRange = calculateDateRange(analyticsRequest.period, analyticsRequest.startDate, analyticsRequest.endDate);

    // Get analytics data
    const analyticsData = await getOrganizationAnalyticsData(analyticsRequest, dateRange);

    const response: OrganizationAnalyticsResponse = {
      organizationId: analyticsRequest.organizationId,
      organizationName: organizationData.name,
      period: analyticsRequest.period,
      dateRange,
      summary: analyticsData.summary,
      trends: analyticsRequest.includeTrends ? analyticsData.trends : undefined,
      breakdowns: analyticsRequest.includeBreakdowns ? analyticsData.breakdowns : undefined,
      comparisons: analyticsRequest.includeComparisons ? analyticsData.comparisons : undefined,
      insights: analyticsData.insights
    };

    logger.info("Organization analytics retrieved successfully", {
      correlationId,
      organizationId: analyticsRequest.organizationId,
      period: analyticsRequest.period,
      totalProjects: analyticsData.summary.totalProjects,
      totalMembers: analyticsData.summary.totalMembers,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get organization analytics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Calculate date range based on period
 */
function calculateDateRange(period: TimePeriod, startDate?: string, endDate?: string): { start: string; end: string } {
  const now = new Date();
  const end = endDate ? new Date(endDate) : now;
  let start: Date;

  if (startDate) {
    start = new Date(startDate);
  } else {
    switch (period) {
      case TimePeriod.DAY:
        start = new Date(end.getTime() - 24 * 60 * 60 * 1000);
        break;
      case TimePeriod.WEEK:
        start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case TimePeriod.MONTH:
        start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate());
        break;
      case TimePeriod.QUARTER:
        start = new Date(end.getFullYear(), end.getMonth() - 3, end.getDate());
        break;
      case TimePeriod.YEAR:
        start = new Date(end.getFullYear() - 1, end.getMonth(), end.getDate());
        break;
      default:
        start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate());
    }
  }

  return {
    start: start.toISOString(),
    end: end.toISOString()
  };
}

/**
 * Check organization analytics access
 */
async function checkOrganizationAnalyticsAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    // Check if user is a member of the organization
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);

    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization analytics access', { error, organizationId, userId });
    return false;
  }
}

/**
 * Get organization analytics data
 */
async function getOrganizationAnalyticsData(
  request: OrganizationAnalyticsRequest,
  dateRange: { start: string; end: string }
): Promise<any> {
  try {
    const orgId = request.organizationId;
    const { start, end } = dateRange;

    // Build base queries
    const projectQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId';
    const memberQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.status = @status';
    const documentQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @start AND c.createdAt <= @end';
    const activityQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.timestamp >= @start AND c.timestamp <= @end';

    // Get data from different collections
    const [projects, members, documents, activities] = await Promise.all([
      db.queryItems('projects', projectQuery, [orgId]),
      db.queryItems('organization-members', memberQuery, [orgId, 'ACTIVE']),
      db.queryItems('documents', documentQuery, [orgId, start, end]),
      db.queryItems('activities', activityQuery, [orgId, start, end])
    ]);

    // Calculate summary statistics
    const summary = calculateSummaryStatistics(projects, members, documents, activities);

    // Calculate trends if requested
    const trends = request.includeTrends ? calculateTrends(projects, members, documents, activities, dateRange) : undefined;

    // Calculate breakdowns if requested
    const breakdowns = request.includeBreakdowns ? calculateBreakdowns(projects, members, documents, activities) : undefined;

    // Calculate comparisons if requested
    const comparisons = request.includeComparisons ? await calculateComparisons(request, dateRange) : undefined;

    // Calculate insights
    const insights = calculateInsights(projects, members, documents, activities);

    return {
      summary,
      trends,
      breakdowns,
      comparisons,
      insights
    };

  } catch (error) {
    logger.error('Failed to get organization analytics data', { error, request });
    throw error;
  }
}

/**
 * Calculate summary statistics
 */
function calculateSummaryStatistics(projects: any[], members: any[], documents: any[], activities: any[]): any {
  const totalProjects = projects.length;
  const totalMembers = members.length;
  const totalDocuments = documents.length;
  const totalActivities = activities.length;

  // Calculate storage used (simplified)
  const storageUsedGB = Math.round(documents.reduce((sum, doc) => sum + (doc.size || 0), 0) / (1024 * 1024 * 1024) * 100) / 100;

  // Calculate active projects (projects with recent activity)
  const activeProjectIds = new Set(activities.map(a => a.projectId).filter(Boolean));
  const activeProjects = activeProjectIds.size;

  // Calculate active members (members with recent activity)
  const activeMemberIds = new Set(activities.map(a => a.userId));
  const activeMembers = activeMemberIds.size;

  // Calculate completed workflows (simplified)
  const completedWorkflows = activities.filter(a => a.type === 'workflow_completed').length;

  return {
    totalProjects,
    totalMembers,
    totalDocuments,
    totalActivities,
    storageUsedGB,
    activeProjects,
    activeMembers,
    completedWorkflows
  };
}

/**
 * Calculate trends
 */
function calculateTrends(projects: any[], members: any[], documents: any[], activities: any[], dateRange: any): any {
  // Group data by date
  const dailyStats: { [date: string]: any } = {};

  // Initialize daily stats
  const start = new Date(dateRange.start);
  const end = new Date(dateRange.end);
  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    const dateKey = d.toISOString().split('T')[0];
    dailyStats[dateKey] = {
      projects: 0,
      members: 0,
      documents: 0,
      storage: 0,
      activities: 0
    };
  }

  // Process projects
  projects.forEach(project => {
    const date = new Date(project.createdAt).toISOString().split('T')[0];
    if (dailyStats[date]) {
      dailyStats[date].projects++;
    }
  });

  // Process members
  members.forEach(member => {
    const date = new Date(member.createdAt).toISOString().split('T')[0];
    if (dailyStats[date]) {
      dailyStats[date].members++;
    }
  });

  // Process documents
  documents.forEach(doc => {
    const date = new Date(doc.createdAt).toISOString().split('T')[0];
    if (dailyStats[date]) {
      dailyStats[date].documents++;
      dailyStats[date].storage += (doc.size || 0) / (1024 * 1024 * 1024); // Convert to GB
    }
  });

  // Process activities
  activities.forEach(activity => {
    const date = new Date(activity.timestamp).toISOString().split('T')[0];
    if (dailyStats[date]) {
      dailyStats[date].activities++;
    }
  });

  // Convert to time series format
  const createTimeSeries = (field: string) => {
    const series = Object.entries(dailyStats).map(([date, stats]) => ({
      date,
      value: (stats as any)[field]
    }));

    const total = series.reduce((sum, item) => sum + item.value, 0);
    const average = series.length > 0 ? total / series.length : 0;
    const change = series.length >= 2 ?
      ((series[series.length - 1].value - series[0].value) / (series[0].value || 1)) * 100 : 0;

    return { series, total, average, change };
  };

  return {
    projectsCreated: createTimeSeries('projects'),
    memberGrowth: createTimeSeries('members'),
    documentsUploaded: createTimeSeries('documents'),
    storageGrowth: createTimeSeries('storage'),
    activityTrend: createTimeSeries('activities')
  };
}

/**
 * Calculate breakdowns
 */
function calculateBreakdowns(projects: any[], members: any[], documents: any[], activities: any[]): any {
  // Projects by status
  const projectStatuses: { [status: string]: number } = {};
  projects.forEach(project => {
    const status = project.status || 'active';
    projectStatuses[status] = (projectStatuses[status] || 0) + 1;
  });

  const projectsByStatus = Object.entries(projectStatuses).map(([status, count]) => ({
    status,
    count,
    percentage: Math.round((count / projects.length) * 100)
  }));

  // Members by role
  const memberRoles: { [role: string]: number } = {};
  members.forEach(member => {
    const role = member.role || 'member';
    memberRoles[role] = (memberRoles[role] || 0) + 1;
  });

  const membersByRole = Object.entries(memberRoles).map(([role, count]) => ({
    role,
    count,
    percentage: Math.round((count / members.length) * 100)
  }));

  // Documents by type
  const documentTypes: { [type: string]: number } = {};
  documents.forEach(doc => {
    const type = doc.contentType?.split('/')[0] || 'unknown';
    documentTypes[type] = (documentTypes[type] || 0) + 1;
  });

  const documentsByType = Object.entries(documentTypes).map(([type, count]) => ({
    type,
    count,
    percentage: Math.round((count / documents.length) * 100)
  }));

  // Activities by type
  const activityTypes: { [type: string]: number } = {};
  activities.forEach(activity => {
    const type = activity.type || 'unknown';
    activityTypes[type] = (activityTypes[type] || 0) + 1;
  });

  const activitiesByType = Object.entries(activityTypes).map(([type, count]) => ({
    type,
    count,
    percentage: Math.round((count / activities.length) * 100)
  }));

  return {
    projectsByStatus,
    membersByRole,
    documentsByType,
    activitiesByType
  };
}

/**
 * Calculate comparisons with previous period
 */
async function calculateComparisons(request: OrganizationAnalyticsRequest, dateRange: any): Promise<any> {
  // Calculate previous period date range
  const periodLength = new Date(dateRange.end).getTime() - new Date(dateRange.start).getTime();
  const previousStart = new Date(new Date(dateRange.start).getTime() - periodLength);
  const previousEnd = new Date(dateRange.start);

  const previousDateRange = {
    start: previousStart.toISOString(),
    end: previousEnd.toISOString()
  };

  // Get previous period data (simplified)
  const previousData = await getOrganizationAnalyticsData(
    { ...request, includeComparisons: false, includeTrends: false, includeBreakdowns: false },
    previousDateRange
  );

  const currentData = await getOrganizationAnalyticsData(
    { ...request, includeComparisons: false, includeTrends: false, includeBreakdowns: false },
    dateRange
  );

  return {
    previousPeriod: {
      projects: {
        current: currentData.summary.totalProjects,
        previous: previousData.summary.totalProjects,
        change: currentData.summary.totalProjects - previousData.summary.totalProjects
      },
      members: {
        current: currentData.summary.totalMembers,
        previous: previousData.summary.totalMembers,
        change: currentData.summary.totalMembers - previousData.summary.totalMembers
      },
      documents: {
        current: currentData.summary.totalDocuments,
        previous: previousData.summary.totalDocuments,
        change: currentData.summary.totalDocuments - previousData.summary.totalDocuments
      },
      activities: {
        current: currentData.summary.totalActivities,
        previous: previousData.summary.totalActivities,
        change: currentData.summary.totalActivities - previousData.summary.totalActivities
      }
    }
  };
}

/**
 * Calculate insights
 */
function calculateInsights(projects: any[], members: any[], documents: any[], activities: any[]): any {
  // Most active projects
  const projectActivity: { [projectId: string]: { count: number; name: string } } = {};
  activities.forEach(activity => {
    if (activity.projectId) {
      if (!projectActivity[activity.projectId]) {
        const project = projects.find(p => p.id === activity.projectId);
        projectActivity[activity.projectId] = { count: 0, name: project?.name || 'Unknown Project' };
      }
      projectActivity[activity.projectId].count++;
    }
  });

  const mostActiveProjects = Object.entries(projectActivity)
    .map(([id, data]) => ({ id, name: data.name, activityCount: data.count }))
    .sort((a, b) => b.activityCount - a.activityCount)
    .slice(0, 5);

  // Most active members
  const memberActivity: { [userId: string]: number } = {};
  activities.forEach(activity => {
    memberActivity[activity.userId] = (memberActivity[activity.userId] || 0) + 1;
  });

  const mostActiveMembers = Object.entries(memberActivity)
    .map(([id, count]) => {
      const member = members.find(m => m.userId === id);
      return { id, name: member?.name || 'Unknown Member', activityCount: count };
    })
    .sort((a, b) => b.activityCount - a.activityCount)
    .slice(0, 5);

  // Storage usage (simplified)
  const storageUsed = documents.reduce((sum, doc) => sum + (doc.size || 0), 0) / (1024 * 1024 * 1024);
  const storageLimit = 100; // Default 100GB, should come from organization settings
  const storageUsage = {
    used: Math.round(storageUsed * 100) / 100,
    total: storageLimit,
    percentage: Math.round((storageUsed / storageLimit) * 100)
  };

  // Growth rate (simplified)
  const growthRate = projects.length > 0 ? Math.round((activities.length / projects.length) * 10) / 10 : 0;

  // Engagement score (activities per member)
  const engagementScore = members.length > 0 ? Math.round((activities.length / members.length) * 10) / 10 : 0;

  return {
    mostActiveProjects,
    mostActiveMembers,
    storageUsage,
    growthRate,
    engagementScore
  };
}

// Register functions
app.http('organization-analytics', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/analytics',
  handler: getOrganizationAnalytics
});
