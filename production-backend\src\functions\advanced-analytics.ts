/**
 * Advanced Analytics Function
 * Handles comprehensive analytics, reporting, and business intelligence
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Analytics metric types enum
enum MetricType {
  DOCUMENT_USAGE = 'DOCUMENT_USAGE',
  USER_ACTIVITY = 'USER_ACTIVITY',
  WORKFLOW_PERFORMANCE = 'WORKFLOW_PERFORMANCE',
  STORAGE_UTILIZATION = 'STORAGE_UTILIZATION',
  API_USAGE = 'API_USAGE',
  COLLABORATION_METRICS = 'COLLABORATION_METRICS',
  SECURITY_METRICS = 'SECURITY_METRICS',
  PERFORMANCE_METRICS = 'PERFORMANCE_METRICS'
}

// Time period enum
enum TimePeriod {
  LAST_24_HOURS = 'LAST_24_HOURS',
  LAST_7_DAYS = 'LAST_7_DAYS',
  LAST_30_DAYS = 'LAST_30_DAYS',
  LAST_90_DAYS = 'LAST_90_DAYS',
  LAST_YEAR = 'LAST_YEAR',
  CUSTOM = 'CUSTOM'
}

// Validation schemas
const getAnalyticsSchema = Joi.object({
  metricType: Joi.string().valid(...Object.values(MetricType)).required(),
  timePeriod: Joi.string().valid(...Object.values(TimePeriod)).default(TimePeriod.LAST_30_DAYS),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  groupBy: Joi.string().valid('day', 'week', 'month', 'user', 'project', 'document_type').optional(),
  filters: Joi.object({
    userId: Joi.string().uuid().optional(),
    documentType: Joi.string().optional(),
    workflowType: Joi.string().optional(),
    status: Joi.string().optional()
  }).optional()
});

const getDashboardSchema = Joi.object({
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  timePeriod: Joi.string().valid(...Object.values(TimePeriod)).default(TimePeriod.LAST_30_DAYS),
  includeComparisons: Joi.boolean().default(true)
});

/**
 * Get analytics handler
 */
export async function getAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get analytics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getAnalyticsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { metricType, timePeriod, startDate, endDate, organizationId, projectId, groupBy, filters } = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Calculate date range
    const dateRange = calculateDateRange(timePeriod, startDate, endDate);

    // Get analytics data based on metric type
    const analyticsData = await getAnalyticsData(
      metricType,
      dateRange,
      organizationId,
      projectId,
      groupBy,
      filters || {}
    );

    logger.info("Analytics retrieved successfully", {
      correlationId,
      userId: user.id,
      metricType,
      timePeriod,
      organizationId,
      dataPoints: analyticsData.data?.length || 0
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        metricType,
        timePeriod,
        dateRange,
        organizationId,
        projectId,
        groupBy,
        filters,
        ...analyticsData,
        generatedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get analytics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get dashboard data handler
 */
export async function getDashboard(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get dashboard started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = getDashboardSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { organizationId, projectId, timePeriod, includeComparisons } = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Calculate date range
    const dateRange = calculateDateRange(timePeriod);

    // Get comprehensive dashboard data
    const dashboardData = await getDashboardData(organizationId, dateRange, includeComparisons, projectId);

    logger.info("Dashboard data retrieved successfully", {
      correlationId,
      userId: user.id,
      organizationId,
      projectId,
      timePeriod
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        organizationId,
        projectId,
        timePeriod,
        dateRange,
        includeComparisons,
        ...dashboardData,
        generatedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get dashboard failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Calculate date range based on time period
 */
function calculateDateRange(timePeriod: string, startDate?: string, endDate?: string): any {
  const now = new Date();
  let start: Date;
  let end: Date = now;

  switch (timePeriod) {
    case TimePeriod.LAST_24_HOURS:
      start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case TimePeriod.LAST_7_DAYS:
      start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case TimePeriod.LAST_30_DAYS:
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case TimePeriod.LAST_90_DAYS:
      start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case TimePeriod.LAST_YEAR:
      start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    case TimePeriod.CUSTOM:
      start = startDate ? new Date(startDate) : new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      end = endDate ? new Date(endDate) : now;
      break;
    default:
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  return {
    startDate: start.toISOString(),
    endDate: end.toISOString()
  };
}

/**
 * Get analytics data based on metric type
 */
async function getAnalyticsData(
  metricType: string,
  dateRange: any,
  organizationId: string,
  projectId?: string,
  groupBy?: string,
  filters: any = {}
): Promise<any> {

  switch (metricType) {
    case MetricType.DOCUMENT_USAGE:
      return await getDocumentUsageAnalytics(dateRange, organizationId, projectId, groupBy, filters);

    case MetricType.USER_ACTIVITY:
      return await getUserActivityAnalytics(dateRange, organizationId, projectId, groupBy, filters);

    case MetricType.WORKFLOW_PERFORMANCE:
      return await getWorkflowPerformanceAnalytics(dateRange, organizationId, projectId, groupBy, filters);

    case MetricType.STORAGE_UTILIZATION:
      return await getStorageUtilizationAnalytics(dateRange, organizationId, projectId, groupBy, filters);

    case MetricType.API_USAGE:
      return await getApiUsageAnalytics(dateRange, organizationId, projectId, groupBy, filters);

    case MetricType.COLLABORATION_METRICS:
      return await getCollaborationMetrics(dateRange, organizationId, projectId, groupBy, filters);

    case MetricType.SECURITY_METRICS:
      return await getSecurityMetrics(dateRange, organizationId, projectId, groupBy, filters);

    case MetricType.PERFORMANCE_METRICS:
      return await getPerformanceMetrics(dateRange, organizationId, projectId, groupBy, filters);

    default:
      throw new Error(`Unsupported metric type: ${metricType}`);
  }
}

/**
 * Get document usage analytics
 */
async function getDocumentUsageAnalytics(dateRange: any, organizationId: string, projectId?: string, groupBy?: string, filters: any = {}): Promise<any> {
  try {
    // Production aggregation using Cosmos DB SQL with advanced analytics
    let documentsQuery = `
      SELECT
        c.id,
        c.name,
        c.size,
        c.contentType,
        c.createdAt,
        c.updatedAt,
        c.createdBy,
        c.projectId,
        c.organizationId,
        c.accessCount,
        c.lastAccessedAt,
        c.tags
      FROM c
      WHERE c.organizationId = @orgId
      AND c.createdAt >= @startDate
      AND c.createdAt <= @endDate
    `;

    const parameters = [
      { name: '@orgId', value: organizationId },
      { name: '@startDate', value: dateRange.startDate },
      { name: '@endDate', value: dateRange.endDate }
    ];

    if (projectId) {
      documentsQuery += ' AND c.projectId = @projectId';
      parameters.push({ name: '@projectId', value: projectId });
    }

    // Add content type filter if specified
    if (filters.contentTypes && filters.contentTypes.length > 0) {
      documentsQuery += ' AND c.contentType IN (@contentTypes)';
      parameters.push({ name: '@contentTypes', value: filters.contentTypes });
    }

    documentsQuery += ' ORDER BY c.createdAt DESC';

    const documents = await db.queryItems('documents', documentsQuery, parameters);

    // Perform advanced statistical aggregations
    const aggregatedData = await performAdvancedDocumentAggregation(documents, groupBy, dateRange);

    // Calculate advanced metrics
    const advancedMetrics = calculateAdvancedDocumentMetrics(documents, dateRange);

    // Generate usage patterns analysis
    const usagePatterns = analyzeDocumentUsagePatterns(documents, dateRange);

    logger.info('Document usage analytics completed', {
      organizationId,
      projectId,
      totalDocuments: documents.length,
      dateRange,
      groupBy
    });

    return {
      totalDocuments: documents.length,
      aggregatedData,
      advancedMetrics,
      usagePatterns,
      summary: {
        averageSize: advancedMetrics.averageSize,
        totalSize: advancedMetrics.totalSize,
        medianSize: advancedMetrics.medianSize,
        mostActiveUsers: advancedMetrics.mostActiveUsers,
        popularFileTypes: advancedMetrics.popularFileTypes,
        accessFrequency: advancedMetrics.accessFrequency
      }
    };

  } catch (error) {
    logger.error('Failed to get document usage analytics', {
      error: error instanceof Error ? error.message : String(error),
      organizationId,
      projectId,
      dateRange
    });
    throw error;
  }
}

/**
 * Get user activity analytics
 */
async function getUserActivityAnalytics(dateRange: any, organizationId: string, projectId?: string, groupBy?: string, filters: any = {}): Promise<any> {
  const activitiesQuery = `
    SELECT * FROM c
    WHERE c.organizationId = @orgId
    AND c.timestamp >= @startDate
    AND c.timestamp <= @endDate
  `;
  const activities = await db.queryItems('activities', activitiesQuery, [organizationId, dateRange.startDate, dateRange.endDate]);

  return {
    totalActivities: activities.length,
    activeUsers: getActiveUsers(activities),
    activitiesByType: groupActivitiesByType(activities),
    activitiesOverTime: groupActivitiesByTime(activities, groupBy || 'day'),
    topUsers: getTopActiveUsers(activities)
  };
}

/**
 * Get comprehensive dashboard data
 */
async function getDashboardData(organizationId: string, dateRange: any, includeComparisons: boolean = true, projectId?: string): Promise<any> {
  // Get key metrics
  const [
    documentMetrics,
    userMetrics,
    workflowMetrics,
    storageMetrics
  ] = await Promise.all([
    getDocumentUsageAnalytics(dateRange, organizationId, projectId),
    getUserActivityAnalytics(dateRange, organizationId, projectId),
    getWorkflowPerformanceAnalytics(dateRange, organizationId, projectId),
    getStorageUtilizationAnalytics(dateRange, organizationId, projectId)
  ]);

  const dashboard: any = {
    overview: {
      totalDocuments: documentMetrics.totalDocuments,
      activeUsers: userMetrics.activeUsers.length,
      totalActivities: userMetrics.totalActivities,
      storageUsed: storageMetrics.totalUsed || 0
    },
    charts: {
      documentTrends: documentMetrics.documentsOverTime,
      userActivity: userMetrics.activitiesOverTime,
      workflowPerformance: workflowMetrics.performanceOverTime || [],
      storageGrowth: storageMetrics.growthOverTime || []
    },
    insights: {
      topDocuments: documentMetrics.topDocuments,
      topUsers: userMetrics.topUsers,
      documentTypes: documentMetrics.documentsByType,
      activityTypes: userMetrics.activitiesByType
    }
  };

  if (includeComparisons) {
    // Add comparison data (previous period)
    (dashboard as any).comparisons = await getComparisonData(organizationId, dateRange, projectId);
  }

  return dashboard;
}

// Helper functions for analytics calculations
function groupDocumentsByType(documents: any[]): any[] {
  const grouped = documents.reduce((acc, doc) => {
    const type = doc.contentType || 'unknown';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped).map(([type, count]) => ({ type, count }));
}

function groupDocumentsByTime(documents: any[], groupBy: string): any[] {
  // Simplified implementation
  return documents.map(doc => ({
    date: doc.createdAt.split('T')[0],
    count: 1
  }));
}

function getTopDocuments(documents: any[]): any[] {
  return documents.slice(0, 10).map(doc => ({
    id: doc.id,
    name: doc.name,
    views: doc.viewCount || 0,
    size: doc.size || 0
  }));
}

function calculateAverageSize(documents: any[]): number {
  if (documents.length === 0) return 0;
  const totalSize = documents.reduce((sum, doc) => sum + (doc.size || 0), 0);
  return Math.round(totalSize / documents.length);
}

function getMostActiveDay(documents: any[]): string {
  // Simplified implementation
  return new Date().toISOString().split('T')[0];
}

function getActiveUsers(activities: any[]): string[] {
  const users = new Set(activities.map(activity => activity.userId).filter(Boolean));
  return Array.from(users);
}

function groupActivitiesByType(activities: any[]): any[] {
  const grouped = activities.reduce((acc, activity) => {
    const type = activity.type || 'unknown';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped).map(([type, count]) => ({ type, count }));
}

function groupActivitiesByTime(activities: any[], groupBy: string): any[] {
  // Simplified implementation
  return activities.map(activity => ({
    date: activity.timestamp.split('T')[0],
    count: 1
  }));
}

function getTopActiveUsers(activities: any[]): any[] {
  const userCounts = activities.reduce((acc, activity) => {
    if (activity.userId) {
      acc[activity.userId] = (acc[activity.userId] || 0) + 1;
    }
    return acc;
  }, {});

  return Object.entries(userCounts)
    .sort(([,a], [,b]) => (b as number) - (a as number))
    .slice(0, 10)
    .map(([userId, count]) => ({ userId, activityCount: count }));
}

async function getWorkflowPerformanceAnalytics(dateRange: any, organizationId: string, projectId?: string, groupBy?: string, filters: any = {}): Promise<any> {
  return {
    totalWorkflows: 0,
    completedWorkflows: 0,
    averageCompletionTime: 0,
    performanceOverTime: []
  };
}

async function getStorageUtilizationAnalytics(dateRange: any, organizationId: string, projectId?: string, groupBy?: string, filters: any = {}): Promise<any> {
  return {
    totalUsed: 0,
    totalLimit: 1000000000, // 1GB
    utilizationPercentage: 0,
    growthOverTime: []
  };
}

async function getApiUsageAnalytics(dateRange: any, organizationId: string, projectId?: string, groupBy?: string, filters: any = {}): Promise<any> {
  return {
    totalRequests: 0,
    successfulRequests: 0,
    errorRate: 0,
    requestsOverTime: []
  };
}

async function getCollaborationMetrics(dateRange: any, organizationId: string, projectId?: string, groupBy?: string, filters: any = {}): Promise<any> {
  return {
    totalShares: 0,
    totalComments: 0,
    collaborativeDocuments: 0,
    collaborationOverTime: []
  };
}

async function getSecurityMetrics(dateRange: any, organizationId: string, projectId?: string, groupBy?: string, filters: any = {}): Promise<any> {
  return {
    securityEvents: 0,
    failedLogins: 0,
    suspiciousActivity: 0,
    securityEventsOverTime: []
  };
}

async function getPerformanceMetrics(dateRange: any, organizationId: string, projectId?: string, groupBy?: string, filters: any = {}): Promise<any> {
  return {
    averageResponseTime: 0,
    uptime: 99.9,
    errorRate: 0.1,
    performanceOverTime: []
  };
}

async function getComparisonData(organizationId: string, dateRange: any, projectId?: string): Promise<any> {
  // Calculate previous period for comparison
  const periodLength = new Date(dateRange.endDate).getTime() - new Date(dateRange.startDate).getTime();
  const previousPeriod = {
    startDate: new Date(new Date(dateRange.startDate).getTime() - periodLength).toISOString(),
    endDate: dateRange.startDate
  };

  // Get metrics for previous period
  const previousMetrics = await getDocumentUsageAnalytics(previousPeriod, organizationId, projectId);

  return {
    previousPeriod,
    documentGrowth: 0, // Calculate percentage change
    userGrowth: 0,
    activityGrowth: 0
  };
}

/**
 * Perform advanced document aggregation with statistical analysis
 */
async function performAdvancedDocumentAggregation(documents: any[], groupBy?: string, dateRange?: any): Promise<any> {
  try {
    const aggregations: any = {};

    // Time-based aggregation
    if (groupBy === 'hour' || groupBy === 'day' || groupBy === 'week' || groupBy === 'month') {
      aggregations.timeSeries = aggregateByTimeInterval(documents, groupBy);
    }

    // Content type aggregation
    aggregations.byContentType = documents.reduce((acc, doc) => {
      const type = doc.contentType || 'unknown';
      if (!acc[type]) {
        acc[type] = { count: 0, totalSize: 0, avgSize: 0 };
      }
      acc[type].count++;
      acc[type].totalSize += doc.size || 0;
      acc[type].avgSize = acc[type].totalSize / acc[type].count;
      return acc;
    }, {});

    // User activity aggregation
    aggregations.byUser = documents.reduce((acc, doc) => {
      const userId = doc.createdBy || 'unknown';
      if (!acc[userId]) {
        acc[userId] = { count: 0, totalSize: 0, lastActivity: null };
      }
      acc[userId].count++;
      acc[userId].totalSize += doc.size || 0;
      if (!acc[userId].lastActivity || doc.createdAt > acc[userId].lastActivity) {
        acc[userId].lastActivity = doc.createdAt;
      }
      return acc;
    }, {});

    // Project aggregation
    aggregations.byProject = documents.reduce((acc, doc) => {
      const projectId = doc.projectId || 'unassigned';
      if (!acc[projectId]) {
        acc[projectId] = { count: 0, totalSize: 0, uniqueUsers: new Set() };
      }
      acc[projectId].count++;
      acc[projectId].totalSize += doc.size || 0;
      acc[projectId].uniqueUsers.add(doc.createdBy);
      return acc;
    }, {});

    // Convert Sets to arrays for JSON serialization
    Object.keys(aggregations.byProject).forEach(projectId => {
      aggregations.byProject[projectId].uniqueUsers = Array.from(aggregations.byProject[projectId].uniqueUsers);
      aggregations.byProject[projectId].collaborationScore = aggregations.byProject[projectId].uniqueUsers.length;
    });

    return aggregations;

  } catch (error) {
    logger.error('Failed to perform advanced document aggregation', {
      error: error instanceof Error ? error.message : String(error)
    });
    return {};
  }
}

/**
 * Calculate advanced document metrics with statistical analysis
 */
function calculateAdvancedDocumentMetrics(documents: any[], dateRange: any): any {
  try {
    const sizes = documents.map(doc => doc.size || 0).filter(size => size > 0);
    const accessCounts = documents.map(doc => doc.accessCount || 0);

    // Statistical calculations
    const totalSize = sizes.reduce((sum, size) => sum + size, 0);
    const averageSize = sizes.length > 0 ? totalSize / sizes.length : 0;
    const medianSize = calculateMedian(sizes);
    const sizeStandardDeviation = calculateStandardDeviation(sizes);

    // User activity analysis
    const userActivity = documents.reduce((acc, doc) => {
      const userId = doc.createdBy || 'unknown';
      acc[userId] = (acc[userId] || 0) + 1;
      return acc;
    }, {});

    const mostActiveUsers = Object.entries(userActivity)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([userId, count]) => ({ userId, documentCount: count }));

    // File type analysis
    const fileTypes = documents.reduce((acc, doc) => {
      const type = doc.contentType || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const popularFileTypes = Object.entries(fileTypes)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([type, count]) => ({ contentType: type, count }));

    // Access frequency analysis
    const totalAccess = accessCounts.reduce((sum, count) => sum + count, 0);
    const averageAccess = accessCounts.length > 0 ? totalAccess / accessCounts.length : 0;

    return {
      totalSize,
      averageSize,
      medianSize,
      sizeStandardDeviation,
      mostActiveUsers,
      popularFileTypes,
      accessFrequency: {
        total: totalAccess,
        average: averageAccess,
        median: calculateMedian(accessCounts)
      }
    };

  } catch (error) {
    logger.error('Failed to calculate advanced document metrics', {
      error: error instanceof Error ? error.message : String(error)
    });
    return {};
  }
}

/**
 * Analyze document usage patterns
 */
function analyzeDocumentUsagePatterns(documents: any[], dateRange: any): any {
  try {
    // Peak usage hours analysis
    const hourlyActivity = documents.reduce((acc, doc) => {
      const hour = new Date(doc.createdAt).getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {});

    const peakHours = Object.entries(hourlyActivity)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 3)
      .map(([hour, count]) => ({ hour: parseInt(hour), activityCount: count }));

    // Weekly patterns
    const weeklyActivity = documents.reduce((acc, doc) => {
      const dayOfWeek = new Date(doc.createdAt).getDay();
      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayName = dayNames[dayOfWeek];
      acc[dayName] = (acc[dayName] || 0) + 1;
      return acc;
    }, {});

    // Collaboration patterns
    const collaborationData = analyzeCollaborationPatterns(documents);

    return {
      peakHours,
      weeklyActivity,
      collaborationPatterns: collaborationData,
      activityDistribution: {
        hourly: hourlyActivity,
        weekly: weeklyActivity
      }
    };

  } catch (error) {
    logger.error('Failed to analyze document usage patterns', {
      error: error instanceof Error ? error.message : String(error)
    });
    return {};
  }
}

/**
 * Helper function to calculate median
 */
function calculateMedian(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  const sorted = [...numbers].sort((a, b) => a - b);
  const mid = Math.floor(sorted.length / 2);
  return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
}

/**
 * Helper function to calculate standard deviation
 */
function calculateStandardDeviation(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
  const avgSquaredDiff = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
  return Math.sqrt(avgSquaredDiff);
}

/**
 * Aggregate documents by time interval
 */
function aggregateByTimeInterval(documents: any[], interval: string): any {
  return documents.reduce((acc, doc) => {
    const date = new Date(doc.createdAt);
    let key: string;

    switch (interval) {
      case 'hour':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
        break;
      case 'day':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = `${weekStart.getFullYear()}-W${Math.ceil((weekStart.getTime() - new Date(weekStart.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`;
        break;
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      default:
        key = date.toISOString().split('T')[0];
    }

    if (!acc[key]) {
      acc[key] = { count: 0, totalSize: 0 };
    }
    acc[key].count++;
    acc[key].totalSize += doc.size || 0;
    return acc;
  }, {});
}

/**
 * Analyze collaboration patterns
 */
function analyzeCollaborationPatterns(documents: any[]): any {
  const projectCollaboration = documents.reduce((acc, doc) => {
    const projectId = doc.projectId || 'unassigned';
    if (!acc[projectId]) {
      acc[projectId] = new Set();
    }
    acc[projectId].add(doc.createdBy);
    return acc;
  }, {});

  const collaborationScores = Object.entries(projectCollaboration).map(([projectId, users]) => ({
    projectId,
    uniqueUsers: (users as Set<string>).size,
    collaborationScore: (users as Set<string>).size > 1 ? (users as Set<string>).size / documents.filter(d => d.projectId === projectId).length : 0
  }));

  return {
    projectCollaboration: collaborationScores,
    averageCollaborationScore: collaborationScores.reduce((sum, score) => sum + score.collaborationScore, 0) / collaborationScores.length || 0
  };
}

// Register functions
app.http('advanced-analytics-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'analytics/advanced',
  handler: getAnalytics
});

app.http('analytics-dashboard', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'analytics/dashboard',
  handler: getDashboard
});
