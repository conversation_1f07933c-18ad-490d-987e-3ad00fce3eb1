/**
 * Logging Service Function
 * Handles centralized logging and log management
 * Migrated from old-arch/src/logging-service/logs/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';

// Log types and enums
enum LogLevel {
  TRACE = 'TRACE',
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL'
}

enum LogCategory {
  APPLICATION = 'APPLICATION',
  SECURITY = 'SECURITY',
  PERFORMANCE = 'PERFORMANCE',
  AUDIT = 'AUDIT',
  SYSTEM = 'SYSTEM',
  USER_ACTION = 'USER_ACTION',
  API = 'API',
  DATABASE = 'DATABASE'
}

// Validation schemas
const createLogSchema = Joi.object({
  level: Joi.string().valid(...Object.values(LogLevel)).required(),
  category: Joi.string().valid(...Object.values(LogCategory)).required(),
  message: Joi.string().min(1).max(1000).required(),
  source: Joi.string().max(100).required(),
  organizationId: Joi.string().uuid().optional(),
  userId: Joi.string().uuid().optional(),
  correlationId: Joi.string().uuid().optional(),
  metadata: Joi.object({
    functionName: Joi.string().optional(),
    duration: Joi.number().optional(),
    statusCode: Joi.number().optional(),
    userAgent: Joi.string().optional(),
    ipAddress: Joi.string().ip().optional(),
    requestId: Joi.string().optional(),
    sessionId: Joi.string().optional(),
    error: Joi.object({
      name: Joi.string().optional(),
      message: Joi.string().optional(),
      stack: Joi.string().optional()
    }).optional(),
    custom: Joi.object().optional()
  }).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(20).optional()
});

const queryLogsSchema = Joi.object({
  level: Joi.array().items(Joi.string().valid(...Object.values(LogLevel))).optional(),
  category: Joi.array().items(Joi.string().valid(...Object.values(LogCategory))).optional(),
  source: Joi.string().max(100).optional(),
  organizationId: Joi.string().uuid().optional(),
  userId: Joi.string().uuid().optional(),
  correlationId: Joi.string().uuid().optional(),
  dateRange: Joi.object({
    startDate: Joi.string().isoDate().required(),
    endDate: Joi.string().isoDate().required()
  }).optional(),
  search: Joi.string().max(200).optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  page: Joi.number().min(1).default(1),
  limit: Joi.number().min(1).max(1000).default(100),
  sortBy: Joi.string().valid('timestamp', 'level', 'source', 'message').default('timestamp'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

interface CreateLogRequest {
  level: LogLevel;
  category: LogCategory;
  message: string;
  source: string;
  organizationId?: string;
  userId?: string;
  correlationId?: string;
  metadata?: {
    functionName?: string;
    duration?: number;
    statusCode?: number;
    userAgent?: string;
    ipAddress?: string;
    requestId?: string;
    sessionId?: string;
    error?: {
      name?: string;
      message?: string;
      stack?: string;
    };
    custom?: any;
  };
  tags?: string[];
}

interface LogEntry {
  id: string;
  level: LogLevel;
  category: LogCategory;
  message: string;
  source: string;
  organizationId?: string;
  userId?: string;
  correlationId?: string;
  metadata: any;
  tags: string[];
  timestamp: string;
  tenantId?: string;
}

/**
 * Create log entry handler
 */
export async function createLogEntry(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;

  try {
    // Validate request body
    const body = await request.json();
    const { error, value } = createLogSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const logRequest: CreateLogRequest = value;

    // Create log entry
    const logId = uuidv4();
    const now = new Date().toISOString();

    const logEntry: LogEntry = {
      id: logId,
      level: logRequest.level,
      category: logRequest.category,
      message: logRequest.message,
      source: logRequest.source,
      organizationId: logRequest.organizationId,
      userId: logRequest.userId,
      correlationId: logRequest.correlationId || correlationId,
      metadata: {
        ...logRequest.metadata,
        requestTimestamp: now,
        serverTimestamp: now
      },
      tags: logRequest.tags || [],
      timestamp: now,
      tenantId: logRequest.organizationId // Use organizationId as tenantId for logs
    };

    // Store log entry
    await storeLogEntry(logEntry);

    // Cache recent logs for quick access
    await cacheRecentLog(logEntry);

    // Update log statistics
    await updateLogStatistics(logEntry);

    // Log the log creation (meta-logging)
    logger.info("Log entry created", {
      logId,
      level: logRequest.level,
      category: logRequest.category,
      source: logRequest.source,
      correlationId: logEntry.correlationId
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        logId,
        level: logRequest.level,
        category: logRequest.category,
        timestamp: now,
        message: "Log entry created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create log entry failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Query logs handler
 */
export async function queryLogs(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Query logs started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access for log viewing
    const hasAccess = await checkLogAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to logs" }
      }, request);
    }

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      level: url.searchParams.get('level')?.split(',') || undefined,
      category: url.searchParams.get('category')?.split(',') || undefined,
      source: url.searchParams.get('source') || undefined,
      organizationId: url.searchParams.get('organizationId') || undefined,
      userId: url.searchParams.get('userId') || undefined,
      correlationId: url.searchParams.get('correlationId') || undefined,
      dateRange: url.searchParams.get('startDate') && url.searchParams.get('endDate') ? {
        startDate: url.searchParams.get('startDate')!,
        endDate: url.searchParams.get('endDate')!
      } : undefined,
      search: url.searchParams.get('search') || undefined,
      tags: url.searchParams.get('tags')?.split(',') || undefined,
      page: parseInt(url.searchParams.get('page') || '1'),
      limit: parseInt(url.searchParams.get('limit') || '100'),
      sortBy: url.searchParams.get('sortBy') || 'timestamp',
      sortOrder: url.searchParams.get('sortOrder') || 'desc'
    };

    // Validate query parameters
    const { error, value } = queryLogsSchema.validate(queryParams);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const queryRequest = value;

    // Execute log query
    const logResults = await executeLogQuery(queryRequest, user);

    logger.info("Logs queried successfully", {
      correlationId,
      resultCount: logResults.logs.length,
      totalCount: logResults.totalCount,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        logs: logResults.logs,
        pagination: {
          page: queryRequest.page,
          limit: queryRequest.limit,
          totalCount: logResults.totalCount,
          totalPages: Math.ceil(logResults.totalCount / queryRequest.limit)
        },
        filters: {
          level: queryRequest.level,
          category: queryRequest.category,
          source: queryRequest.source,
          dateRange: queryRequest.dateRange,
          search: queryRequest.search
        },
        queriedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Query logs failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get log statistics handler
 */
export async function getLogStatistics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get log statistics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkLogAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to log statistics" }
      }, request);
    }

    // Get log statistics
    const statistics = await getLogStatisticsData();

    logger.info("Log statistics retrieved successfully", {
      correlationId,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        statistics,
        generatedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get log statistics failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkLogAccess(user: any): Promise<boolean> {
  try {
    // Check if user has admin or log viewer role
    return user.roles?.includes('admin') || user.roles?.includes('log_viewer');
  } catch (error) {
    logger.error('Failed to check log access', { error, userId: user.id });
    return false;
  }
}

async function storeLogEntry(logEntry: LogEntry): Promise<void> {
  try {
    // Store in database
    await db.createItem('logs', logEntry);

    // Also store in time-series collection for analytics
    const timeSeriesEntry = {
      id: uuidv4(),
      logId: logEntry.id,
      level: logEntry.level,
      category: logEntry.category,
      source: logEntry.source,
      timestamp: logEntry.timestamp,
      date: logEntry.timestamp.split('T')[0], // Date for partitioning
      hour: new Date(logEntry.timestamp).getHours(),
      organizationId: logEntry.organizationId,
      tenantId: logEntry.tenantId
    };

    await db.createItem('log-timeseries', timeSeriesEntry);

  } catch (error) {
    logger.error('Failed to store log entry', { error, logId: logEntry.id });
  }
}

async function cacheRecentLog(logEntry: LogEntry): Promise<void> {
  try {
    // Cache recent logs by level for quick access
    const cacheKey = `logs:recent:${logEntry.level}`;
    const logData = JSON.stringify({
      id: logEntry.id,
      level: logEntry.level,
      category: logEntry.category,
      message: logEntry.message,
      source: logEntry.source,
      timestamp: logEntry.timestamp
    });

    await redis.lpush(cacheKey, logData);
    await redis.ltrim(cacheKey, 0, 99); // Keep last 100 logs
    await redis.expire(cacheKey, 3600); // 1 hour expiry

    // Cache by organization if available
    if (logEntry.organizationId) {
      const orgCacheKey = `logs:org:${logEntry.organizationId}:recent`;
      await redis.lpush(orgCacheKey, logData);
      await redis.ltrim(orgCacheKey, 0, 49); // Keep last 50 logs per org
      await redis.expire(orgCacheKey, 1800); // 30 minutes expiry
    }

  } catch (error) {
    logger.error('Failed to cache recent log', { error, logId: logEntry.id });
  }
}

async function updateLogStatistics(logEntry: LogEntry): Promise<void> {
  try {
    const today = new Date().toISOString().split('T')[0];
    const hour = new Date(logEntry.timestamp).getHours();

    // Update daily statistics
    const dailyStatsKey = `log_stats:${today}`;
    await redis.hincrby(dailyStatsKey, 'total_logs', 1);
    await redis.hincrby(dailyStatsKey, `level_${logEntry.level}`, 1);
    await redis.hincrby(dailyStatsKey, `category_${logEntry.category}`, 1);
    await redis.hincrby(dailyStatsKey, `source_${logEntry.source}`, 1);
    await redis.expire(dailyStatsKey, 86400 * 7); // 7 days

    // Update hourly statistics
    const hourlyStatsKey = `log_stats:${today}:${hour}`;
    await redis.hincrby(hourlyStatsKey, 'total_logs', 1);
    await redis.hincrby(hourlyStatsKey, `level_${logEntry.level}`, 1);
    await redis.expire(hourlyStatsKey, 86400); // 24 hours

    // Update organization statistics if available
    if (logEntry.organizationId) {
      const orgStatsKey = `log_stats:org:${logEntry.organizationId}:${today}`;
      await redis.hincrby(orgStatsKey, 'total_logs', 1);
      await redis.hincrby(orgStatsKey, `level_${logEntry.level}`, 1);
      await redis.expire(orgStatsKey, 86400 * 30); // 30 days
    }

  } catch (error) {
    logger.error('Failed to update log statistics', { error, logId: logEntry.id });
  }
}

async function executeLogQuery(queryRequest: any, user: any): Promise<any> {
  try {
    // Build query
    let query = 'SELECT * FROM c WHERE 1=1';
    const parameters: any[] = [];

    if (queryRequest.level && queryRequest.level.length > 0) {
      query += ' AND c.level IN (@levels)';
      parameters.push(queryRequest.level);
    }

    if (queryRequest.category && queryRequest.category.length > 0) {
      query += ' AND c.category IN (@categories)';
      parameters.push(queryRequest.category);
    }

    if (queryRequest.source) {
      query += ' AND c.source = @source';
      parameters.push(queryRequest.source);
    }

    if (queryRequest.organizationId) {
      query += ' AND c.organizationId = @orgId';
      parameters.push(queryRequest.organizationId);
    }

    if (queryRequest.userId) {
      query += ' AND c.userId = @userId';
      parameters.push(queryRequest.userId);
    }

    if (queryRequest.correlationId) {
      query += ' AND c.correlationId = @correlationId';
      parameters.push(queryRequest.correlationId);
    }

    if (queryRequest.dateRange) {
      query += ' AND c.timestamp >= @startDate AND c.timestamp <= @endDate';
      parameters.push(queryRequest.dateRange.startDate, queryRequest.dateRange.endDate);
    }

    if (queryRequest.search) {
      query += ' AND CONTAINS(LOWER(c.message), @search)';
      parameters.push(queryRequest.search.toLowerCase());
    }

    if (queryRequest.tags && queryRequest.tags.length > 0) {
      query += ' AND ARRAY_CONTAINS(c.tags, @tag)';
      parameters.push(queryRequest.tags[0]); // Simplified - would handle multiple tags
    }

    // Add sorting
    const sortDirection = queryRequest.sortOrder.toUpperCase();
    query += ` ORDER BY c.${queryRequest.sortBy} ${sortDirection}`;

    // Execute query
    const allLogs = await db.queryItems('logs', query, parameters);

    // Apply pagination
    const startIndex = (queryRequest.page - 1) * queryRequest.limit;
    const paginatedLogs = allLogs.slice(startIndex, startIndex + queryRequest.limit);

    return {
      logs: paginatedLogs,
      totalCount: allLogs.length
    };

  } catch (error) {
    logger.error('Failed to execute log query', { error, queryRequest });
    return {
      logs: [],
      totalCount: 0
    };
  }
}

async function getLogStatisticsData(): Promise<any> {
  try {
    const today = new Date().toISOString().split('T')[0];
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Get daily statistics
    const todayStats = await redis.hgetall(`log_stats:${today}`);
    const yesterdayStats = await redis.hgetall(`log_stats:${yesterday}`);

    // Get hourly statistics for today
    const hourlyStats = [];
    for (let hour = 0; hour < 24; hour++) {
      const hourStats = await redis.hgetall(`log_stats:${today}:${hour}`);
      hourlyStats.push({
        hour,
        totalLogs: parseInt(hourStats.total_logs || '0'),
        errorLogs: parseInt(hourStats.level_ERROR || '0'),
        warnLogs: parseInt(hourStats.level_WARN || '0')
      });
    }

    // Calculate trends
    const todayTotal = parseInt(todayStats.total_logs || '0');
    const yesterdayTotal = parseInt(yesterdayStats.total_logs || '0');
    const trend = yesterdayTotal > 0 ? ((todayTotal - yesterdayTotal) / yesterdayTotal * 100) : 0;

    return {
      summary: {
        totalLogsToday: todayTotal,
        totalLogsYesterday: yesterdayTotal,
        trend: Math.round(trend * 100) / 100,
        errorLogsToday: parseInt(todayStats.level_ERROR || '0'),
        warnLogsToday: parseInt(todayStats.level_WARN || '0')
      },
      levelBreakdown: {
        TRACE: parseInt(todayStats.level_TRACE || '0'),
        DEBUG: parseInt(todayStats.level_DEBUG || '0'),
        INFO: parseInt(todayStats.level_INFO || '0'),
        WARN: parseInt(todayStats.level_WARN || '0'),
        ERROR: parseInt(todayStats.level_ERROR || '0'),
        FATAL: parseInt(todayStats.level_FATAL || '0')
      },
      categoryBreakdown: {
        APPLICATION: parseInt(todayStats.category_APPLICATION || '0'),
        SECURITY: parseInt(todayStats.category_SECURITY || '0'),
        PERFORMANCE: parseInt(todayStats.category_PERFORMANCE || '0'),
        AUDIT: parseInt(todayStats.category_AUDIT || '0'),
        SYSTEM: parseInt(todayStats.category_SYSTEM || '0'),
        USER_ACTION: parseInt(todayStats.category_USER_ACTION || '0'),
        API: parseInt(todayStats.category_API || '0'),
        DATABASE: parseInt(todayStats.category_DATABASE || '0')
      },
      hourlyTrend: hourlyStats
    };

  } catch (error) {
    logger.error('Failed to get log statistics data', { error });
    return {
      summary: {
        totalLogsToday: 0,
        totalLogsYesterday: 0,
        trend: 0,
        errorLogsToday: 0,
        warnLogsToday: 0
      },
      levelBreakdown: {},
      categoryBreakdown: {},
      hourlyTrend: []
    };
  }
}

// Register functions
app.http('log-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'logs',
  handler: createLogEntry
});

app.http('log-query', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'logs/query',
  handler: queryLogs
});

app.http('log-statistics', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'logs/statistics',
  handler: getLogStatistics
});
