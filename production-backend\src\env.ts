/**
 * Environment configuration for Azure Functions
 * Loads environment variables and validates required settings
 */

import * as dotenv from 'dotenv';

// Load environment variables from .env file in development
if (process.env.NODE_ENV !== 'production') {
  dotenv.config();
}

// Environment validation
const requiredEnvVars = [
  'COSMOS_DB_ENDPOINT',
  'COSMOS_DB_KEY',
  'COSMOS_DB_DATABASE',
  'AZURE_STORAGE_CONNECTION_STRING'
];

// Check for required environment variables
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.warn(`Warning: Missing environment variables: ${missingEnvVars.join(', ')}`);
  console.warn('Some functions may not work properly without these variables.');
}

// Export environment configuration
export const config = {
  // Database
  cosmosDb: {
    endpoint: process.env.COSMOS_DB_ENDPOINT || '',
    key: process.env.COSMOS_DB_KEY || '',
    database: process.env.COSMOS_DB_DATABASE || 'hepz-db'
  },
  
  // Storage
  storage: {
    connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING || ''
  },
  
  // AI Services
  ai: {
    azureOpenAI: {
      endpoint: process.env.AZURE_OPENAI_ENDPOINT || '',
      key: process.env.AZURE_OPENAI_KEY || '',
      deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT_NAME || 'gpt-4'
    },
    documentIntelligence: {
      endpoint: process.env.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT || '',
      key: process.env.AZURE_DOCUMENT_INTELLIGENCE_KEY || ''
    },
    search: {
      endpoint: process.env.AZURE_SEARCH_ENDPOINT || '',
      key: process.env.AZURE_SEARCH_KEY || '',
      indexName: process.env.AZURE_SEARCH_INDEX_NAME || 'documents'
    }
  },
  
  // Service Bus
  serviceBus: {
    connectionString: process.env.AZURE_SERVICE_BUS_CONNECTION_STRING || ''
  },
  
  // Email
  email: {
    postmarkServerToken: process.env.POSTMARK_SERVER_TOKEN || ''
  },
  
  // Redis
  redis: {
    connectionString: process.env.REDIS_CONNECTION_STRING || ''
  },
  
  // Application
  app: {
    environment: process.env.NODE_ENV || 'development',
    version: process.env.VERSION || '1.0.0',
    logLevel: process.env.LOG_LEVEL || 'info'
  }
};

export default config;
