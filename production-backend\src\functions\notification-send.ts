/**
 * Notification Send Function
 * Handles sending notifications to users through multiple channels
 * Enhanced with notification service integration
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService, NotificationRequest, NotificationChannelType } from '../shared/services/notification';

// Notification types enum
enum NotificationType {
  DOCUMENT_SHARED = 'DOCUMENT_SHARED',
  DOCUMENT_COMMENTED = 'DOCUMENT_COMMENTED',
  WORKFLOW_ASSIGNED = 'WORKFLOW_ASSIGNED',
  WORKFLOW_COMPLETED = 'WORKFLOW_COMPLETED',
  PROJECT_INVITATION = 'PROJECT_INVITATION',
  ORGANIZATION_INVITATION = 'ORGANIZATION_INVITATION',
  SYSTEM_ANNOUNCEMENT = 'SYSTEM_ANNOUNCEMENT',
  DOCUMENT_PROCESSED = 'DOCUMENT_PROCESSED',
  APPROVAL_REQUEST = 'APPROVAL_REQUEST'
}

// Notification priority enum
enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Validation schema
const sendNotificationSchema = Joi.object({
  recipientIds: Joi.array().items(Joi.string().uuid()).min(1).required(),
  type: Joi.string().valid(...Object.values(NotificationType)).required(),
  title: Joi.string().required().max(200),
  message: Joi.string().required().max(1000),
  priority: Joi.string().valid(...Object.values(NotificationPriority)).default(NotificationPriority.MEDIUM),
  actionUrl: Joi.string().uri().optional(),
  actionText: Joi.string().max(50).optional(),
  metadata: Joi.object().optional(),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional(),
  documentId: Joi.string().uuid().optional(),
  workflowId: Joi.string().uuid().optional(),
  expiresAt: Joi.date().iso().optional(),
  sendEmail: Joi.boolean().default(false),
  sendPush: Joi.boolean().default(true)
});

interface Notification {
  id: string;
  recipientId: string;
  senderId: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  actionUrl?: string;
  actionText?: string;
  metadata?: any;
  organizationId?: string;
  projectId?: string;
  documentId?: string;
  workflowId?: string;
  isRead: boolean;
  readAt?: string;
  createdAt: string;
  expiresAt?: string;
  tenantId?: string;
}

/**
 * Send notification handler
 */
export async function sendNotification(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Send notification started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = sendNotificationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const {
      recipientIds,
      type,
      title,
      message,
      priority,
      actionUrl,
      actionText,
      metadata,
      organizationId,
      projectId,
      documentId,
      workflowId,
      expiresAt,
      sendEmail,
      sendPush
    } = value;

    // Validate recipients exist and user has permission to send to them
    const validRecipients: string[] = [];
    for (const recipientId of recipientIds) {
      const recipient = await db.readItem('users', recipientId, recipientId);
      if (recipient) {
        // Check if user can send notifications to this recipient
        // For now, allow if they're in the same organization or it's a system notification
        if (organizationId) {
          const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
          const memberships = await db.queryItems('organization-members', membershipQuery, [recipientId, organizationId, 'active']);
          if (memberships.length > 0) {
            validRecipients.push(recipientId);
          }
        } else {
          // Allow system notifications or direct notifications
          validRecipients.push(recipientId);
        }
      }
    }

    if (validRecipients.length === 0) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "No valid recipients found" }
      }, request);
    }

    // Send notifications using the notification service
    const notificationResults = [];
    const notificationIds: string[] = [];

    for (const recipientId of validRecipients) {
      try {
        // Prepare notification channels
        const channels: NotificationChannelType[] = [];

        if (sendEmail) {
          channels.push(NotificationChannelType.EMAIL);
        }

        if (sendPush) {
          channels.push(NotificationChannelType.PUSH);
        }

        // Prepare notification request
        const notificationRequest: NotificationRequest = {
          userId: recipientId,
          type,
          title,
          message,
          resourceId: documentId || workflowId || projectId,
          resourceType: documentId ? 'document' : workflowId ? 'workflow' : projectId ? 'project' : undefined,
          priority: priority.toLowerCase() as 'low' | 'normal' | 'high' | 'urgent',
          metadata: {
            ...metadata,
            actionUrl,
            actionText,
            expiresAt,
            senderId: user.id
          },
          channels: channels.length > 0 ? channels : undefined,
          organizationId,
          projectId
        };

        // Send notification through the service
        const result = await notificationService.sendNotification(notificationRequest);

        if (result.success && result.notificationId) {
          notificationIds.push(result.notificationId);
        }

        notificationResults.push({
          recipientId,
          success: result.success,
          notificationId: result.notificationId,
          channels: result.channels
        });

      } catch (error) {
        logger.error("Failed to send notification to recipient", {
          error,
          recipientId,
          correlationId
        });

        notificationResults.push({
          recipientId,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "notification_sent",
      userId: user.id,
      organizationId,
      projectId,
      timestamp: new Date().toISOString(),
      details: {
        notificationType: type,
        recipientCount: validRecipients.length,
        priority,
        title
      },
      tenantId: user.tenantId
    });

    const successCount = notificationResults.filter(r => r.success).length;
    const failureCount = notificationResults.length - successCount;

    logger.info("Notifications sent successfully", {
      correlationId,
      senderId: user.id,
      recipientCount: validRecipients.length,
      successCount,
      failureCount,
      type,
      priority
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        success: successCount > 0,
        notificationIds,
        recipientCount: validRecipients.length,
        successCount,
        failureCount,
        results: notificationResults,
        message: `${successCount} notifications sent successfully${failureCount > 0 ? `, ${failureCount} failed` : ''}`
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Send notification failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('notification-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/send',
  handler: sendNotification
});
