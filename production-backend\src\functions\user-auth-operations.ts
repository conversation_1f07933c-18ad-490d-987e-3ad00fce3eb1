/**
 * User Auth Operations Functions
 * Handles logout, refresh token, and user registration operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import jwt from "jsonwebtoken";
import * as bcrypt from "bcryptjs";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { serviceBusEnhanced } from '../shared/services/service-bus';
import { publishEvent, EventType } from './event-grid-handlers';

// Validation schemas
const logoutSchema = Joi.object({
  refreshToken: Joi.string().optional(),
  allDevices: Joi.boolean().optional()
}).or('refreshToken', 'allDevices');

const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required()
});

const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  firstName: Joi.string().required(),
  lastName: Joi.string().required(),
  organizationId: Joi.string().uuid().optional(),
  invitationCode: Joi.string().optional()
});

/**
 * User logout handler
 */
export async function userLogout(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("User logout started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = logoutSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { refreshToken, allDevices } = value;

    // Get current user data
    const currentUser = await db.readItem('users', user.id, user.id);
    if (!currentUser) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User not found" }
      }, request);
    }

    let updatedUser: any;

    if (allDevices) {
      // Log out from all devices by removing all refresh tokens
      updatedUser = {
        ...currentUser,
        id: (currentUser as any).id,
        refreshTokens: [],
        lastLogoutAt: new Date().toISOString()
      };
      logger.info("User logged out from all devices", { userId: user.id, correlationId });
    } else if (refreshToken) {
      // Log out from specific device by removing the specific refresh token
      const refreshTokens = (currentUser as any).refreshTokens || [];
      const updatedTokens = refreshTokens.filter((t: any) => t.token !== refreshToken);

      updatedUser = {
        ...currentUser,
        id: (currentUser as any).id,
        refreshTokens: updatedTokens,
        lastLogoutAt: new Date().toISOString()
      };
      logger.info("User logged out from specific device", { userId: user.id, correlationId });
    }

    if (updatedUser) {
      await db.updateItem('users', updatedUser);
    }

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { message: "Logged out successfully" }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("User logout failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Refresh token handler
 */
export async function refreshToken(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Refresh token started", { correlationId });

  try {
    // Validate request body
    const body = await request.json();
    const { error, value } = refreshTokenSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { refreshToken: token } = value;

    // Find user by refresh token
    const query = 'SELECT * FROM c WHERE ARRAY_CONTAINS(c.refreshTokens, {"token": @token}, true)';
    const users = await db.queryItems('users', query, [token]);

    if (users.length === 0) {
      logger.warn("Refresh token attempt with invalid token", { correlationId });
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Invalid refresh token" }
      }, request);
    }

    const user = users[0] as any;

    // Find the specific token info
    const tokenInfo = user.refreshTokens.find((t: any) => t.token === token);
    if (!tokenInfo) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Invalid refresh token" }
      }, request);
    }

    // Check if token is expired
    const expiresAt = new Date(tokenInfo.expiresAt);
    if (expiresAt < new Date()) {
      logger.warn("Refresh token attempt with expired token", { userId: user.id, correlationId });

      // Remove expired token
      const updatedTokens = user.refreshTokens.filter((t: any) => t.token !== token);
      const updatedUser = {
        ...user,
        refreshTokens: updatedTokens
      };
      await db.updateItem('users', updatedUser);

      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Refresh token expired" }
      }, request);
    }

    // Generate new tokens
    const newRefreshToken = uuidv4();
    const refreshTokenExpiry = new Date();
    refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30 days

    const jwtSecret = process.env.AUTH_CLIENT_SECRET;
    if (!jwtSecret) {
      logger.error("AUTH_CLIENT_SECRET environment variable is not set", { correlationId });
      return addCorsHeaders({
        status: 500,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Authentication service misconfigured" }
      }, request);
    }

    const now = Math.floor(Date.now() / 1000);
    const expiresIn = 3600; // 1 hour

    const accessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.displayName || `${user.firstName} ${user.lastName}`,
        tenantId: user.tenantId,
        roles: user.roles || [],
        systemRoles: user.systemRoles || [],
        organizationIds: user.organizationIds || [],
        defaultOrganizationId: user.defaultOrganizationId,
        iat: now,
        exp: now + expiresIn,
        jti: uuidv4(),
        auth_time: now
      },
      jwtSecret,
      {
        expiresIn: `${expiresIn}s`,
        issuer: process.env.AUTH_ISSUER || "hepz-platform",
        audience: process.env.AUTH_CLIENT_ID || "hepz-client",
        algorithm: "HS256"
      }
    );

    // Update refresh tokens in database
    const updatedTokens = user.refreshTokens.filter((t: any) => t.token !== token);
    updatedTokens.push({
      token: newRefreshToken,
      expiresAt: refreshTokenExpiry.toISOString(),
      createdAt: new Date().toISOString(),
      userAgent: request.headers.get("user-agent") || "unknown",
      ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown"
    });

    const updatedUser = {
      ...user,
      refreshTokens: updatedTokens,
      lastTokenRefreshAt: new Date().toISOString()
    };

    await db.updateItem('users', updatedUser);

    logger.info("Token refreshed successfully", { userId: user.id, correlationId });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn,
        tokenType: "Bearer"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Refresh token failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * User registration handler
 */
export async function userRegister(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("User registration started", { correlationId });

  try {
    // Validate request body
    const body = await request.json();
    const { error, value } = registerSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { email, password, firstName, lastName, organizationId, invitationCode: _invitationCode } = value;

    // Check if user already exists
    const query = 'SELECT * FROM c WHERE c.email = @email';
    const existingUsers = await db.queryItems('users', query, [email.toLowerCase()]);

    if (existingUsers.length > 0) {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User with this email already exists" }
      }, request);
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash(password, salt);

    // Create new user
    const userId = uuidv4();
    const newUser = {
      id: userId,
      email: email.toLowerCase(),
      firstName,
      lastName,
      displayName: `${firstName} ${lastName}`,
      passwordHash,
      tenantId: organizationId || uuidv4(),
      roles: [],
      systemRoles: [],
      organizationIds: organizationId ? [organizationId] : [],
      defaultOrganizationId: organizationId,
      status: "active",
      refreshTokens: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      preferences: {
        theme: "system",
        language: "en",
        timezone: "UTC",
        dateFormat: "MM/DD/YYYY",
        notifications: {
          email: true,
          inApp: true,
          documentUploaded: true,
          documentProcessed: true,
          commentAdded: true,
          mentionedInComment: true,
          projectInvitation: true,
          organizationInvitation: true
        }
      }
    };

    await db.createItem('users', newUser);

    // Cache user profile in Redis
    const userProfile = {
      id: userId,
      email: email.toLowerCase(),
      firstName,
      lastName,
      displayName: `${firstName} ${lastName}`,
      organizationIds: organizationId ? [organizationId] : [],
      defaultOrganizationId: organizationId,
      status: "active",
      preferences: newUser.preferences
    };
    await redis.setJson(`user:${userId}:profile`, userProfile, 3600); // 1 hour cache

    // Publish Event Grid event
    await publishEvent(
      EventType.USER_REGISTERED,
      `users/${userId}/registered`,
      {
        userId,
        email: email.toLowerCase(),
        firstName,
        lastName,
        organizationId,
        registrationMethod: 'email',
        timestamp: new Date().toISOString()
      }
    );

    // Send Service Bus message for welcome workflow
    await serviceBusEnhanced.sendToQueue('notification-delivery', {
      body: {
        eventType: 'user_registered',
        userId,
        email: email.toLowerCase(),
        firstName,
        lastName,
        organizationId,
        timestamp: new Date().toISOString()
      },
      messageId: `user-register-${userId}-${Date.now()}`,
      correlationId: `user-${userId}`,
      subject: 'user.registered'
    });

    logger.info("User registered successfully", {
      userId,
      email: email.toLowerCase(),
      correlationId
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        message: "User registered successfully",
        userId,
        email: email.toLowerCase()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("User registration failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('auth-logout', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'anonymous',
  route: 'auth/logout',
  handler: userLogout
});

app.http('auth-refresh', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'anonymous',
  route: 'auth/refresh',
  handler: refreshToken
});

app.http('auth-register', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'anonymous',
  route: 'auth/register',
  handler: userRegister
});
