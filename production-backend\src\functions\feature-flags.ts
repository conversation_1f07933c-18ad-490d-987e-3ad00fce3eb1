/**
 * Feature Flags Function
 * Handles feature flag management and evaluation
 * Migrated from old-arch/src/admin-service/feature-flags/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Feature flag types and enums
enum FeatureFlagType {
  BOOLEAN = 'BOOLEAN',
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  JSON = 'JSON',
  PERCENTAGE = 'PERCENTAGE'
}

enum FeatureFlagStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ARCHIVED = 'ARCHIVED'
}

enum TargetType {
  ALL = 'ALL',
  USER = 'USER',
  ORGANIZATION = 'ORGANIZATION',
  TENANT = 'TENANT',
  PERCENTAGE = 'PERCENTAGE'
}

// Validation schemas
const createFeatureFlagSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  key: Joi.string().pattern(/^[a-zA-Z0-9_-]+$/).min(2).max(50).required(),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(FeatureFlagType)).required(),
  defaultValue: Joi.any().required(),
  variations: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    value: Joi.any().required(),
    description: Joi.string().optional()
  })).optional(),
  targeting: Joi.object({
    enabled: Joi.boolean().default(false),
    rules: Joi.array().items(Joi.object({
      id: Joi.string().optional(),
      name: Joi.string().required(),
      targetType: Joi.string().valid(...Object.values(TargetType)).required(),
      targets: Joi.array().items(Joi.string()).optional(),
      percentage: Joi.number().min(0).max(100).optional(),
      value: Joi.any().required(),
      enabled: Joi.boolean().default(true)
    })).optional()
  }).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(20).optional(),
  metadata: Joi.object().optional()
});

const evaluateFeatureFlagSchema = Joi.object({
  flagKey: Joi.string().required(),
  context: Joi.object({
    userId: Joi.string().uuid().optional(),
    organizationId: Joi.string().uuid().optional(),
    tenantId: Joi.string().uuid().optional(),
    userAgent: Joi.string().optional(),
    ipAddress: Joi.string().ip().optional(),
    custom: Joi.object().optional()
  }).optional()
});

interface CreateFeatureFlagRequest {
  name: string;
  key: string;
  description?: string;
  type: FeatureFlagType;
  defaultValue: any;
  variations?: Array<{
    name: string;
    value: any;
    description?: string;
  }>;
  targeting?: {
    enabled?: boolean;
    rules?: Array<{
      id?: string;
      name: string;
      targetType: TargetType;
      targets?: string[];
      percentage?: number;
      value: any;
      enabled?: boolean;
    }>;
  };
  tags?: string[];
  metadata?: any;
}

interface FeatureFlag {
  id: string;
  name: string;
  key: string;
  description?: string;
  type: FeatureFlagType;
  status: FeatureFlagStatus;
  defaultValue: any;
  variations: Array<{
    name: string;
    value: any;
    description?: string;
  }>;
  targeting: {
    enabled: boolean;
    rules: Array<{
      id: string;
      name: string;
      targetType: TargetType;
      targets?: string[];
      percentage?: number;
      value: any;
      enabled: boolean;
    }>;
  };
  tags: string[];
  metadata: any;
  statistics: {
    evaluationCount: number;
    lastEvaluated?: string;
    variationCounts: { [key: string]: number };
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create feature flag handler
 */
export async function createFeatureFlag(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create feature flag started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkFeatureFlagAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to feature flag management" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = createFeatureFlagSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const flagRequest: CreateFeatureFlagRequest = value;

    // Check if flag key already exists
    const keyExists = await checkFeatureFlagKeyExists(flagRequest.key);
    if (keyExists) {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Feature flag key already exists" }
      }, request);
    }

    // Create feature flag
    const flagId = uuidv4();
    const now = new Date().toISOString();

    const featureFlag: FeatureFlag = {
      id: flagId,
      name: flagRequest.name,
      key: flagRequest.key,
      description: flagRequest.description,
      type: flagRequest.type,
      status: FeatureFlagStatus.ACTIVE,
      defaultValue: flagRequest.defaultValue,
      variations: (flagRequest.variations || []).map((variation: any) => ({
        id: uuidv4(),
        name: variation.name,
        targetType: variation.targetType || 'USER',
        targets: variation.targets || [],
        percentage: variation.percentage || 0,
        value: variation.value,
        enabled: true
      })),
      targeting: {
        enabled: false,
        rules: (flagRequest.targeting?.rules || []) as any,
        ...flagRequest.targeting
      },
      tags: flagRequest.tags || [],
      metadata: flagRequest.metadata || {},
      statistics: {
        evaluationCount: 0,
        variationCounts: {}
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    // Add IDs to targeting rules
    if (featureFlag.targeting.rules) {
      featureFlag.targeting.rules = featureFlag.targeting.rules.map(rule => ({
        ...rule,
        id: rule.id || uuidv4(),
        enabled: rule.enabled !== false
      }));
    }

    await db.createItem('feature-flags', featureFlag);

    // Cache feature flag for fast evaluation
    await cacheFeatureFlag(featureFlag);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "feature_flag_created",
      userId: user.id,
      timestamp: now,
      details: {
        flagId,
        flagKey: flagRequest.key,
        flagName: flagRequest.name,
        flagType: flagRequest.type,
        targetingEnabled: featureFlag.targeting.enabled,
        ruleCount: featureFlag.targeting.rules?.length || 0
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'FeatureFlagCreated',
      aggregateId: flagId,
      aggregateType: 'FeatureFlag',
      version: 1,
      data: {
        featureFlag,
        createdBy: user.id
      },
      userId: user.id,
      tenantId: user.tenantId
    });

    logger.info("Feature flag created successfully", {
      correlationId,
      flagId,
      flagKey: flagRequest.key,
      flagName: flagRequest.name,
      flagType: flagRequest.type,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: flagId,
        name: flagRequest.name,
        key: flagRequest.key,
        type: flagRequest.type,
        status: FeatureFlagStatus.ACTIVE,
        defaultValue: flagRequest.defaultValue,
        targeting: featureFlag.targeting,
        createdAt: now,
        message: "Feature flag created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create feature flag failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Evaluate feature flag handler
 */
export async function evaluateFeatureFlag(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Evaluate feature flag started", { correlationId });

  try {
    // Parse query parameters or body
    let evaluationRequest: any;

    if (request.method === 'GET') {
      const url = new URL(request.url);
      evaluationRequest = {
        flagKey: url.searchParams.get('flagKey'),
        context: {
          userId: url.searchParams.get('userId'),
          organizationId: url.searchParams.get('organizationId'),
          tenantId: url.searchParams.get('tenantId')
        }
      };
    } else {
      const body = await request.json();
      evaluationRequest = body;
    }

    // Validate request
    const { error, value } = evaluateFeatureFlagSchema.validate(evaluationRequest);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { flagKey, context } = value;

    // Get feature flag (try cache first)
    const featureFlag = await getFeatureFlag(flagKey);
    if (!featureFlag) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Feature flag not found" }
      }, request);
    }

    // Evaluate feature flag
    const evaluation = await evaluateFlag(featureFlag, context || {});

    // Update statistics
    await updateFlagStatistics(featureFlag.id, evaluation.value);

    logger.info("Feature flag evaluated successfully", {
      correlationId,
      flagKey,
      flagValue: evaluation.value,
      reason: evaluation.reason,
      userId: context?.userId
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        flagKey,
        value: evaluation.value,
        reason: evaluation.reason,
        ruleId: evaluation.ruleId,
        evaluatedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Evaluate feature flag failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkFeatureFlagAccess(user: any): Promise<boolean> {
  try {
    // Check if user has admin or feature flag management role
    return user.roles?.includes('admin') || user.roles?.includes('feature_flag_admin');
  } catch (error) {
    logger.error('Failed to check feature flag access', { error, userId: user.id });
    return false;
  }
}

async function checkFeatureFlagKeyExists(key: string): Promise<boolean> {
  try {
    const existingQuery = 'SELECT * FROM c WHERE c.key = @key';
    const existing = await db.queryItems('feature-flags', existingQuery, [key]);
    return existing.length > 0;
  } catch (error) {
    logger.error('Failed to check feature flag key exists', { error, key });
    return false;
  }
}

async function cacheFeatureFlag(featureFlag: FeatureFlag): Promise<void> {
  try {
    const cacheKey = `feature_flag:${featureFlag.key}`;
    const flagData = JSON.stringify(featureFlag);

    await redis.setex(cacheKey, 300, flagData); // 5 minutes cache

    // Also cache in a set for bulk operations
    await redis.sadd('feature_flags:active', featureFlag.key);

  } catch (error) {
    logger.error('Failed to cache feature flag', { error, flagId: featureFlag.id });
  }
}

async function getFeatureFlag(key: string): Promise<FeatureFlag | null> {
  try {
    const { cacheAside } = await import('../shared/services/cache-aside');

    return await cacheAside.get<FeatureFlag>(
      key,
      {
        containerName: 'feature-flags',
        query: 'SELECT * FROM c WHERE c.key = @key AND c.status = @active',
        parameters: [key, FeatureFlagStatus.ACTIVE]
      },
      {
        ttlSeconds: 300, // 5 minutes cache
        cachePrefix: 'feature_flag',
        enableFallback: true,
        enableWarming: true,
        warmingPriority: 'high',
        eventDriven: true
      }
    );

  } catch (error) {
    logger.error('Failed to get feature flag', { error, key });
    return null;
  }
}

async function evaluateFlag(featureFlag: FeatureFlag, context: any): Promise<any> {
  try {
    // If targeting is disabled, return default value
    if (!featureFlag.targeting.enabled) {
      return {
        value: featureFlag.defaultValue,
        reason: 'DEFAULT',
        ruleId: null
      };
    }

    // Evaluate targeting rules in order
    for (const rule of featureFlag.targeting.rules || []) {
      if (!rule.enabled) continue;

      const matches = await evaluateRule(rule, context);
      if (matches) {
        return {
          value: rule.value,
          reason: 'RULE_MATCH',
          ruleId: rule.id
        };
      }
    }

    // No rules matched, return default
    return {
      value: featureFlag.defaultValue,
      reason: 'DEFAULT',
      ruleId: null
    };

  } catch (error) {
    logger.error('Failed to evaluate feature flag', { error, flagId: featureFlag.id });
    return {
      value: featureFlag.defaultValue,
      reason: 'ERROR',
      ruleId: null
    };
  }
}

async function evaluateRule(rule: any, context: any): Promise<boolean> {
  try {
    switch (rule.targetType) {
      case TargetType.ALL:
        return true;

      case TargetType.USER:
        return context.userId && rule.targets?.includes(context.userId);

      case TargetType.ORGANIZATION:
        return context.organizationId && rule.targets?.includes(context.organizationId);

      case TargetType.TENANT:
        return context.tenantId && rule.targets?.includes(context.tenantId);

      case TargetType.PERCENTAGE:
        if (rule.percentage !== undefined) {
          // Use consistent hashing for percentage rollouts
          const hash = hashString(context.userId || context.organizationId || 'anonymous');
          const percentage = (hash % 100) + 1;
          return percentage <= rule.percentage;
        }
        return false;

      default:
        return false;
    }
  } catch (error) {
    logger.error('Failed to evaluate rule', { error, ruleId: rule.id });
    return false;
  }
}

function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

async function updateFlagStatistics(flagId: string, value: any): Promise<void> {
  try {
    // Update statistics in Redis for performance
    const statsKey = `feature_flag_stats:${flagId}`;
    const valueKey = JSON.stringify(value);

    await redis.hincrby(statsKey, 'evaluationCount', 1);
    await redis.hincrby(statsKey, `variation:${valueKey}`, 1);
    await redis.hset(statsKey, 'lastEvaluated', new Date().toISOString());
    await redis.expire(statsKey, 86400); // 24 hours

  } catch (error) {
    logger.error('Failed to update flag statistics', { error, flagId });
  }
}

// Register functions
app.http('feature-flag-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/feature-flags/create',
  handler: createFeatureFlag
});

app.http('feature-flag-evaluate', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'feature-flags/evaluate',
  handler: evaluateFeatureFlag
});
