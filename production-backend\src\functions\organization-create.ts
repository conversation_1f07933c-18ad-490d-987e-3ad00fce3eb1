/**
 * Organization Create Function
 * Handles creating new organizations with proper tier management
 * Enhanced with notification service and event system integration
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { serviceBusEnhanced } from '../shared/services/service-bus';
import { publishEvent, EventType } from './event-grid-handlers';
import { listOrganizations } from './organization-list';
import { notificationService } from '../shared/services/notification';


// Organization tiers enum
enum OrganizationTier {
  FREE = 'FREE',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE'
}

// User roles enum
enum UserRole {
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
  VIEWER = 'VIEWER'
}

// Validation schema
const createOrganizationSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  tier: Joi.string().valid(...Object.values(OrganizationTier)).default(OrganizationTier.FREE)
});

interface Organization {
  id: string;
  name: string;
  description: string;
  tier: OrganizationTier;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  projectIds: string[];
  memberIds: string[];
  teamIds: string[];
  settings: {
    allowedDocumentTypes: string[];
    maxFileSize: number;
    maxProjects: number;
    features: {
      aiAnalysis: boolean;
      advancedWorkflows: boolean;
      bulkProcessing: boolean;
      apiAccess: boolean;
    };
  };
  tenantId?: string;
}

/**
 * Create organization handler
 */
export async function createOrganization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create organization started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createOrganizationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { name, description, tier } = value;

    // Check if user already has organizations (for free tier limits)
    if (tier === OrganizationTier.FREE) {
      const existingOrgsQuery = 'SELECT * FROM c WHERE c.createdBy = @userId AND c.tier = @tier';
      const existingOrgs = await db.queryItems('organizations', existingOrgsQuery, [user.id, OrganizationTier.FREE]);

      if (existingOrgs.length >= 1) {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: {
            error: "Free tier limit reached",
            message: "You can only create one free organization. Please upgrade to create more."
          }
        }, request);
      }
    }

    // Create organization
    const organizationId = uuidv4();
    const organization: Organization = {
      id: organizationId,
      name,
      description: description || "",
      tier,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      projectIds: [],
      memberIds: [user.id],
      teamIds: [],
      settings: {
        allowedDocumentTypes: ["pdf", "docx", "xlsx", "pptx", "jpg", "png"],
        maxFileSize: 20 * 1024 * 1024, // 20MB
        maxProjects: tier === OrganizationTier.FREE ? 3 :
                    tier === OrganizationTier.PROFESSIONAL ? 10 :
                    tier === OrganizationTier.ENTERPRISE ? 100 : 3,
        features: {
          aiAnalysis: tier !== OrganizationTier.FREE,
          advancedWorkflows: tier === OrganizationTier.ENTERPRISE,
          bulkProcessing: tier !== OrganizationTier.FREE,
          apiAccess: tier === OrganizationTier.ENTERPRISE
        }
      },
      tenantId: user.tenantId
    };

    await db.createItem('organizations', organization);

    // Add creator as admin member
    const membership = {
      id: uuidv4(),
      userId: user.id,
      organizationId,
      role: UserRole.ADMIN,
      joinedAt: new Date().toISOString(),
      invitedBy: user.id,
      status: "active",
      permissions: [],
      tenantId: user.tenantId
    };

    await db.createItem('organization-members', membership);

    // Update user's organizations list
    const currentUser = await db.readItem('users', user.id, user.id);
    if (currentUser) {
      const updatedUser = {
        ...(currentUser as any),
        id: user.id,
        organizationIds: [...((currentUser as any).organizationIds || []), organizationId],
        updatedAt: new Date().toISOString()
      };
      await db.updateItem('users', updatedUser);
    }

    // Cache organization data in Redis
    const organizationWithStats = {
      ...organization,
      memberCount: 1,
      projectCount: 0,
      documentCount: 0,
      storageUsed: 0,
      userRole: 'admin'
    };
    await redis.setJson(`org:${organizationId}:details`, organizationWithStats, 1800); // 30 minutes cache

    // Invalidate user organizations cache
    await redis.del(`user:${user.id}:organizations`);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "organization_created",
      userId: user.id,
      organizationId,
      timestamp: new Date().toISOString(),
      details: {
        organizationName: name,
        tier,
        memberCount: 1
      },
      tenantId: user.tenantId
    });

    // Publish Event Grid event
    await publishEvent(
      EventType.ORGANIZATION_CREATED,
      `organizations/${organizationId}/created`,
      {
        organizationId,
        organizationName: name,
        tier,
        createdBy: user.id,
        memberCount: 1,
        timestamp: new Date().toISOString()
      }
    );

    // Send Service Bus message for workflow orchestration
    await serviceBusEnhanced.sendToQueue('analytics-events', {
      body: {
        eventType: 'organization_created',
        organizationId,
        organizationName: name,
        tier,
        createdBy: user.id,
        timestamp: new Date().toISOString()
      },
      messageId: `org-create-${organizationId}-${Date.now()}`,
      correlationId: `org-${organizationId}`,
      subject: 'organization.created'
    });

    // Send welcome notification
    await notificationService.sendNotification({
      userId: user.id,
      type: 'ORGANIZATION_CREATED',
      title: 'Welcome to your new organization!',
      message: `Your organization "${name}" has been created successfully. You can now invite team members and start creating projects.`,
      priority: 'normal',
      metadata: {
        organizationId,
        organizationName: name,
        tier
      },
      organizationId
    });

    logger.info("Organization created successfully", {
      correlationId,
      organizationId,
      userId: user.id,
      tier
    });

    // Return the complete organization object
    const responseOrganization = {
      id: organizationId,
      name,
      description: description || "",
      tier,
      createdBy: user.id,
      createdAt: organization.createdAt,
      updatedBy: user.id,
      updatedAt: organization.updatedAt,
      projectIds: [],
      memberIds: [user.id],
      teamIds: [],
      settings: organization.settings,
      memberCount: 1,
      projectCount: 0,
      documentCount: 0,
      storageUsed: 0
    };

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: responseOrganization
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create organization failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Combined organizations handler
 */
async function handleOrganizations(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await createOrganization(request, context);
    case 'GET':
      return await listOrganizations(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('organizations', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations',
  handler: handleOrganizations
});
