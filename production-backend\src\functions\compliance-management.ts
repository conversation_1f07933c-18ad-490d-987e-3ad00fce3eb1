/**
 * Compliance Management Function
 * Handles compliance requirements, auditing, and regulatory compliance
 * Migrated from old-arch/src/compliance-service/management/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Compliance types and enums
enum ComplianceFramework {
  GDPR = 'GDPR',
  HIPAA = 'HIPAA',
  SOX = 'SOX',
  PCI_DSS = 'PCI_DSS',
  ISO_27001 = 'ISO_27001',
  SOC2 = 'SOC2',
  CCPA = 'CCPA',
  FERPA = 'FERPA'
}

enum ComplianceStatus {
  COMPLIANT = 'COMPLIANT',
  NON_COMPLIANT = 'NON_COMPLIANT',
  PENDING_REVIEW = 'PENDING_REVIEW',
  IN_PROGRESS = 'IN_PROGRESS',
  EXEMPT = 'EXEMPT'
}

enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Validation schemas
const createComplianceAssessmentSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  framework: Joi.string().valid(...Object.values(ComplianceFramework)).required(),
  organizationId: Joi.string().uuid().required(),
  scope: Joi.object({
    departments: Joi.array().items(Joi.string()).optional(),
    systems: Joi.array().items(Joi.string()).optional(),
    dataTypes: Joi.array().items(Joi.string()).optional(),
    geographies: Joi.array().items(Joi.string()).optional()
  }).optional(),
  requirements: Joi.array().items(Joi.object({
    id: Joi.string().required(),
    title: Joi.string().required(),
    description: Joi.string().required(),
    category: Joi.string().required(),
    mandatory: Joi.boolean().default(true),
    evidence: Joi.array().items(Joi.string()).optional()
  })).required(),
  schedule: Joi.object({
    startDate: Joi.string().isoDate().required(),
    endDate: Joi.string().isoDate().required(),
    reviewFrequency: Joi.string().valid('monthly', 'quarterly', 'annually').default('annually')
  }).required(),
  assignees: Joi.array().items(Joi.object({
    userId: Joi.string().uuid().required(),
    role: Joi.string().valid('owner', 'reviewer', 'contributor').required()
  })).min(1).required()
});

const updateComplianceStatusSchema = Joi.object({
  assessmentId: Joi.string().uuid().required(),
  requirementId: Joi.string().required(),
  status: Joi.string().valid(...Object.values(ComplianceStatus)).required(),
  evidence: Joi.array().items(Joi.object({
    type: Joi.string().valid('document', 'screenshot', 'policy', 'procedure', 'certificate').required(),
    title: Joi.string().required(),
    description: Joi.string().optional(),
    url: Joi.string().uri().optional(),
    uploadedBy: Joi.string().uuid().required(),
    uploadedAt: Joi.string().isoDate().required()
  })).optional(),
  notes: Joi.string().max(1000).optional(),
  riskLevel: Joi.string().valid(...Object.values(RiskLevel)).optional(),
  remediation: Joi.object({
    required: Joi.boolean().default(false),
    dueDate: Joi.string().isoDate().optional(),
    assignedTo: Joi.string().uuid().optional(),
    description: Joi.string().optional(),
    priority: Joi.string().valid('low', 'medium', 'high', 'critical').optional()
  }).optional()
});

interface CreateComplianceAssessmentRequest {
  name: string;
  description?: string;
  framework: ComplianceFramework;
  organizationId: string;
  scope?: {
    departments?: string[];
    systems?: string[];
    dataTypes?: string[];
    geographies?: string[];
  };
  requirements: Array<{
    id: string;
    title: string;
    description: string;
    category: string;
    mandatory?: boolean;
    evidence?: string[];
  }>;
  schedule: {
    startDate: string;
    endDate: string;
    reviewFrequency?: string;
  };
  assignees: Array<{
    userId: string;
    role: string;
  }>;
}

interface ComplianceAssessment {
  id: string;
  name: string;
  description?: string;
  framework: ComplianceFramework;
  status: ComplianceStatus;
  organizationId: string;
  scope: {
    departments: string[];
    systems: string[];
    dataTypes: string[];
    geographies: string[];
  };
  requirements: Array<{
    id: string;
    title: string;
    description: string;
    category: string;
    mandatory: boolean;
    status: ComplianceStatus;
    evidence: Array<{
      type: string;
      title: string;
      description?: string;
      url?: string;
      uploadedBy: string;
      uploadedAt: string;
    }>;
    lastReviewed?: string;
    reviewedBy?: string;
    notes?: string;
    riskLevel?: RiskLevel;
    remediation?: {
      required: boolean;
      dueDate?: string;
      assignedTo?: string;
      description?: string;
      priority?: string;
      status?: string;
    };
  }>;
  schedule: {
    startDate: string;
    endDate: string;
    reviewFrequency: string;
    nextReview?: string;
  };
  assignees: Array<{
    userId: string;
    role: string;
    assignedAt: string;
  }>;
  statistics: {
    totalRequirements: number;
    compliantRequirements: number;
    nonCompliantRequirements: number;
    pendingRequirements: number;
    compliancePercentage: number;
    lastUpdated: string;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

/**
 * Create compliance assessment handler
 */
export async function createComplianceAssessment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create compliance assessment started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createComplianceAssessmentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const assessmentRequest: CreateComplianceAssessmentRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(assessmentRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check compliance access
    const hasComplianceAccess = await checkComplianceAccess(user, assessmentRequest.organizationId);
    if (!hasComplianceAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to compliance management" }
      }, request);
    }

    // Create compliance assessment
    const assessmentId = uuidv4();
    const now = new Date().toISOString();

    const assessment: ComplianceAssessment = {
      id: assessmentId,
      name: assessmentRequest.name,
      description: assessmentRequest.description,
      framework: assessmentRequest.framework,
      status: ComplianceStatus.IN_PROGRESS,
      organizationId: assessmentRequest.organizationId,
      scope: {
        departments: [],
        systems: [],
        dataTypes: [],
        geographies: [],
        ...assessmentRequest.scope
      },
      requirements: assessmentRequest.requirements.map(req => ({
        ...req,
        mandatory: req.mandatory !== false,
        status: ComplianceStatus.PENDING_REVIEW,
        evidence: []
      })),
      schedule: {
        ...assessmentRequest.schedule,
        reviewFrequency: assessmentRequest.schedule.reviewFrequency || 'annually',
        nextReview: calculateNextReview(assessmentRequest.schedule.endDate, assessmentRequest.schedule.reviewFrequency || 'annually')
      },
      assignees: assessmentRequest.assignees.map(assignee => ({
        ...assignee,
        assignedAt: now
      })),
      statistics: {
        totalRequirements: assessmentRequest.requirements.length,
        compliantRequirements: 0,
        nonCompliantRequirements: 0,
        pendingRequirements: assessmentRequest.requirements.length,
        compliancePercentage: 0,
        lastUpdated: now
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('compliance-assessments', assessment);

    // Create notifications for assignees
    await notifyAssignees(assessment, 'assessment_created');

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "compliance_assessment_created",
      userId: user.id,
      organizationId: assessmentRequest.organizationId,
      timestamp: now,
      details: {
        assessmentId,
        assessmentName: assessmentRequest.name,
        framework: assessmentRequest.framework,
        requirementCount: assessmentRequest.requirements.length,
        assigneeCount: assessmentRequest.assignees.length
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'ComplianceAssessmentCreated',
      aggregateId: assessmentId,
      aggregateType: 'ComplianceAssessment',
      version: 1,
      data: {
        assessment,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: assessmentRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Compliance assessment created successfully", {
      correlationId,
      assessmentId,
      assessmentName: assessmentRequest.name,
      framework: assessmentRequest.framework,
      requirementCount: assessmentRequest.requirements.length,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: assessmentId,
        name: assessmentRequest.name,
        framework: assessmentRequest.framework,
        status: ComplianceStatus.IN_PROGRESS,
        organizationId: assessmentRequest.organizationId,
        requirementCount: assessmentRequest.requirements.length,
        assigneeCount: assessmentRequest.assignees.length,
        schedule: assessment.schedule,
        statistics: assessment.statistics,
        createdAt: now,
        message: "Compliance assessment created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create compliance assessment failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Update compliance status handler
 */
export async function updateComplianceStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Update compliance status started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = updateComplianceStatusSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const updateRequest = value;

    // Get compliance assessment
    const assessment = await db.readItem('compliance-assessments', updateRequest.assessmentId, updateRequest.assessmentId);
    if (!assessment) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Compliance assessment not found" }
      }, request);
    }

    const assessmentData = assessment as any;

    // Check access
    const hasAccess = await checkComplianceAccess(user, assessmentData.organizationId);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to compliance assessment" }
      }, request);
    }

    // Find and update requirement
    const requirementIndex = assessmentData.requirements.findIndex((req: any) => req.id === updateRequest.requirementId);
    if (requirementIndex === -1) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Requirement not found" }
      }, request);
    }

    const now = new Date().toISOString();

    // Update requirement
    assessmentData.requirements[requirementIndex] = {
      ...assessmentData.requirements[requirementIndex],
      status: updateRequest.status,
      evidence: updateRequest.evidence || assessmentData.requirements[requirementIndex].evidence,
      lastReviewed: now,
      reviewedBy: user.id,
      notes: updateRequest.notes,
      riskLevel: updateRequest.riskLevel,
      remediation: updateRequest.remediation
    };

    // Recalculate statistics
    const statistics = calculateComplianceStatistics(assessmentData.requirements);
    assessmentData.statistics = {
      ...statistics,
      lastUpdated: now
    };

    // Update overall assessment status
    assessmentData.status = determineOverallStatus(statistics);
    assessmentData.updatedAt = now;

    await db.updateItem('compliance-assessments', assessmentData);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "compliance_status_updated",
      userId: user.id,
      organizationId: assessmentData.organizationId,
      timestamp: now,
      details: {
        assessmentId: updateRequest.assessmentId,
        requirementId: updateRequest.requirementId,
        oldStatus: assessmentData.requirements[requirementIndex].status,
        newStatus: updateRequest.status,
        riskLevel: updateRequest.riskLevel,
        hasEvidence: !!updateRequest.evidence?.length
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'ComplianceStatusUpdated',
      aggregateId: updateRequest.assessmentId,
      aggregateType: 'ComplianceAssessment',
      version: 1,
      data: {
        assessmentId: updateRequest.assessmentId,
        requirementId: updateRequest.requirementId,
        status: updateRequest.status,
        riskLevel: updateRequest.riskLevel,
        updatedBy: user.id
      },
      userId: user.id,
      organizationId: assessmentData.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Compliance status updated successfully", {
      correlationId,
      assessmentId: updateRequest.assessmentId,
      requirementId: updateRequest.requirementId,
      status: updateRequest.status,
      updatedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        assessmentId: updateRequest.assessmentId,
        requirementId: updateRequest.requirementId,
        status: updateRequest.status,
        statistics: assessmentData.statistics,
        overallStatus: assessmentData.status,
        updatedAt: now,
        message: "Compliance status updated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Update compliance status failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkComplianceAccess(user: any, organizationId: string): Promise<boolean> {
  try {
    // Check if user has admin or compliance role
    if (user.roles?.includes('admin') || user.roles?.includes('compliance_officer')) {
      return true;
    }

    // Check organization-level permissions
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);

    if (memberships.length > 0) {
      const membership = memberships[0] as any;
      return membership.role === 'OWNER' || membership.role === 'ADMIN';
    }

    return false;
  } catch (error) {
    logger.error('Failed to check compliance access', { error, userId: user.id, organizationId });
    return false;
  }
}

function calculateNextReview(endDate: string, frequency: string): string {
  const end = new Date(endDate);
  const nextReview = new Date(end);

  switch (frequency) {
    case 'monthly':
      nextReview.setMonth(nextReview.getMonth() + 1);
      break;
    case 'quarterly':
      nextReview.setMonth(nextReview.getMonth() + 3);
      break;
    case 'annually':
    default:
      nextReview.setFullYear(nextReview.getFullYear() + 1);
      break;
  }

  return nextReview.toISOString();
}

function calculateComplianceStatistics(requirements: any[]): any {
  const total = requirements.length;
  const compliant = requirements.filter(req => req.status === ComplianceStatus.COMPLIANT).length;
  const nonCompliant = requirements.filter(req => req.status === ComplianceStatus.NON_COMPLIANT).length;
  const pending = requirements.filter(req => req.status === ComplianceStatus.PENDING_REVIEW || req.status === ComplianceStatus.IN_PROGRESS).length;

  return {
    totalRequirements: total,
    compliantRequirements: compliant,
    nonCompliantRequirements: nonCompliant,
    pendingRequirements: pending,
    compliancePercentage: total > 0 ? Math.round((compliant / total) * 100) : 0
  };
}

function determineOverallStatus(statistics: any): ComplianceStatus {
  if (statistics.pendingRequirements > 0) {
    return ComplianceStatus.IN_PROGRESS;
  }

  if (statistics.nonCompliantRequirements > 0) {
    return ComplianceStatus.NON_COMPLIANT;
  }

  if (statistics.compliantRequirements === statistics.totalRequirements) {
    return ComplianceStatus.COMPLIANT;
  }

  return ComplianceStatus.PENDING_REVIEW;
}

async function notifyAssignees(assessment: ComplianceAssessment, eventType: string): Promise<void> {
  try {
    for (const assignee of assessment.assignees) {
      await db.createItem('notifications', {
        id: uuidv4(),
        userId: assignee.userId,
        type: 'compliance_assignment',
        title: `Compliance Assessment: ${assessment.name}`,
        message: `You have been assigned to the ${assessment.framework} compliance assessment.`,
        data: {
          assessmentId: assessment.id,
          assessmentName: assessment.name,
          framework: assessment.framework,
          role: assignee.role,
          eventType
        },
        read: false,
        createdAt: new Date().toISOString(),
        tenantId: assessment.tenantId
      });
    }
  } catch (error) {
    logger.error('Failed to notify assignees', { error, assessmentId: assessment.id });
  }
}

// Register functions
app.http('compliance-assessment-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'compliance/assessments',
  handler: createComplianceAssessment
});

app.http('compliance-status-update', {
  methods: ['PUT', 'OPTIONS'],
  authLevel: 'function',
  route: 'compliance/status',
  handler: updateComplianceStatus
});
