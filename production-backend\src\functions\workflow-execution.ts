/**
 * Workflow Execution Functions
 * Handles workflow execution operations (start, complete steps, assign)
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Workflow enums
export enum WorkflowStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  PAUSED = 'PAUSED'
}

export enum StepStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  SKIPPED = 'SKIPPED',
  FAILED = 'FAILED'
}

// Validation schemas
const completeStepSchema = Joi.object({
  comment: Joi.string().max(1000).optional(),
  outcome: Joi.string().valid('approved', 'rejected', 'completed').optional(),
  metadata: Joi.object().optional()
});

const assignStepSchema = Joi.object({
  assigneeId: Joi.string().uuid().required(),
  comment: Joi.string().max(500).optional()
});

/**
 * Start workflow handler
 */
export async function startWorkflow(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const workflowId = request.params.id;

  if (!workflowId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Workflow ID is required' }
    }, request);
  }

  logger.info("Start workflow started", { correlationId, workflowId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Get workflow
    const workflow = await db.readItem('workflows', workflowId, workflowId);
    if (!workflow) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Workflow not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (workflow as any).createdBy === user.id ||
      (workflow as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Check if workflow is in DRAFT status
    if ((workflow as any).status !== WorkflowStatus.DRAFT) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only workflows in DRAFT status can be started" }
      }, request);
    }

    // Check if workflow has steps
    const steps = (workflow as any).steps || [];
    if (steps.length === 0) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Workflow has no steps" }
      }, request);
    }

    // Sort steps by order and get first step
    const sortedSteps = [...steps].sort((a, b) => a.order - b.order);
    const firstStep = sortedSteps[0];

    // Update workflow status and current step
    const updatedWorkflow = {
      ...workflow,
      id: (workflow as any).id,
      status: WorkflowStatus.ACTIVE,
      currentStepId: firstStep.id,
      startedAt: new Date().toISOString(),
      startedBy: user.id,
      steps: steps.map((step: any) =>
        step.id === firstStep.id
          ? { ...step, status: StepStatus.IN_PROGRESS, startedAt: new Date().toISOString() }
          : step
      )
    };

    await db.updateItem('workflows', updatedWorkflow);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "workflow_started",
      userId: user.id,
      organizationId: (workflow as any).organizationId,
      projectId: (workflow as any).projectId,
      workflowId: (workflow as any).id,
      timestamp: new Date().toISOString(),
      details: {
        workflowName: (workflow as any).name,
        firstStepId: firstStep.id,
        firstStepName: firstStep.name
      }
    });

    logger.info("Workflow started successfully", {
      correlationId,
      workflowId,
      userId: user.id,
      firstStepId: firstStep.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: (workflow as any).id,
        message: "Workflow started successfully",
        currentStep: {
          id: firstStep.id,
          name: firstStep.name,
          status: StepStatus.IN_PROGRESS
        }
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Start workflow failed", {
      correlationId,
      workflowId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Complete workflow step handler
 */
export async function completeWorkflowStep(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const workflowId = request.params.id;
  const stepId = request.params.stepId;

  if (!workflowId || !stepId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Workflow ID and Step ID are required' }
    }, request);
  }

  logger.info("Complete workflow step started", { correlationId, workflowId, stepId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = completeStepSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { comment, outcome, metadata } = value;

    // Get workflow
    const workflow = await db.readItem('workflows', workflowId, workflowId);
    if (!workflow) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Workflow not found" }
      }, request);
    }

    // Find the step
    const steps = (workflow as any).steps || [];
    const stepIndex = steps.findIndex((step: any) => step.id === stepId);

    if (stepIndex === -1) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Step not found" }
      }, request);
    }

    const step = steps[stepIndex];

    // Check if user can complete this step
    const canComplete = (
      step.assigneeId === user.id ||
      (workflow as any).createdBy === user.id ||
      user.roles?.includes('admin')
    );

    if (!canComplete) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "You don't have permission to complete this step" }
      }, request);
    }

    // Check if step is in progress
    if (step.status !== StepStatus.IN_PROGRESS) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only steps in progress can be completed" }
      }, request);
    }

    // Update step status
    const updatedSteps = [...steps];
    updatedSteps[stepIndex] = {
      ...step,
      status: StepStatus.COMPLETED,
      completedAt: new Date().toISOString(),
      completedBy: user.id,
      comment,
      outcome,
      metadata
    };

    // Determine next step
    const sortedSteps = [...updatedSteps].sort((a, b) => a.order - b.order);
    const currentStepOrder = step.order;
    const nextStep = sortedSteps.find(s => s.order > currentStepOrder && s.status === StepStatus.PENDING);

    let workflowStatus = (workflow as any).status;
    let currentStepId = (workflow as any).currentStepId;

    if (nextStep) {
      // Start next step
      const nextStepIndex = updatedSteps.findIndex(s => s.id === nextStep.id);
      updatedSteps[nextStepIndex] = {
        ...nextStep,
        status: StepStatus.IN_PROGRESS,
        startedAt: new Date().toISOString()
      };
      currentStepId = nextStep.id;
    } else {
      // No more steps, complete workflow
      workflowStatus = WorkflowStatus.COMPLETED;
      currentStepId = null;
    }

    // Update workflow
    const updatedWorkflow = {
      ...workflow,
      id: (workflow as any).id,
      status: workflowStatus,
      currentStepId,
      steps: updatedSteps,
      ...(workflowStatus === WorkflowStatus.COMPLETED && {
        completedAt: new Date().toISOString(),
        completedBy: user.id
      })
    };

    await db.updateItem('workflows', updatedWorkflow);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: workflowStatus === WorkflowStatus.COMPLETED ? "workflow_completed" : "workflow_step_completed",
      userId: user.id,
      organizationId: (workflow as any).organizationId,
      projectId: (workflow as any).projectId,
      workflowId: (workflow as any).id,
      stepId,
      timestamp: new Date().toISOString(),
      details: {
        stepName: step.name,
        outcome,
        comment,
        nextStepId: nextStep?.id,
        nextStepName: nextStep?.name
      }
    });

    logger.info("Workflow step completed successfully", {
      correlationId,
      workflowId,
      stepId,
      userId: user.id,
      outcome,
      nextStepId: nextStep?.id,
      workflowCompleted: workflowStatus === WorkflowStatus.COMPLETED
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        message: "Step completed successfully",
        stepId,
        workflowStatus,
        nextStep: nextStep ? {
          id: nextStep.id,
          name: nextStep.name,
          status: StepStatus.IN_PROGRESS
        } : null,
        workflowCompleted: workflowStatus === WorkflowStatus.COMPLETED
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Complete workflow step failed", {
      correlationId,
      workflowId,
      stepId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

// Register functions
app.http('workflow-start', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/{id}/start',
  handler: startWorkflow
});

app.http('workflow-execution-complete-step', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/{id}/steps/{stepId}/complete',
  handler: completeWorkflowStep
});
