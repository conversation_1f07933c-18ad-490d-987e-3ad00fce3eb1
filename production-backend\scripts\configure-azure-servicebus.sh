#!/bin/bash

# Azure Service Bus Configuration Script for Enhanced Workflow Functions
# This script configures all necessary queues, topics, and subscriptions for:
# - Document signing workflows
# - Approval chain processes  
# - Real-time collaboration
# - Workflow automation

set -e

# Configuration variables
RESOURCE_GROUP="docucontext"
NAMESPACE="hepzbackend"
LOCATION="eastus"

echo "🚀 Configuring Azure Service Bus for Enhanced Workflow Functions..."
echo "Resource Group: $RESOURCE_GROUP"
echo "Namespace: $NAMESPACE"
echo "Location: $LOCATION"
echo ""

# Function to create queue if it doesn't exist
create_queue_if_not_exists() {
    local queue_name=$1
    local max_delivery_count=${2:-10}
    local lock_duration=${3:-"PT1M"}
    local max_size=${4:-1024}
    local ttl=${5:-"P10675199DT2H48M5.4775807S"}
    local dead_lettering=${6:-false}
    
    echo "Checking queue: $queue_name"
    if az servicebus queue show --resource-group $RESOURCE_GROUP --namespace-name $NAMESPACE --name $queue_name &>/dev/null; then
        echo "✅ Queue '$queue_name' already exists"
    else
        echo "📝 Creating queue: $queue_name"
        az servicebus queue create \
            --resource-group $RESOURCE_GROUP \
            --namespace-name $NAMESPACE \
            --name $queue_name \
            --max-delivery-count $max_delivery_count \
            --lock-duration $lock_duration \
            --max-size $max_size \
            --default-message-time-to-live $ttl \
            --dead-lettering-on-message-expiration $dead_lettering \
            --enable-batched-operations true
        echo "✅ Queue '$queue_name' created successfully"
    fi
}

# Function to create topic if it doesn't exist
create_topic_if_not_exists() {
    local topic_name=$1
    local max_size=${2:-1024}
    local ttl=${3:-"P10675199DT2H48M5.4775807S"}
    local duplicate_detection=${4:-false}
    
    echo "Checking topic: $topic_name"
    if az servicebus topic show --resource-group $RESOURCE_GROUP --namespace-name $NAMESPACE --name $topic_name &>/dev/null; then
        echo "✅ Topic '$topic_name' already exists"
    else
        echo "📝 Creating topic: $topic_name"
        az servicebus topic create \
            --resource-group $RESOURCE_GROUP \
            --namespace-name $NAMESPACE \
            --name $topic_name \
            --max-size $max_size \
            --default-message-time-to-live $ttl \
            --requires-duplicate-detection $duplicate_detection \
            --enable-batched-operations true \
            --support-ordering true
        echo "✅ Topic '$topic_name' created successfully"
    fi
}

# Function to create subscription if it doesn't exist
create_subscription_if_not_exists() {
    local topic_name=$1
    local subscription_name=$2
    local max_delivery_count=${3:-10}
    local lock_duration=${4:-"PT1M"}
    local ttl=${5:-"P10675199DT2H48M5.4775807S"}
    local dead_lettering=${6:-true}
    
    echo "Checking subscription: $subscription_name on topic: $topic_name"
    if az servicebus topic subscription show --resource-group $RESOURCE_GROUP --namespace-name $NAMESPACE --topic-name $topic_name --name $subscription_name &>/dev/null; then
        echo "✅ Subscription '$subscription_name' already exists on topic '$topic_name'"
    else
        echo "📝 Creating subscription: $subscription_name on topic: $topic_name"
        az servicebus topic subscription create \
            --resource-group $RESOURCE_GROUP \
            --namespace-name $NAMESPACE \
            --topic-name $topic_name \
            --name $subscription_name \
            --max-delivery-count $max_delivery_count \
            --lock-duration $lock_duration \
            --default-message-time-to-live $ttl \
            --dead-lettering-on-filter-evaluation-exceptions $dead_lettering \
            --enable-batched-operations true
        echo "✅ Subscription '$subscription_name' created successfully"
    fi
}

echo "🔧 Configuring Service Bus Queues..."
echo "=================================="

# Core workflow queues (already exist, but ensuring they're properly configured)
create_queue_if_not_exists "workflow-orchestration" 10 "PT1M" 1024 "P10675199DT2H48M5.4775807S" false
create_queue_if_not_exists "ai-operations" 10 "PT1M" 1024 "P10675199DT2H48M5.4775807S" false
create_queue_if_not_exists "scheduled-emails" 10 "PT1M" 1024 "P10675199DT2H48M5.4775807S" false
create_queue_if_not_exists "document-processing" 10 "PT1M" 1024 "P10675199DT2H48M5.4775807S" false
create_queue_if_not_exists "notification-delivery" 10 "PT1M" 1024 "P10675199DT2H48M5.4775807S" false

# Enhanced workflow queues for new functionality
create_queue_if_not_exists "approval-workflows" 5 "PT2M" 2048 "P7D" true
create_queue_if_not_exists "document-signing" 5 "PT1M" 1024 "P7D" true
create_queue_if_not_exists "collaboration-events" 10 "PT30S" 1024 "P1D" false
create_queue_if_not_exists "workflow-automation" 10 "PT1M" 2048 "P14D" true

echo ""
echo "🔧 Configuring Service Bus Topics..."
echo "===================================="

# Core topics (already exist)
create_topic_if_not_exists "document-collaboration" 1024 "P10675199DT2H48M5.4775807S" false
create_topic_if_not_exists "analytics-events" 1024 "P10675199DT2H48M5.4775807S" false
create_topic_if_not_exists "monitoring-events" 1024 "P10675199DT2H48M5.4775807S" false

# Enhanced topics for new functionality
create_topic_if_not_exists "approval-events" 2048 "P7D" true
create_topic_if_not_exists "signing-events" 1024 "P7D" true
create_topic_if_not_exists "workflow-events" 2048 "P14D" true
create_topic_if_not_exists "real-time-events" 1024 "P1D" false

echo ""
echo "🔧 Configuring Topic Subscriptions..."
echo "====================================="

# Document collaboration subscriptions (already exists)
create_subscription_if_not_exists "document-collaboration" "collaboration-processor" 10 "PT1M" "P10675199DT2H48M5.4775807S" true

# Analytics subscriptions (already exists)  
create_subscription_if_not_exists "analytics-events" "analytics-aggregator" 10 "PT1M" "P10675199DT2H48M5.4775807S" true

# Monitoring subscriptions (check if exists)
create_subscription_if_not_exists "monitoring-events" "system-monitor" 10 "PT1M" "P10675199DT2H48M5.4775807S" true

# Enhanced subscriptions for new functionality
create_subscription_if_not_exists "approval-events" "approval-processor" 5 "PT2M" "P7D" true
create_subscription_if_not_exists "approval-events" "approval-notifications" 10 "PT1M" "P7D" true
create_subscription_if_not_exists "approval-events" "approval-analytics" 10 "PT1M" "P7D" true

create_subscription_if_not_exists "signing-events" "signing-processor" 5 "PT1M" "P7D" true
create_subscription_if_not_exists "signing-events" "signing-notifications" 10 "PT30S" "P7D" true
create_subscription_if_not_exists "signing-events" "signing-audit" 10 "PT1M" "P7D" true

create_subscription_if_not_exists "workflow-events" "workflow-processor" 10 "PT1M" "P14D" true
create_subscription_if_not_exists "workflow-events" "workflow-analytics" 10 "PT1M" "P14D" true
create_subscription_if_not_exists "workflow-events" "workflow-monitoring" 10 "PT1M" "P14D" true

create_subscription_if_not_exists "real-time-events" "signalr-processor" 10 "PT30S" "P1D" false
create_subscription_if_not_exists "real-time-events" "collaboration-sync" 10 "PT30S" "P1D" false

echo ""
echo "🔧 Configuring Dead Letter Queue Processing..."
echo "=============================================="

# Enhanced dead letter queue for better error handling
create_queue_if_not_exists "enhanced-dead-letter" 1 "PT5M" 2048 "P30D" false

echo ""
echo "✅ Azure Service Bus Configuration Complete!"
echo "============================================"
echo ""
echo "📊 Summary of Configured Resources:"
echo "Queues:"
echo "  ✅ workflow-orchestration (existing)"
echo "  ✅ ai-operations (existing)" 
echo "  ✅ scheduled-emails (existing)"
echo "  ✅ document-processing (existing)"
echo "  ✅ notification-delivery (existing)"
echo "  🆕 approval-workflows (new)"
echo "  🆕 document-signing (new)"
echo "  🆕 collaboration-events (new)"
echo "  🆕 workflow-automation (new)"
echo "  🆕 enhanced-dead-letter (new)"
echo ""
echo "Topics & Subscriptions:"
echo "  ✅ document-collaboration → collaboration-processor (existing)"
echo "  ✅ analytics-events → analytics-aggregator (existing)"
echo "  ✅ monitoring-events → system-monitor (existing/new)"
echo "  🆕 approval-events → approval-processor, approval-notifications, approval-analytics"
echo "  🆕 signing-events → signing-processor, signing-notifications, signing-audit"
echo "  🆕 workflow-events → workflow-processor, workflow-analytics, workflow-monitoring"
echo "  🆕 real-time-events → signalr-processor, collaboration-sync"
echo ""
echo "🎯 All resources are now configured for enhanced workflow functions!"
echo "🔗 Functions can now use these queues and topics for:"
echo "   - Document signing workflows"
echo "   - Approval chain processes"
echo "   - Real-time collaboration"
echo "   - Workflow automation"
echo "   - Enhanced error handling and monitoring"
