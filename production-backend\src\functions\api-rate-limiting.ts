/**
 * API Rate Limiting Function
 * Handles API rate limiting, throttling, and quota management
 * Migrated from old-arch/src/api-service/rate-limiting/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Rate limiting types and enums
enum LimitType {
  REQUESTS_PER_MINUTE = 'REQUESTS_PER_MINUTE',
  REQUESTS_PER_HOUR = 'REQUESTS_PER_HOUR',
  REQUESTS_PER_DAY = 'REQUESTS_PER_DAY',
  BANDWIDTH_PER_MINUTE = 'BANDWIDTH_PER_MINUTE',
  BANDWIDTH_PER_HOUR = 'BANDWIDTH_PER_HOUR'
}

enum LimitScope {
  GLOBAL = 'GLOBAL',
  USER = 'USER',
  ORGANIZATION = 'ORGANIZATION',
  API_KEY = 'API_KEY',
  IP_ADDRESS = 'IP_ADDRESS',
  ENDPOINT = 'ENDPOINT'
}

enum ActionType {
  ALLOW = 'ALLOW',
  THROTTLE = 'THROTTLE',
  BLOCK = 'BLOCK',
  QUEUE = 'QUEUE'
}

// Validation schemas
const checkRateLimitSchema = Joi.object({
  identifier: Joi.string().required(),
  scope: Joi.string().valid(...Object.values(LimitScope)).required(),
  endpoint: Joi.string().optional(),
  method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').optional(),
  requestSize: Joi.number().min(0).optional(),
  organizationId: Joi.string().uuid().optional(),
  userId: Joi.string().uuid().optional(),
  metadata: Joi.object().optional()
});

const createRateLimitSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  scope: Joi.string().valid(...Object.values(LimitScope)).required(),
  organizationId: Joi.string().uuid().optional(),
  rules: Joi.array().items(Joi.object({
    limitType: Joi.string().valid(...Object.values(LimitType)).required(),
    limit: Joi.number().min(1).required(),
    window: Joi.number().min(1).required(), // seconds
    action: Joi.string().valid(...Object.values(ActionType)).required(),
    endpoints: Joi.array().items(Joi.string()).optional(),
    methods: Joi.array().items(Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH')).optional(),
    priority: Joi.number().min(1).max(100).default(50)
  })).min(1).required(),
  conditions: Joi.object({
    ipWhitelist: Joi.array().items(Joi.string().ip()).optional(),
    ipBlacklist: Joi.array().items(Joi.string().ip()).optional(),
    userAgentPatterns: Joi.array().items(Joi.string()).optional(),
    timeRestrictions: Joi.object({
      allowedHours: Joi.array().items(Joi.number().min(0).max(23)).optional(),
      allowedDays: Joi.array().items(Joi.number().min(0).max(6)).optional(),
      timezone: Joi.string().default('UTC')
    }).optional()
  }).optional(),
  enabled: Joi.boolean().default(true)
});

interface CheckRateLimitRequest {
  identifier: string;
  scope: LimitScope;
  endpoint?: string;
  method?: string;
  requestSize?: number;
  organizationId?: string;
  userId?: string;
  metadata?: any;
}

interface RateLimit {
  id: string;
  name: string;
  description?: string;
  scope: LimitScope;
  organizationId?: string;
  rules: Array<{
    id: string;
    limitType: LimitType;
    limit: number;
    window: number;
    action: ActionType;
    endpoints?: string[];
    methods?: string[];
    priority: number;
  }>;
  conditions: {
    ipWhitelist?: string[];
    ipBlacklist?: string[];
    userAgentPatterns?: string[];
    timeRestrictions?: {
      allowedHours?: number[];
      allowedDays?: number[];
      timezone: string;
    };
  };
  statistics: {
    totalRequests: number;
    allowedRequests: number;
    throttledRequests: number;
    blockedRequests: number;
    lastTriggered?: string;
  };
  enabled: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

interface RateLimitResult {
  allowed: boolean;
  action: ActionType;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  rule?: {
    id: string;
    limitType: LimitType;
    window: number;
  };
}

/**
 * Check rate limit handler
 */
export async function checkRateLimit(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Check rate limit started", { correlationId });

  try {
    // Parse request (can be GET with query params or POST with body)
    let rateLimitRequest: CheckRateLimitRequest;

    if (request.method === 'GET') {
      const url = new URL(request.url);
      rateLimitRequest = {
        identifier: url.searchParams.get('identifier') || '',
        scope: url.searchParams.get('scope') as LimitScope || LimitScope.USER,
        endpoint: url.searchParams.get('endpoint') || undefined,
        method: url.searchParams.get('method') || undefined,
        requestSize: url.searchParams.get('requestSize') ? parseInt(url.searchParams.get('requestSize')!) : undefined,
        organizationId: url.searchParams.get('organizationId') || undefined,
        userId: url.searchParams.get('userId') || undefined
      };
    } else {
      const body = await request.json();
      rateLimitRequest = body as CheckRateLimitRequest;
    }

    // Validate request
    const { error, value } = checkRateLimitSchema.validate(rateLimitRequest);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const checkRequest: CheckRateLimitRequest = value;

    // Get applicable rate limits
    const rateLimits = await getApplicableRateLimits(checkRequest);

    // Check each rate limit
    let finalResult: RateLimitResult = {
      allowed: true,
      action: ActionType.ALLOW,
      limit: 0,
      remaining: 0,
      resetTime: 0
    };

    for (const rateLimit of rateLimits) {
      if (!rateLimit.enabled) continue;

      // Check conditions
      const conditionsMatch = await checkConditions(rateLimit.conditions, request);
      if (!conditionsMatch) continue;

      // Check each rule
      for (const rule of rateLimit.rules.sort((a, b) => b.priority - a.priority)) {
        const ruleResult = await checkRule(rule, checkRequest, rateLimit.id);

        if (!ruleResult.allowed) {
          finalResult = ruleResult;

          // Log rate limit violation
          await logRateLimitViolation(rateLimit, rule, checkRequest, request);

          // Update statistics
          await updateRateLimitStatistics(rateLimit.id, rule.action);

          break;
        }
      }

      if (!finalResult.allowed) break;
    }

    // Record the request
    await recordRequest(checkRequest, finalResult);

    const duration = Date.now() - startTime;

    logger.info("Rate limit check completed", {
      correlationId,
      identifier: checkRequest.identifier,
      scope: checkRequest.scope,
      allowed: finalResult.allowed,
      action: finalResult.action,
      duration
    });

    // Set rate limit headers
    const headers: { [key: string]: string } = {
      'Content-Type': 'application/json',
      'X-RateLimit-Limit': finalResult.limit.toString(),
      'X-RateLimit-Remaining': finalResult.remaining.toString(),
      'X-RateLimit-Reset': finalResult.resetTime.toString()
    };

    if (finalResult.retryAfter) {
      headers['Retry-After'] = finalResult.retryAfter.toString();
    }

    const statusCode = finalResult.allowed ? 200 : (finalResult.action === ActionType.BLOCK ? 429 : 200);

    return addCorsHeaders({
      status: statusCode,
      headers,
      jsonBody: {
        allowed: finalResult.allowed,
        action: finalResult.action,
        limit: finalResult.limit,
        remaining: finalResult.remaining,
        resetTime: finalResult.resetTime,
        retryAfter: finalResult.retryAfter,
        rule: finalResult.rule,
        checkedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Check rate limit failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Create rate limit handler
 */
export async function createRateLimit(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create rate limit started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkRateLimitAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to rate limit management" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = createRateLimitSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const rateLimitRequest = value;

    // Create rate limit
    const rateLimitId = uuidv4();
    const now = new Date().toISOString();

    const rateLimit: RateLimit = {
      id: rateLimitId,
      name: rateLimitRequest.name,
      description: rateLimitRequest.description,
      scope: rateLimitRequest.scope,
      organizationId: rateLimitRequest.organizationId,
      rules: rateLimitRequest.rules.map((rule: any) => ({
        ...rule,
        id: uuidv4()
      })),
      conditions: rateLimitRequest.conditions || {},
      statistics: {
        totalRequests: 0,
        allowedRequests: 0,
        throttledRequests: 0,
        blockedRequests: 0
      },
      enabled: rateLimitRequest.enabled,
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('rate-limits', rateLimit);

    // Cache rate limit for fast access
    await cacheRateLimit(rateLimit);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "rate_limit_created",
      userId: user.id,
      organizationId: rateLimitRequest.organizationId,
      timestamp: now,
      details: {
        rateLimitId,
        rateLimitName: rateLimitRequest.name,
        scope: rateLimitRequest.scope,
        ruleCount: rateLimitRequest.rules.length,
        enabled: rateLimitRequest.enabled
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'RateLimitCreated',
      aggregateId: rateLimitId,
      aggregateType: 'RateLimit',
      version: 1,
      data: {
        rateLimit,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: rateLimitRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Rate limit created successfully", {
      correlationId,
      rateLimitId,
      rateLimitName: rateLimitRequest.name,
      scope: rateLimitRequest.scope,
      ruleCount: rateLimitRequest.rules.length,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: rateLimitId,
        name: rateLimitRequest.name,
        scope: rateLimitRequest.scope,
        ruleCount: rateLimitRequest.rules.length,
        enabled: rateLimitRequest.enabled,
        createdAt: now,
        message: "Rate limit created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create rate limit failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkRateLimitAccess(user: any): Promise<boolean> {
  try {
    // Check if user has admin or rate limit management role
    return user.roles?.includes('admin') || user.roles?.includes('rate_limit_admin');
  } catch (error) {
    logger.error('Failed to check rate limit access', { error, userId: user.id });
    return false;
  }
}

async function getApplicableRateLimits(checkRequest: CheckRateLimitRequest): Promise<RateLimit[]> {
  try {
    // Try cache first
    const cacheKey = `rate_limits:${checkRequest.scope}:${checkRequest.organizationId || 'global'}`;
    const cached = await redis.get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    // Query database
    let query = 'SELECT * FROM c WHERE c.scope = @param0 AND c.enabled = true';
    const parameters: any[] = [checkRequest.scope];

    if (checkRequest.organizationId) {
      query += ' AND (c.organizationId = @param1 OR c.organizationId = null)';
      parameters.push(checkRequest.organizationId);
    }

    const rateLimits = await db.queryItems('rate-limits', query, parameters);

    // Cache for 5 minutes
    await redis.setex(cacheKey, 300, JSON.stringify(rateLimits));

    return rateLimits as RateLimit[];

  } catch (error) {
    logger.error('Failed to get applicable rate limits', { error, checkRequest });
    return [];
  }
}

async function checkConditions(conditions: any, request: HttpRequest): Promise<boolean> {
  try {
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || '';

    // Check IP whitelist
    if (conditions.ipWhitelist && conditions.ipWhitelist.length > 0) {
      if (!conditions.ipWhitelist.includes(clientIP)) {
        return false;
      }
    }

    // Check IP blacklist
    if (conditions.ipBlacklist && conditions.ipBlacklist.length > 0) {
      if (conditions.ipBlacklist.includes(clientIP)) {
        return false;
      }
    }

    // Check user agent patterns
    if (conditions.userAgentPatterns && conditions.userAgentPatterns.length > 0) {
      const matches = conditions.userAgentPatterns.some((pattern: string) => {
        try {
          const regex = new RegExp(pattern, 'i');
          return regex.test(userAgent);
        } catch {
          return userAgent.includes(pattern);
        }
      });
      if (!matches) {
        return false;
      }
    }

    // Check time restrictions
    if (conditions.timeRestrictions) {
      const now = new Date();
      const hour = now.getHours();
      const day = now.getDay();

      if (conditions.timeRestrictions.allowedHours &&
          !conditions.timeRestrictions.allowedHours.includes(hour)) {
        return false;
      }

      if (conditions.timeRestrictions.allowedDays &&
          !conditions.timeRestrictions.allowedDays.includes(day)) {
        return false;
      }
    }

    return true;

  } catch (error) {
    logger.error('Failed to check conditions', { error });
    return true; // Allow on error
  }
}

async function checkRule(rule: any, checkRequest: CheckRateLimitRequest, rateLimitId: string): Promise<RateLimitResult> {
  try {
    // Check if rule applies to this endpoint/method
    if (rule.endpoints && checkRequest.endpoint && !rule.endpoints.includes(checkRequest.endpoint)) {
      return { allowed: true, action: ActionType.ALLOW, limit: 0, remaining: 0, resetTime: 0 };
    }

    if (rule.methods && checkRequest.method && !rule.methods.includes(checkRequest.method)) {
      return { allowed: true, action: ActionType.ALLOW, limit: 0, remaining: 0, resetTime: 0 };
    }

    // Generate cache key for this rule and identifier
    const cacheKey = `rate_limit:${rateLimitId}:${rule.id}:${checkRequest.identifier}`;

    // Get current count
    const currentCount = await redis.get(cacheKey);
    const count = currentCount ? parseInt(currentCount) : 0;

    // Calculate reset time
    const now = Math.floor(Date.now() / 1000);
    const windowStart = Math.floor(now / rule.window) * rule.window;
    const resetTime = windowStart + rule.window;

    // Check if limit exceeded
    if (count >= rule.limit) {
      const retryAfter = resetTime - now;

      return {
        allowed: false,
        action: rule.action,
        limit: rule.limit,
        remaining: 0,
        resetTime,
        retryAfter,
        rule: {
          id: rule.id,
          limitType: rule.limitType,
          window: rule.window
        }
      };
    }

    // Increment counter
    const newCount = count + 1;
    await redis.setex(cacheKey, rule.window, newCount.toString());

    return {
      allowed: true,
      action: ActionType.ALLOW,
      limit: rule.limit,
      remaining: rule.limit - newCount,
      resetTime,
      rule: {
        id: rule.id,
        limitType: rule.limitType,
        window: rule.window
      }
    };

  } catch (error) {
    logger.error('Failed to check rule', { error, ruleId: rule.id });
    return { allowed: true, action: ActionType.ALLOW, limit: 0, remaining: 0, resetTime: 0 };
  }
}

async function cacheRateLimit(rateLimit: RateLimit): Promise<void> {
  try {
    const cacheKey = `rate_limits:${rateLimit.scope}:${rateLimit.organizationId || 'global'}`;

    // Get existing cached rate limits
    const existing = await redis.get(cacheKey);
    const rateLimits = existing ? JSON.parse(existing) : [];

    // Add or update this rate limit
    const index = rateLimits.findIndex((rl: any) => rl.id === rateLimit.id);
    if (index >= 0) {
      rateLimits[index] = rateLimit;
    } else {
      rateLimits.push(rateLimit);
    }

    await redis.setex(cacheKey, 300, JSON.stringify(rateLimits));

  } catch (error) {
    logger.error('Failed to cache rate limit', { error, rateLimitId: rateLimit.id });
  }
}

async function logRateLimitViolation(rateLimit: RateLimit, rule: any, checkRequest: CheckRateLimitRequest, request: HttpRequest): Promise<void> {
  try {
    await db.createItem('rate-limit-violations', {
      id: uuidv4(),
      rateLimitId: rateLimit.id,
      ruleId: rule.id,
      identifier: checkRequest.identifier,
      scope: checkRequest.scope,
      action: rule.action,
      endpoint: checkRequest.endpoint,
      method: checkRequest.method,
      clientIP: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      timestamp: new Date().toISOString(),
      tenantId: rateLimit.tenantId
    });
  } catch (error) {
    logger.error('Failed to log rate limit violation', { error });
  }
}

async function updateRateLimitStatistics(rateLimitId: string, action: ActionType): Promise<void> {
  try {
    const statsKey = `rate_limit_stats:${rateLimitId}`;

    await redis.hincrby(statsKey, 'totalRequests', 1);

    switch (action) {
      case ActionType.ALLOW:
        await redis.hincrby(statsKey, 'allowedRequests', 1);
        break;
      case ActionType.THROTTLE:
        await redis.hincrby(statsKey, 'throttledRequests', 1);
        break;
      case ActionType.BLOCK:
        await redis.hincrby(statsKey, 'blockedRequests', 1);
        break;
    }

    await redis.hset(statsKey, 'lastTriggered', new Date().toISOString());
    await redis.expire(statsKey, 86400); // 24 hours

  } catch (error) {
    logger.error('Failed to update rate limit statistics', { error, rateLimitId });
  }
}

async function recordRequest(checkRequest: CheckRateLimitRequest, result: RateLimitResult): Promise<void> {
  try {
    // Record request for analytics (simplified)
    const requestKey = `requests:${checkRequest.scope}:${new Date().toISOString().split('T')[0]}`;
    await redis.hincrby(requestKey, checkRequest.identifier, 1);
    await redis.expire(requestKey, 86400 * 7); // 7 days

  } catch (error) {
    logger.error('Failed to record request', { error });
  }
}

// Register functions
app.http('rate-limit-check', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'rate-limit/check',
  handler: checkRateLimit
});

app.http('rate-limit-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/rate-limits/create',
  handler: createRateLimit
});
