/**
 * <PERSON><PERSON><PERSON> to fix remaining database update calls in LemonSqueezy webhook handler
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '..', 'src', 'functions', 'lemonsqueezy-webhooks.ts');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Define replacements for remaining database update calls
const replacements = [
  // Subscription expired
  {
    from: `await db.updateItem('subscriptions', data.id, {
      status: SubscriptionStatus.EXPIRED,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    });`,
    to: `await safeUpdateItem('subscriptions', data.id, {
      status: SubscriptionStatus.EXPIRED,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    }, correlationId);`
  },
  // Subscription paused
  {
    from: `await db.updateItem('subscriptions', data.id, {
      status: SubscriptionStatus.PAUSED,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    });`,
    to: `await safeUpdateItem('subscriptions', data.id, {
      status: SubscriptionStatus.PAUSED,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    }, correlationId);`
  },
  // Subscription unpaused
  {
    from: `await db.updateItem('subscriptions', data.id, {
      status: SubscriptionStatus.ACTIVE,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    });`,
    to: `await safeUpdateItem('subscriptions', data.id, {
      status: SubscriptionStatus.ACTIVE,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    }, correlationId);`
  },
  // Payment failed subscription update
  {
    from: `await db.updateItem('subscriptions', attributes.subscription_id, {
        status: SubscriptionStatus.PAST_DUE,
        updatedAt: attributes.updated_at,
        processedAt: new Date().toISOString()
      });`,
    to: `await safeUpdateItem('subscriptions', attributes.subscription_id, {
        status: SubscriptionStatus.PAST_DUE,
        updatedAt: attributes.updated_at,
        processedAt: new Date().toISOString()
      }, correlationId);`
  },
  // Payment recovered subscription update
  {
    from: `await db.updateItem('subscriptions', attributes.subscription_id, {
        status: SubscriptionStatus.ACTIVE,
        updatedAt: attributes.updated_at,
        processedAt: new Date().toISOString()
      });`,
    to: `await safeUpdateItem('subscriptions', attributes.subscription_id, {
        status: SubscriptionStatus.ACTIVE,
        updatedAt: attributes.updated_at,
        processedAt: new Date().toISOString()
      }, correlationId);`
  },
  // Payment refunded invoice update
  {
    from: `await db.updateItem('subscription-invoices', data.id, {
      refunded: attributes.refunded,
      refundedAmount: attributes.refunded_amount,
      status: attributes.status,
      statusFormatted: attributes.status_formatted,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    });`,
    to: `await safeUpdateItem('subscription-invoices', data.id, {
      refunded: attributes.refunded,
      refundedAmount: attributes.refunded_amount,
      status: attributes.status,
      statusFormatted: attributes.status_formatted,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    }, correlationId);`
  },
  // License key update
  {
    from: `await db.updateItem('license-keys', data.id, {
      activationLimit: attributes.activation_limit,
      instancesCount: attributes.instances_count,
      disabled: attributes.disabled,
      status: attributes.status,
      statusFormatted: attributes.status_formatted,
      expiresAt: attributes.expires_at,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    });`,
    to: `await safeUpdateItem('license-keys', data.id, {
      activationLimit: attributes.activation_limit,
      instancesCount: attributes.instances_count,
      disabled: attributes.disabled,
      status: attributes.status,
      statusFormatted: attributes.status_formatted,
      expiresAt: attributes.expires_at,
      updatedAt: attributes.updated_at,
      processedAt: new Date().toISOString()
    }, correlationId);`
  }
];

// Apply replacements
let updatedContent = content;
let replacementCount = 0;

replacements.forEach((replacement, index) => {
  if (updatedContent.includes(replacement.from)) {
    updatedContent = updatedContent.replace(replacement.from, replacement.to);
    replacementCount++;
    console.log(`✅ Applied replacement ${index + 1}`);
  } else {
    console.log(`⚠️ Replacement ${index + 1} not found or already applied`);
  }
});

// Write the updated content back to the file
if (replacementCount > 0) {
  fs.writeFileSync(filePath, updatedContent, 'utf8');
  console.log(`🎉 Successfully applied ${replacementCount} replacements to ${filePath}`);
} else {
  console.log('ℹ️ No replacements needed');
}
