/**
 * Optimal Event Grid Setup for Complete Azure Infrastructure
 * Node.js script to configure Event Grid with all your existing resources
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  resourceGroup: 'docucontext',
  functionAppName: 'hepzlogic',
  eventGridTopicName: 'hepzeg',
  eventGridNamespace: 'hepzfullstack',
  storageAccountName: 'stdocucontex900520441468',
  cosmosAccountName: 'hepz',
  serviceBusNamespace: 'hepzbackend',
  signalRName: 'hepztech',
  notificationHubNamespace: 'hepzdocs',
  location: 'eastus'
};

console.log('🚀 Starting Optimal Event Grid Configuration...\n');

/**
 * Execute Azure CLI command with error handling
 */
function executeAzCommand(command, description) {
  try {
    console.log(`📋 ${description}...`);
    console.log(`   Command: ${command}`);
    
    const result = execSync(command, { 
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    console.log(`✅ ${description} - SUCCESS`);
    return { success: true, output: result };
  } catch (error) {
    console.log(`⚠️ ${description} - FAILED or ALREADY EXISTS`);
    console.log(`   Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Get subscription ID
 */
function getSubscriptionId() {
  try {
    const result = execSync('az account show --query id --output tsv', { encoding: 'utf8' });
    return result.trim();
  } catch (error) {
    console.error('❌ Failed to get subscription ID:', error.message);
    process.exit(1);
  }
}

/**
 * Get Function App URL
 */
function getFunctionAppUrl() {
  try {
    const command = `az functionapp show --name ${config.functionAppName} --resource-group ${config.resourceGroup} --query defaultHostName --output tsv`;
    const result = execSync(command, { encoding: 'utf8' });
    return result.trim();
  } catch (error) {
    console.error('❌ Failed to get Function App URL:', error.message);
    process.exit(1);
  }
}

/**
 * Main setup function
 */
async function setupEventGrid() {
  console.log('📊 Your Azure Resources for Event Grid:');
  console.log(`   ✅ Event Grid Topic: ${config.eventGridTopicName} (Basic)`);
  console.log(`   ✅ Event Grid Namespace: ${config.eventGridNamespace} (Standard)`);
  console.log(`   ✅ Function App: ${config.functionAppName}`);
  console.log(`   ✅ Storage Account: ${config.storageAccountName}`);
  console.log(`   ✅ Cosmos DB: ${config.cosmosAccountName}`);
  console.log(`   ✅ Service Bus: ${config.serviceBusNamespace}`);
  console.log(`   ✅ SignalR: ${config.signalRName}`);
  console.log(`   ✅ Notification Hubs: ${config.notificationHubNamespace}\n`);

  // Get subscription ID and Function App URL
  const subscriptionId = getSubscriptionId();
  const functionAppUrl = getFunctionAppUrl();
  
  console.log(`📋 Subscription ID: ${subscriptionId}`);
  console.log(`📋 Function App URL: https://${functionAppUrl}\n`);

  // Resource IDs
  const storageAccountId = `/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.Storage/storageAccounts/${config.storageAccountName}`;
  const cosmosAccountId = `/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.DocumentDb/databaseAccounts/${config.cosmosAccountName}`;
  const eventGridTopicId = `/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.EventGrid/topics/${config.eventGridTopicName}`;

  // System topic names
  const storageSystemTopic = `${config.storageAccountName}-events`;
  const cosmosSystemTopic = `${config.cosmosAccountName}-events`;

  console.log('🔧 Starting Event Grid Configuration...\n');

  // 1. Create Storage System Topic
  executeAzCommand(
    `az eventgrid system-topic create --name "${storageSystemTopic}" --resource-group "${config.resourceGroup}" --source "${storageAccountId}" --topic-type "Microsoft.Storage.StorageAccounts" --location "${config.location}"`,
    'Creating Storage System Topic'
  );

  // 2. Create Cosmos DB System Topic
  executeAzCommand(
    `az eventgrid system-topic create --name "${cosmosSystemTopic}" --resource-group "${config.resourceGroup}" --source "${cosmosAccountId}" --topic-type "Microsoft.DocumentDb.DatabaseAccounts" --location "${config.location}"`,
    'Creating Cosmos DB System Topic'
  );

  // 3. Storage Events to Function App
  executeAzCommand(
    `az eventgrid event-subscription create --name "storage-to-function" --source-resource-id "/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.EventGrid/systemTopics/${storageSystemTopic}" --endpoint "https://${functionAppUrl}/api/eventgrid/webhook" --endpoint-type webhook --included-event-types "Microsoft.Storage.BlobCreated" "Microsoft.Storage.BlobDeleted" --subject-begins-with "/blobServices/default/containers/documents/" --max-delivery-attempts 3 --event-ttl 1440`,
    'Creating Storage to Function App subscription'
  );

  // 4. Cosmos Events to Function App
  executeAzCommand(
    `az eventgrid event-subscription create --name "cosmos-to-function" --source-resource-id "/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.EventGrid/systemTopics/${cosmosSystemTopic}" --endpoint "https://${functionAppUrl}/api/eventgrid/webhook" --endpoint-type webhook --max-delivery-attempts 3 --event-ttl 1440`,
    'Creating Cosmos DB to Function App subscription'
  );

  // 5. Custom High-Priority Events to Function App
  executeAzCommand(
    `az eventgrid event-subscription create --name "custom-high-priority" --source-resource-id "${eventGridTopicId}" --endpoint "https://${functionAppUrl}/api/eventgrid/webhook" --endpoint-type webhook --included-event-types "Document.Uploaded" "Document.Processed" "Workflow.Completed" "User.Registered" --max-delivery-attempts 3 --event-ttl 1440`,
    'Creating Custom High-Priority Events subscription'
  );

  // 6. Create Service Bus Topics (if they don't exist)
  executeAzCommand(
    `az servicebus topic create --resource-group "${config.resourceGroup}" --namespace-name "${config.serviceBusNamespace}" --name "blob-events"`,
    'Creating Service Bus Topic: blob-events'
  );

  executeAzCommand(
    `az servicebus topic create --resource-group "${config.resourceGroup}" --namespace-name "${config.serviceBusNamespace}" --name "analytics-events"`,
    'Creating Service Bus Topic: analytics-events'
  );

  executeAzCommand(
    `az servicebus queue create --resource-group "${config.resourceGroup}" --namespace-name "${config.serviceBusNamespace}" --name "notification-queue"`,
    'Creating Service Bus Queue: notification-queue'
  );

  // 7. Storage Events to Service Bus (for batch processing)
  executeAzCommand(
    `az eventgrid event-subscription create --name "storage-to-servicebus" --source-resource-id "/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.EventGrid/systemTopics/${storageSystemTopic}" --endpoint "/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.ServiceBus/namespaces/${config.serviceBusNamespace}/topics/blob-events" --endpoint-type servicebustopic --included-event-types "Microsoft.Storage.BlobCreated" --subject-begins-with "/blobServices/default/containers/processed/" --max-delivery-attempts 3`,
    'Creating Storage to Service Bus subscription'
  );

  // 8. Analytics Events to Service Bus
  executeAzCommand(
    `az eventgrid event-subscription create --name "custom-analytics" --source-resource-id "${eventGridTopicId}" --endpoint "/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.ServiceBus/namespaces/${config.serviceBusNamespace}/topics/analytics-events" --endpoint-type servicebustopic --included-event-types "Analytics.Generated" "Performance.Alert" "System.HealthCheck" --max-delivery-attempts 5`,
    'Creating Analytics Events to Service Bus subscription'
  );

  // 9. Notification Events to Service Bus Queue
  executeAzCommand(
    `az eventgrid event-subscription create --name "custom-notifications" --source-resource-id "${eventGridTopicId}" --endpoint "/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.ServiceBus/namespaces/${config.serviceBusNamespace}/queues/notification-queue" --endpoint-type servicebusqueue --included-event-types "Notification.Sent" "Document.Shared" --max-delivery-attempts 5`,
    'Creating Notification Events to Service Bus Queue subscription'
  );

  // 10. Create Dead Letter Storage Container
  executeAzCommand(
    `az storage container create --name "event-dead-letters" --account-name "${config.storageAccountName}" --auth-mode login`,
    'Creating Dead Letter Storage Container'
  );

  // 11. Create Event Grid Namespace Topic
  executeAzCommand(
    `az eventgrid namespace topic create --namespace-name "${config.eventGridNamespace}" --resource-group "${config.resourceGroup}" --name "advanced-events" --publisher-type custom --input-schema cloudevents-1.0`,
    'Creating Event Grid Namespace Topic for advanced scenarios'
  );

  console.log('\n🎉 Optimal Event Grid Configuration Complete!\n');
  
  console.log('📊 Event Flow Summary:');
  console.log('   📁 Storage Events → Function App (real-time) + Service Bus (batch)');
  console.log('   🗄️  Cosmos DB Events → Function App (real-time processing)');
  console.log('   📋 High-Priority Custom Events → Function App (immediate processing)');
  console.log('   📈 Analytics Events → Service Bus (batch processing)');
  console.log('   🔔 Notification Events → Service Bus (reliable delivery)');
  console.log('   🌐 Advanced Events → Event Grid Namespace (MQTT/CloudEvents)\n');
  
  console.log('🎯 Benefits of This Configuration:');
  console.log('   ✅ Cost-effective (uses existing Function App)');
  console.log('   ✅ Scalable (Service Bus for high-volume events)');
  console.log('   ✅ Reliable (multiple retry attempts + dead letter)');
  console.log('   ✅ Real-time (Function App for immediate processing)');
  console.log('   ✅ Future-ready (Event Grid Namespace for advanced scenarios)\n');
  
  console.log('🔄 Next Steps:');
  console.log(`   1. Deploy your Function App: func azure functionapp publish ${config.functionAppName}`);
  console.log('   2. Test event flow: node scripts/test-event-grid.js');
  console.log('   3. Monitor in Azure Portal: Event Grid + Service Bus + Function App metrics');
  console.log('   4. Set up SignalR integration for real-time notifications');
}

/**
 * Verify prerequisites
 */
function verifyPrerequisites() {
  try {
    // Check if Azure CLI is installed and logged in
    execSync('az account show', { stdio: 'pipe' });
    console.log('✅ Azure CLI is installed and logged in');
    return true;
  } catch (error) {
    console.error('❌ Azure CLI is not installed or not logged in');
    console.error('Please install Azure CLI and run "az login" first');
    return false;
  }
}

// Main execution
if (require.main === module) {
  console.log('🔍 Verifying prerequisites...');
  
  if (!verifyPrerequisites()) {
    process.exit(1);
  }
  
  setupEventGrid().catch(error => {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  });
}

module.exports = { setupEventGrid, config };
