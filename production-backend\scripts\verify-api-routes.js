/**
 * Verify API Routes and HATEOAS Links
 * Checks for consistency between route registrations and HATEOAS links
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying API Routes and HATEOAS Links\n');

/**
 * Extract route registrations from TypeScript files
 */
function extractRoutes(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const routes = [];
    
    // Match app.http() registrations
    const httpMatches = content.matchAll(/app\.http\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*\{[^}]*route:\s*['"`]([^'"`]+)['"`]/g);
    
    for (const match of httpMatches) {
      routes.push({
        functionId: match[1],
        route: match[2],
        file: path.basename(filePath)
      });
    }
    
    return routes;
  } catch (error) {
    return [];
  }
}

/**
 * Extract HATEOAS links from TypeScript files
 */
function extractHateoasLinks(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const links = [];
    
    // Match _links objects
    const linkMatches = content.matchAll(/href:\s*['"`]([^'"`]+)['"`]/g);
    
    for (const match of linkMatches) {
      links.push({
        href: match[1],
        file: path.basename(filePath)
      });
    }
    
    return links;
  } catch (error) {
    return [];
  }
}

/**
 * Scan all TypeScript function files
 */
function scanFunctionFiles() {
  const functionsDir = path.join(__dirname, '..', 'src', 'functions');
  const files = fs.readdirSync(functionsDir).filter(file => file.endsWith('.ts'));
  
  const allRoutes = [];
  const allLinks = [];
  
  for (const file of files) {
    const filePath = path.join(functionsDir, file);
    const routes = extractRoutes(filePath);
    const links = extractHateoasLinks(filePath);
    
    allRoutes.push(...routes);
    allLinks.push(...links);
  }
  
  return { routes: allRoutes, links: allLinks };
}

/**
 * Check for inconsistencies
 */
function checkConsistency(routes, links) {
  console.log('📋 Route Registrations:');
  console.log('=======================');
  
  const routeMap = new Map();
  
  for (const route of routes) {
    console.log(`   ${route.functionId}: /${route.route} (${route.file})`);
    routeMap.set(route.route, route);
  }
  
  console.log('\n🔗 HATEOAS Links Found:');
  console.log('========================');
  
  const issues = [];
  
  for (const link of links) {
    console.log(`   ${link.href} (${link.file})`);
    
    // Check if link starts with /api/
    if (link.href.startsWith('/api/')) {
      const routeWithoutApi = link.href.substring(5); // Remove '/api/'
      const routeWithoutApiBase = routeWithoutApi.split('/')[0].split('?')[0]; // Get base route
      
      // Check if corresponding route exists without /api/
      let foundMatch = false;
      for (const [registeredRoute] of routeMap) {
        if (registeredRoute === routeWithoutApiBase || registeredRoute.startsWith(routeWithoutApiBase)) {
          foundMatch = true;
          break;
        }
      }
      
      if (foundMatch) {
        issues.push({
          type: 'api_prefix_mismatch',
          link: link.href,
          file: link.file,
          suggestion: link.href.substring(4) // Remove '/api'
        });
      }
    }
  }
  
  return issues;
}

/**
 * Generate route documentation
 */
function generateRouteDocumentation(routes) {
  console.log('\n📚 Actual API Endpoints (for documentation):');
  console.log('=============================================');
  
  const groupedRoutes = {};
  
  for (const route of routes) {
    const category = route.route.split('/')[0] || 'root';
    if (!groupedRoutes[category]) {
      groupedRoutes[category] = [];
    }
    groupedRoutes[category].push(route);
  }
  
  for (const [category, categoryRoutes] of Object.entries(groupedRoutes)) {
    console.log(`\n### ${category.charAt(0).toUpperCase() + category.slice(1)}`);
    for (const route of categoryRoutes) {
      console.log(`- \`/${route.route}\` - ${route.functionId}`);
    }
  }
}

/**
 * Main verification function
 */
function main() {
  const { routes, links } = scanFunctionFiles();
  
  console.log(`Found ${routes.length} route registrations and ${links.length} HATEOAS links\n`);
  
  const issues = checkConsistency(routes, links);
  
  if (issues.length > 0) {
    console.log('\n❌ Issues Found:');
    console.log('================');
    
    for (const issue of issues) {
      if (issue.type === 'api_prefix_mismatch') {
        console.log(`🔧 ${issue.file}: "${issue.link}" should be "${issue.suggestion}"`);
      }
    }
    
    console.log('\n💡 Recommendations:');
    console.log('===================');
    console.log('1. Remove /api/ prefix from HATEOAS links to match actual routes');
    console.log('2. Update documentation to reflect actual endpoints');
    console.log('3. Rebuild TypeScript to update dist/ folder');
    
  } else {
    console.log('\n✅ No issues found! Routes and HATEOAS links are consistent.');
  }
  
  generateRouteDocumentation(routes);
  
  console.log('\n🔄 Next Steps:');
  console.log('==============');
  console.log('1. Fix any HATEOAS link issues found above');
  console.log('2. Run: npm run build');
  console.log('3. Update README.md with correct endpoint documentation');
  console.log('4. Test endpoints to ensure they work correctly');
}

// Run verification
main();
