# Service Bus Management

1. az servicebus namespace show --name hepzbackend --resource-group docucontext --query "{name:name,status:status,location:location,sku:sku}" --output table

Output:
Name         Status    Location
-----------  --------  ----------
hepzbackend  Active    eastus


2. az servicebus queue list --namespace-name hepzbackend --resource-group docucontext --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name                    Status    MessageCount
----------------------  --------  --------------
ai-operations           Active    0
audit-log               Active    0
connection              Active    0
dead-letter-queue       Active    0
document-processing     Active    0
health-check            Active    29
monitoring-event        Active    0
notification-delivery   Active    0
notification-queue      Active    0
notifications           Active    0
scheduled-emails        Active    0
workflow-orchestration  Active    0


3. az servicebus topic list --namespace-name hepzbackend --resource-group docucontext --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name                    Status
----------------------  --------
analytics-events        Active
blob-events             Active
document-collaboration  Active
monitoring-events       Active

4. az servicebus topic subscription list --namespace-name hepzbackend --resource-group docucontext --topic-name analytics-events --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name                  Status    MessageCount
--------------------  --------  --------------
analytics-aggregator  Active    0

5. az servicebus topic subscription list --namespace-name hepzbackend --resource-group docucontext --topic-name document-collaboration --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name                     Status    MessageCount
-----------------------  --------  --------------
collaboration-processor  Active    0

6. az servicebus topic subscription list --namespace-name hepzbackend --resource-group docucontext --topic-name monitoring-events --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name            Status    MessageCount
--------------  --------  --------------
system-monitor  Active    0


# Redis Management

1. az resource list --resource-group docucontext --query "[?contains(type, 'redis') || contains(type, 'Redis')].{name:name, type:type, location:location}" --output table

Name         Location
-----------  ----------
hepzbackend  eastus

2. az resource list --resource-group docucontext --query "[?contains(type, 'redis') || contains(type, 'Redis')].{name:name, type:type, location:location, id:id}" --output json

[
  {
    "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.Cache/redisEnterprise/hepzbackend",
    "location": "eastus",
    "name": "hepzbackend",
    "type": "Microsoft.Cache/redisEnterprise"
  },
  {
    "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Cache/Redis/hepzbackend",
    "location": "eastus",
    "name": "hepzbackend",
    "type": "Microsoft.Cache/Redis"
  }
]

# Redis Enterprise Status Check

3. az redisenterprise show --cluster-name hepzbackend --resource-group docucontext --output table

HighAvailability    HostName                            Location    MinimumTlsVersion    Name         ProvisioningState    RedundancyMode    ResourceGroup    ResourceState
------------------  ----------------------------------  ----------  -------------------  -----------  -------------------  ----------------  ---------------  ---------------
Enabled             hepzbackend.eastus.redis.azure.net  East US     1.2                  hepzbackend  Succeeded            ZR                DocuContext      Running

4. az redisenterprise database list --cluster-name hepzbackend --resource-group docucontext --output table

ClientProtocol    ClusteringPolicy    EvictionPolicy    Name     Port    ProvisioningState    ResourceGroup    ResourceState
----------------  ------------------  ----------------  -------  ------  -------------------  ---------------  ---------------
Encrypted         OSSCluster          NoEviction        default  10000   Succeeded            DocuContext      Running

3. az redisenterprise database show --help

This command is from the following extension: redisenterprise

Command
    az redisenterprise database show : Get information about a database in a RedisEnterprise
    cluster.

Arguments

Resource Id Arguments
    --cluster-name      : The name of the RedisEnterprise cluster.
    --ids               : One or more resource IDs (space-delimited). It should be a complete
                          resource ID containing all information of 'Resource Id' arguments. You
                          should provide either --ids or other 'Resource Id' arguments.
    --resource-group -g : Name of resource group. You can configure the default group using `az
                          configure --defaults group=<name>`.
    --subscription      : Name or ID of subscription. You can configure the default subscription
                          using `az account set -s NAME_OR_ID`.

Global Arguments
    --debug             : Increase logging verbosity to show all debug logs.
    --help -h           : Show this help message and exit.
    --only-show-errors  : Only show errors, suppressing warnings.
    --output -o         : Output format.  Allowed values: json, jsonc, none, table, tsv, yaml,
                          yamlc.  Default: json.
    --query             : JMESPath query string. See http://jmespath.org/ for more information and
                          examples.
    --verbose           : Increase logging verbosity. Use --debug for full debug logs.

To search AI knowledge base for examples, use: az find "az redisenterprise database show"


4. az redisenterprise database list-keys --cluster-name hepzbackend --resource-group docucontext

{
  "primaryKey": "uYB4CoTMNKayqhchSzci7CEG6oqpeRhZVAzCaEnHa70=",
  "secondaryKey": "y6kziAVIInixcGYV724l1yMSeeUFcxtGEAzCaMP7H3A="
}

az redisenterprise database list --cluster-name hepzbackend --resource-group docucontext --output json

[
  {
    "clientProtocol": "Encrypted",
    "clusteringPolicy": "OSSCluster",
    "evictionPolicy": "NoEviction",
    "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.Cache/redisEnterprise/hepzbackend/databases/default",
    "name": "default",
    "persistence": {
      "aofEnabled": false,
      "aofFrequency": "1s",
      "rdbEnabled": true,
      "rdbFrequency": "1h"
    },
    "port": 10000,
    "provisioningState": "Succeeded",
    "resourceGroup": "DocuContext",
    "resourceState": "Running",
    "type": "Microsoft.Cache/redisEnterprise/databases"
  }
]

# Event Grid Management

1. az eventgrid topic list --resource-group docucontext --output table

Name    Location    ProvisioningState    ResourceGroup
------  ----------  -------------------  ---------------
hepzeg  East US     Succeeded            DocuContext

2. az eventgrid system-topic list --resource-group docucontext --output table

Location    MetricResourceId                      Name                             ProvisioningState    ResourceGroup    Source                                                                                                                                               TopicType
----------  ------------------------------------  -------------------------------  -------------------  ---------------  ---------------------------------------------------------------------------------------------------------------------------------------------------  ---------------------------------
eastus      7155481c-c90d-4b1c-a696-a2f8d0bcd98e  stdocucontex900520441468-events  Succeeded            docucontext      /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Storage/storageAccounts/stdocucontex900520441468  Microsoft.Storage.StorageAccounts

3. Event Grid Topic Details:
{
  "dataResidencyBoundary": "WithinGeopair",
  "disableLocalAuth": false,
  "endpoint": "https://hepzeg.eastus-1.eventgrid.azure.net/api/events",
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.EventGrid/topics/hepzeg",
  "inputSchema": "EventGridSchema",
  "location": "eastus",
  "name": "hepzeg",
  "provisioningState": "Succeeded",
  "publicNetworkAccess": "Enabled",
  "resourceGroup": "DocuContext",
  "type": "Microsoft.EventGrid/topics"
}

# Document Lifecycle Azure Infrastructure Status

## ✅ CONFIGURED SERVICES

### 1. Azure Redis Enterprise Cluster
- **Name**: hepzbackend
- **Host**: hepzbackend.eastus.redis.azure.net
- **Port**: 10000
- **Status**: Running
- **High Availability**: Enabled
- **Clustering Policy**: OSSCluster
- **Eviction Policy**: NoEviction
- **TLS**: Required (1.2+)
- **Persistence**: RDB enabled (1h frequency)

### 2. Service Bus Namespace: hepzbackend
**Queues (All Active):**
- ai-operations (0 messages)
- document-processing (0 messages)
- scheduled-emails (0 messages)
- notification-delivery (0 messages)
- dead-letter-queue (0 messages)
- health-check (29 messages)

**Topics (All Active):**
- analytics-events (1 subscription)
- blob-events (0 subscriptions)
- document-collaboration (1 subscription)
- monitoring-events (1 subscription)

### 3. Event Grid Configuration
- **Custom Topic**: hepzeg (Active)
- **Storage System Topic**: stdocucontex900520441468-events (Active)
- **Endpoint**: https://hepzeg.eastus-1.eventgrid.azure.net/api/events

## ✅ CONFIGURED EVENT GRID SUBSCRIPTIONS

### Event Grid Subscriptions Status (All Active)

1. **document-lifecycle-events** ✅
   - Destination: Service Bus Topic (analytics-events)
   - Event Types: Document.UploadInitiated, Document.UploadCompleted, Document.Uploaded, Document.Processed, Document.Shared, Document.VersionCreated, Document.VersionRestored, Document.Archived, Document.Deleted, Document.CollaborationSessionCreated, Search.Performed

2. **document-processing-events** ✅
   - Destination: Service Bus Topic (analytics-events)
   - Event Types: Document.ProcessingStarted, Document.ProcessingCompleted, Document.ProcessingFailed, Document.AIAnalysisStarted, Document.AIAnalysisCompleted, Document.AIAnalysisFailed, Document.TextExtracted, Document.ThumbnailGenerated

3. **document-collaboration-events** ✅
   - Destination: Service Bus Topic (document-collaboration)
   - Event Types: Document.ShareUpdated, Document.ShareDeleted, Document.CollaborationStarted, Document.CollaborationEnded

4. **document-analytics-events** ✅
   - Destination: Service Bus Topic (analytics-events)
   - Event Types: Document.Viewed, Document.Downloaded, Document.SearchPerformed

5. **custom-analytics** ✅
   - Destination: Service Bus Topic (analytics-events)
   - Event Types: Analytics.Generated, Performance.Alert, System.HealthCheck

6. **storage-blob-events** ✅
   - Source: Storage Account (stdocucontex900520441468)
   - Destination: Service Bus Topic (blob-events)
   - Event Types: Microsoft.Storage.BlobCreated, Microsoft.Storage.BlobDeleted
   - Subject Filter: /blobServices/default/containers/documents/

7. **custom-events-to-function** ✅
   - Destination: Azure Function (webhook)
   - Event Types: All events (no filter)

8. **storage-to-function** ✅
   - Destination: Azure Function (webhook)
   - Event Types: Storage events