/**
 * Event-Driven Cache Service
 * Integrates with Azure Event Grid for real-time cache invalidation
 * Provides automatic cache warming based on usage patterns
 */

import { cacheAside, CacheEvent } from './cache-aside';
import { logger } from '../utils/logger';
// Note: Event Grid publishing will be implemented when event-grid-handlers is available
// import { publishEvent, EventType } from '../../functions/event-grid-handlers';

export interface CacheInvalidationEvent {
  eventType: 'document.updated' | 'user.updated' | 'session.expired' | 'device.updated';
  resourceId: string;
  organizationId?: string;
  userId?: string;
  timestamp: Date;
  metadata?: any;
}

export interface CacheWarmingConfig {
  enabled: boolean;
  patterns: {
    documents: boolean;
    sessions: boolean;
    userActivities: boolean;
    deviceRegistrations: boolean;
    featureFlags: boolean;
    systemConfig: boolean;
  };
  priorities: {
    high: string[];
    medium: string[];
    low: string[];
  };
  frequencies: {
    high: number; // minutes
    medium: number;
    low: number;
  };
}

export class EventDrivenCacheService {
  private static instance: EventDrivenCacheService;
  private warmingConfig: CacheWarmingConfig;
  private isInitialized = false;

  private constructor() {
    this.warmingConfig = {
      enabled: true,
      patterns: {
        documents: true,
        sessions: true,
        userActivities: true,
        deviceRegistrations: true,
        featureFlags: true,
        systemConfig: true
      },
      priorities: {
        high: ['session:*', 'document:*:content', 'feature_flag:*'],
        medium: ['user:*:recent_activities', 'device:*', 'config:*'],
        low: ['stats:*', 'cache:*', 'bi_report:*']
      },
      frequencies: {
        high: 5,    // 5 minutes
        medium: 15, // 15 minutes
        low: 60     // 1 hour
      }
    };
  }

  public static getInstance(): EventDrivenCacheService {
    if (!EventDrivenCacheService.instance) {
      EventDrivenCacheService.instance = new EventDrivenCacheService();
    }
    return EventDrivenCacheService.instance;
  }

  /**
   * Initialize event-driven cache system
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Setup cache warming rules
      await this.setupWarmingRules();
      
      // Setup event listeners
      this.setupEventListeners();
      
      this.isInitialized = true;
      logger.info('Event-driven cache service initialized');
    } catch (error) {
      logger.error('Failed to initialize event-driven cache service', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Setup cache warming rules based on configuration
   */
  private async setupWarmingRules(): Promise<void> {
    if (!this.warmingConfig.enabled) {
      return;
    }

    // High priority warming rules
    for (const pattern of this.warmingConfig.priorities.high) {
      await this.addWarmingRule(pattern, 'high', this.warmingConfig.frequencies.high);
    }

    // Medium priority warming rules
    for (const pattern of this.warmingConfig.priorities.medium) {
      await this.addWarmingRule(pattern, 'medium', this.warmingConfig.frequencies.medium);
    }

    // Low priority warming rules
    for (const pattern of this.warmingConfig.priorities.low) {
      await this.addWarmingRule(pattern, 'low', this.warmingConfig.frequencies.low);
    }

    logger.info('Cache warming rules configured', {
      highPriority: this.warmingConfig.priorities.high.length,
      mediumPriority: this.warmingConfig.priorities.medium.length,
      lowPriority: this.warmingConfig.priorities.low.length
    });
  }

  /**
   * Add warming rule for specific pattern
   */
  private async addWarmingRule(
    pattern: string, 
    priority: 'high' | 'medium' | 'low', 
    frequency: number
  ): Promise<void> {
    const ruleId = `warming-${pattern.replace(/[^a-zA-Z0-9]/g, '-')}-${priority}`;
    
    // Determine database query based on pattern
    const dbQuery = this.getDbQueryForPattern(pattern);
    
    if (dbQuery) {
      cacheAside.addWarmingRule(ruleId, {
        pattern,
        dbQuery,
        options: {
          ttlSeconds: this.getTtlForPriority(priority),
          enableFallback: true,
          enableWarming: true,
          warmingPriority: priority,
          eventDriven: true
        },
        frequency,
        priority
      });
    }
  }

  /**
   * Get database query configuration for cache pattern
   */
  private getDbQueryForPattern(pattern: string): any {
    if (pattern.startsWith('session:')) {
      return {
        containerName: 'collaboration-sessions',
        query: 'SELECT * FROM c WHERE c.status = @status ORDER BY c.lastActivity DESC',
        parameters: ['ACTIVE']
      };
    }
    
    if (pattern.startsWith('document:')) {
      return {
        containerName: 'documents',
        query: 'SELECT * FROM c ORDER BY c.lastModified DESC OFFSET 0 LIMIT 100',
        parameters: []
      };
    }
    
    if (pattern.startsWith('feature_flag:')) {
      return {
        containerName: 'feature-flags',
        query: 'SELECT * FROM c WHERE c.status = @status',
        parameters: ['ACTIVE']
      };
    }
    
    if (pattern.startsWith('user:')) {
      return {
        containerName: 'activities',
        query: 'SELECT * FROM c ORDER BY c.timestamp DESC OFFSET 0 LIMIT 50',
        parameters: []
      };
    }
    
    if (pattern.startsWith('device:')) {
      return {
        containerName: 'device-registrations',
        query: 'SELECT * FROM c WHERE c.isActive = true',
        parameters: []
      };
    }
    
    if (pattern.startsWith('config:')) {
      return {
        containerName: 'configurations',
        query: 'SELECT * FROM c ORDER BY c.lastModified DESC OFFSET 0 LIMIT 100',
        parameters: []
      };
    }
    
    return null;
  }

  /**
   * Get TTL based on priority
   */
  private getTtlForPriority(priority: 'high' | 'medium' | 'low'): number {
    switch (priority) {
      case 'high': return 1800;  // 30 minutes
      case 'medium': return 3600; // 1 hour
      case 'low': return 7200;   // 2 hours
      default: return 3600;
    }
  }

  /**
   * Setup event listeners for cache invalidation
   */
  private setupEventListeners(): void {
    // Listen to cache events from cache-aside service
    cacheAside.on('cacheEvent', (event: CacheEvent) => {
      this.handleCacheEvent(event);
    });
  }

  /**
   * Handle cache events
   */
  private async handleCacheEvent(event: CacheEvent): Promise<void> {
    try {
      // Publish to Event Grid for distributed cache invalidation
      if (event.type === 'invalidate' || event.type === 'update') {
        await this.publishCacheEvent(event);
      }
      
      // Handle warming events
      if (event.type === 'warm') {
        await this.handleWarmingEvent(event);
      }
      
      logger.debug('Cache event handled', { 
        type: event.type, 
        key: event.key, 
        source: event.source 
      });
    } catch (error) {
      logger.error('Failed to handle cache event', {
        error: error instanceof Error ? error.message : String(error),
        event
      });
    }
  }

  /**
   * Publish cache event to Event Grid
   */
  private async publishCacheEvent(event: CacheEvent): Promise<void> {
    try {
      // TODO: Implement Event Grid publishing when event-grid-handlers is available
      // For now, just log the event that would be published
      logger.info('Cache event ready for Event Grid publishing', {
        eventType: 'cache.invalidation',
        subject: `cache/${event.key}`,
        data: {
          cacheKey: event.key,
          pattern: event.pattern,
          eventType: event.type,
          timestamp: event.timestamp,
          source: event.source
        },
        eventTime: event.timestamp,
        id: `cache-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        dataVersion: '1.0'
      });

      logger.debug('Cache event prepared for Event Grid', { key: event.key, type: event.type });
    } catch (error) {
      logger.error('Failed to prepare cache event for Event Grid', {
        error: error instanceof Error ? error.message : String(error),
        event
      });
    }
  }

  /**
   * Handle cache warming event
   */
  private async handleWarmingEvent(event: CacheEvent): Promise<void> {
    // Implement intelligent warming based on usage patterns
    // This could include ML-based prediction of cache needs
    logger.debug('Cache warming event processed', { 
      key: event.key, 
      priority: event.priority 
    });
  }

  /**
   * Process cache invalidation event from external source
   */
  public async processInvalidationEvent(event: CacheInvalidationEvent): Promise<void> {
    try {
      const patterns = this.getInvalidationPatterns(event);
      
      for (const pattern of patterns) {
        cacheAside.emitCacheEvent({
          type: 'invalidate',
          key: pattern,
          pattern,
          timestamp: event.timestamp,
          source: 'external-event'
        });
      }
      
      logger.info('Cache invalidation event processed', {
        eventType: event.eventType,
        resourceId: event.resourceId,
        patterns: patterns.length
      });
    } catch (error) {
      logger.error('Failed to process cache invalidation event', {
        error: error instanceof Error ? error.message : String(error),
        event
      });
    }
  }

  /**
   * Get cache patterns to invalidate based on event type
   */
  private getInvalidationPatterns(event: CacheInvalidationEvent): string[] {
    const patterns: string[] = [];
    
    switch (event.eventType) {
      case 'document.updated':
        patterns.push(`document:${event.resourceId}:*`);
        patterns.push(`session:*:${event.resourceId}:*`);
        if (event.organizationId) {
          patterns.push(`bi_report:*:${event.organizationId}*`);
        }
        break;
        
      case 'user.updated':
        patterns.push(`user:${event.resourceId}:*`);
        patterns.push(`session:${event.resourceId}:*`);
        patterns.push(`device:${event.resourceId}:*`);
        patterns.push(`user_advanced_roles:${event.resourceId}:*`);
        break;
        
      case 'session.expired':
        patterns.push(`session:${event.resourceId}`);
        patterns.push(`session:${event.resourceId}:*`);
        break;
        
      case 'device.updated':
        patterns.push(`device:${event.userId}:*`);
        patterns.push(`user_devices:${event.userId}`);
        break;
    }
    
    return patterns;
  }

  /**
   * Get cache warming and event processing statistics
   */
  public getStatistics(): any {
    return {
      warming: cacheAside.getWarmingStats(),
      events: cacheAside.getEventStats(),
      config: {
        enabled: this.warmingConfig.enabled,
        patterns: this.warmingConfig.patterns,
        priorities: Object.keys(this.warmingConfig.priorities).reduce((acc, key) => {
          acc[key] = this.warmingConfig.priorities[key as keyof typeof this.warmingConfig.priorities].length;
          return acc;
        }, {} as any)
      }
    };
  }

  /**
   * Update warming configuration
   */
  public updateWarmingConfig(config: Partial<CacheWarmingConfig>): void {
    this.warmingConfig = { ...this.warmingConfig, ...config };
    logger.info('Cache warming configuration updated', { config });
  }
}

// Export singleton instance
export const eventDrivenCache = EventDrivenCacheService.getInstance();
export default eventDrivenCache;
