{"name": "hepz-backend-production", "version": "1.0.0", "description": "HEPZ Enterprise Document Processing Platform - Production Backend", "main": "dist/index.js", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "func start --typescript", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "watch": "tsc -w", "prestart": "npm run clean && npm run build", "test": "echo \"No tests yet...\"", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "keywords": ["azure", "functions", "document", "processing", "enterprise"], "author": "HEPZ Team", "license": "UNLICENSED", "dependencies": {"@azure/ai-form-recognizer": "^4.0.0", "@azure/ai-text-analytics": "^5.1.0", "@azure/core-rest-pipeline": "^1.20.0", "@azure/cosmos": "^3.17.3", "@azure/eventgrid": "^5.12.0", "@azure/functions": "^4.7.2", "@azure/identity": "^3.4.2", "@azure/keyvault-keys": "^4.9.0", "@azure/keyvault-secrets": "^4.7.0", "@azure/notification-hubs": "^2.0.0", "@azure/search-documents": "^12.1.0", "@azure/service-bus": "^7.9.3", "@azure/storage-blob": "^12.14.0", "@azure/storage-queue": "^12.26.0", "@azure/web-pubsub": "^1.2.0", "@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@sentry/node": "^9.22.0", "@sentry/profiling-node": "^9.22.0", "@types/bcrypt": "^5.0.2", "@types/diff": "^7.0.2", "axios": "^1.4.0", "axios-retry": "^4.5.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cookie": "^1.0.2", "cors": "^2.8.5", "diff": "^8.0.2", "docx": "^9.5.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "handlebars": "^4.7.8", "jimp": "^0.22.10", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.0.1", "lodash": "^4.17.21", "nodemailer": "^7.0.3", "p-limit": "^6.2.0", "p-retry": "^6.2.1", "p-timeout": "^6.1.4", "pdf-lib": "^1.17.1", "pdfkit": "^0.14.0", "postmark": "^4.0.5", "redis": "^5.1.0", "retry-axios": "^3.1.3", "sharp": "^0.33.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.14.196", "@types/node": "^20.4.5", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.13.3", "@types/sharp": "^0.32.0", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.57.1", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.6.2", "prettier": "^3.0.0", "rimraf": "^5.0.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.8.3"}}