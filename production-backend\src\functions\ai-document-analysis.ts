/**
 * AI Document Analysis Function
 * Handles advanced AI-powered document analysis including classification, entity extraction, and insights
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

import { aiServices } from '../shared/services/ai-services';
import { ragService } from '../shared/services/rag-service';
import { redis } from '../shared/services/redis';
import { eventGridIntegration } from '../shared/services/event-grid-integration';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';
import { eventDrivenCache } from '../shared/services/event-driven-cache';

// Analysis types enum
enum AnalysisType {
  CLASSIFICATION = 'CLASSIFICATION',
  ENTITY_EXTRACTION = 'ENTITY_EXTRACTION',
  SENTIMENT_ANALYSIS = 'SENTIMENT_ANALYSIS',
  KEY_PHRASE_EXTRACTION = 'KEY_PHRASE_EXTRACTION',
  LANGUAGE_DETECTION = 'LANGUAGE_DETECTION',
  SUMMARIZATION = 'SUMMARIZATION',
  KNOWLEDGE_EXTRACTION = 'KNOWLEDGE_EXTRACTION',
  COMPLIANCE_CHECK = 'COMPLIANCE_CHECK'
}

// Validation schema
const analyzeDocumentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  analysisTypes: Joi.array().items(Joi.string().valid(...Object.values(AnalysisType))).min(1).required(),
  options: Joi.object({
    language: Joi.string().length(2).optional(),
    confidenceThreshold: Joi.number().min(0).max(1).default(0.7),
    maxEntities: Joi.number().integer().min(1).max(100).default(50),
    maxKeyPhrases: Joi.number().integer().min(1).max(50).default(20),
    summaryLength: Joi.string().valid('short', 'medium', 'long').default('medium'),
    complianceStandards: Joi.array().items(Joi.string()).optional()
  }).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().required()
});

interface AnalysisResult {
  documentId: string;
  analysisId: string;
  analysisTypes: AnalysisType[];
  results: {
    classification?: any;
    entities?: any[];
    sentiment?: any;
    keyPhrases?: any[];
    language?: any;
    summary?: string;
    knowledge?: any;
    compliance?: any;
  };
  confidence: number;
  processingTime: number;
  success: boolean;
}

/**
 * Analyze document handler
 */
export async function analyzeDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("AI document analysis started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = analyzeDocumentSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { documentId, analysisTypes, options, organizationId, projectId } = value;
    const startTime = Date.now();
    const analysisId = uuidv4();

    // Get document
    const document = await db.readItem('documents', documentId, documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Get document content
    let documentText = (document as any).extractedText || '';
    
    // If no extracted text, try to get from blob storage
    if (!documentText) {
      const blobServiceClient = new BlobServiceClient(
        process.env.AZURE_STORAGE_CONNECTION_STRING || ""
      );
      const containerClient = blobServiceClient.getContainerClient(
        process.env.DOCUMENT_CONTAINER || "documents"
      );
      const blobClient = containerClient.getBlobClient((document as any).blobName);

      try {
        const downloadResponse = await blobClient.download();
        if (downloadResponse.readableStreamBody) {
          const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);
          const contentType = (document as any).contentType || 'application/octet-stream';

          // Production: Extract text using Azure Document Intelligence
          try {
            documentText = await extractTextWithDocumentIntelligence(documentBuffer, contentType);
            logger.info('Text extracted using Azure Document Intelligence', {
              documentId,
              textLength: documentText.length,
              contentType
            });
          } catch (extractError) {
            logger.warn('Document Intelligence extraction failed, using fallback', {
              documentId,
              error: extractError instanceof Error ? extractError.message : String(extractError)
            });
            documentText = `[Binary content - ${documentBuffer.length} bytes]`;
          }
        }
      } catch (error) {
        logger.warn("Could not download document for analysis", { documentId, error });
      }
    }

    if (!documentText) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "No text content available for analysis" }
      }, request);
    }

    // Initialize services
    const serviceBusService = ServiceBusEnhancedService.getInstance();
    await Promise.all([
      serviceBusService.initialize(),
      eventDrivenCache.initialize()
    ]);

    // Check Redis cache for recent analysis results
    const cacheKey = `ai-analysis:${documentId}:${JSON.stringify(analysisTypes)}`;
    const cachedResult = await redis.getJson(cacheKey) as any;

    if (cachedResult && cachedResult.timestamp && (Date.now() - cachedResult.timestamp) < 1800000) { // 30 minutes cache
      logger.info('Returning cached AI analysis result', {
        correlationId,
        documentId,
        cacheAge: Date.now() - cachedResult.timestamp
      });

      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { ...cachedResult.result, fromCache: true }
      }, request);
    }

    // Publish AI analysis started event
    await eventGridIntegration.publishEvent({
      eventType: 'Document.AIAnalysisStarted',
      subject: `documents/${documentId}/ai-analysis`,
      data: {
        documentId,
        userId: user.id,
        organizationId: user.tenantId,
        analysisTypes,
        startedAt: new Date().toISOString(),
        correlationId
      }
    });

    // Send analysis message to Service Bus
    await serviceBusService.sendToQueue('ai-operations', {
      body: {
        documentId,
        action: 'ai-analysis-started',
        analysisTypes,
        userId: user.id,
        organizationId: user.tenantId,
        correlationId,
        timestamp: new Date().toISOString()
      },
      messageId: `ai-analysis-${documentId}-${Date.now()}`,
      correlationId,
      subject: 'document.ai.analysis.started'
    });

    // Perform comprehensive document analysis using enhanced services
    const analysisResults = await performEnhancedAIAnalysis(
      documentText,
      analysisTypes,
      options || {},
      (document as any).contentType,
      documentId
    );

    // Calculate overall confidence
    const overallConfidence = calculateOverallConfidence(analysisResults);

    // Save analysis results
    const analysis = {
      id: analysisId,
      documentId,
      analysisTypes,
      results: analysisResults,
      confidence: overallConfidence,
      options,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      organizationId,
      projectId,
      processingTime: Date.now() - startTime,
      tenantId: user.tenantId
    };

    await db.createItem('document-analyses', analysis);

    // Update document with analysis results
    const updatedDocument = {
      ...(document as any),
      id: documentId,
      aiAnalysis: {
        lastAnalysisId: analysisId,
        lastAnalyzedAt: new Date().toISOString(),
        classification: analysisResults.classification,
        entities: analysisResults.entities?.slice(0, 10), // Store top 10 entities
        sentiment: analysisResults.sentiment,
        language: analysisResults.language,
        summary: analysisResults.summary,
        confidence: overallConfidence
      },
      updatedAt: new Date().toISOString(),
      updatedBy: user.id
    };

    await db.updateItem('documents', updatedDocument);

    // Cache the analysis result
    await redis.setJson(cacheKey, {
      result: {
        documentId,
        analysisId,
        analysisTypes,
        results: analysisResults,
        confidence: overallConfidence,
        processingTime: Date.now() - startTime,
        success: true
      },
      timestamp: Date.now(),
      documentId,
      analysisTypes,
      overallConfidence
    }, 1800); // 30 minutes cache

    // Invalidate related cache entries using Redis
    await redis.del(`doc:${documentId}:content`);
    await redis.del(`doc:${documentId}:metadata`);
    await redis.del(`user:${user.id}:recent-analyses`);

    // Publish AI analysis completed event
    await eventGridIntegration.publishEvent({
      eventType: 'Document.AIAnalysisCompleted',
      subject: `documents/${documentId}/ai-analysis/completed`,
      data: {
        documentId,
        analysisId,
        userId: user.id,
        organizationId,
        analysisTypes,
        results: {
          overallConfidence,
          processingTime: Date.now() - startTime,
          analysisCount: Object.keys(analysisResults).length,
          entitiesFound: analysisResults.entities?.length || 0
        },
        completedAt: new Date().toISOString(),
        correlationId
      }
    });

    // Send completion message to Service Bus
    await serviceBusService.sendToQueue('ai-operations', {
      body: {
        documentId,
        analysisId,
        action: 'ai-analysis-completed',
        analysisTypes,
        results: {
          overallConfidence,
          processingTime: Date.now() - startTime,
          analysisCount: Object.keys(analysisResults).length,
          entitiesFound: analysisResults.entities?.length || 0
        },
        userId: user.id,
        organizationId,
        correlationId,
        timestamp: new Date().toISOString()
      },
      messageId: `ai-analysis-complete-${documentId}-${Date.now()}`,
      correlationId,
      subject: 'document.ai.analysis.completed'
    });

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_ai_analyzed",
      userId: user.id,
      organizationId,
      projectId,
      documentId,
      analysisId,
      timestamp: new Date().toISOString(),
      details: {
        analysisTypes,
        confidence: overallConfidence,
        entitiesFound: analysisResults.entities?.length || 0,
        keyPhrasesFound: analysisResults.keyPhrases?.length || 0,
        processingTime: Date.now() - startTime
      },
      tenantId: user.tenantId
    });

    const response: AnalysisResult = {
      documentId,
      analysisId,
      analysisTypes,
      results: analysisResults,
      confidence: overallConfidence,
      processingTime: Date.now() - startTime,
      success: true
    };

    logger.info("AI document analysis completed successfully", {
      correlationId,
      documentId,
      analysisId,
      analysisTypes,
      confidence: overallConfidence,
      userId: user.id,
      processingTime: response.processingTime
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("AI document analysis failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Extract text using Azure Document Intelligence
 */
async function extractTextWithDocumentIntelligence(documentBuffer: Buffer, contentType: string): Promise<string> {
  const { DocumentAnalysisClient, AzureKeyCredential } = require('@azure/ai-form-recognizer');

  const endpoint = process.env.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT;
  const key = process.env.AZURE_DOCUMENT_INTELLIGENCE_KEY;

  if (!endpoint || !key) {
    throw new Error('Azure Document Intelligence not configured');
  }

  const client = new DocumentAnalysisClient(endpoint, new AzureKeyCredential(key));

  try {
    // Use prebuilt-read model for general text extraction
    const poller = await client.beginAnalyzeDocument('prebuilt-read', documentBuffer);
    const result = await poller.pollUntilDone();

    if (!result.content) {
      throw new Error('No content extracted from document');
    }

    logger.info('Document Intelligence extraction successful', {
      contentLength: result.content.length,
      pages: result.pages?.length || 0,
      contentType
    });

    return result.content;
  } catch (error) {
    logger.error('Document Intelligence extraction failed', {
      error: error instanceof Error ? error.message : String(error),
      contentType
    });
    throw error;
  }
}

/**
 * Perform AI analysis using Azure AI services (production implementation)
 */
async function performEnhancedAIAnalysis(
  text: string,
  analysisTypes: AnalysisType[],
  _options: any,
  contentType: string,
  documentId: string
): Promise<any> {
  const results: any = {};

  try {
    // Initialize AI services
    await aiServices.initialize();

    // Prepare context for AI analysis
    const analysisContext = [
      `Document Type: ${contentType}`,
      `Document Length: ${text.length} characters`,
      `Analysis Types Requested: ${analysisTypes.join(', ')}`
    ];

    for (const analysisType of analysisTypes) {
      try {
        switch (analysisType) {
          case AnalysisType.CLASSIFICATION:
            const classificationPrompt = `Analyze this document and classify it. Determine the document type, main categories, and confidence level.

Document Content:
${text.substring(0, 2000)}...

Provide a JSON response with:
- documentType: string
- confidence: number (0-1)
- categories: string[]
- reasoning: string`;

            const classificationResult = await aiServices.reason(classificationPrompt, analysisContext, {
              systemPrompt: 'You are a document classification expert. Analyze documents and provide structured classification results.',
              temperature: 0.3,
              maxTokens: 1000
            });

            try {
              const parsed = JSON.parse(classificationResult.content);
              results.classification = parsed;
            } catch {
              results.classification = {
                documentType: 'general',
                confidence: classificationResult.confidence,
                categories: ['document'],
                reasoning: classificationResult.reasoning
              };
            }
            break;

          case AnalysisType.ENTITY_EXTRACTION:
            const entityPrompt = `Extract all important entities from this document. Find people, organizations, locations, dates, amounts, and other key information.

Document Content:
${text.substring(0, 3000)}...

Provide a JSON array of entities with:
- text: string
- category: string
- subCategory: string (optional)
- confidence: number (0-1)
- context: string`;

            const entityResult = await aiServices.reason(entityPrompt, analysisContext, {
              systemPrompt: 'You are an expert at extracting entities from documents. Be thorough and accurate.',
              temperature: 0.2,
              maxTokens: 1500
            });

            try {
              results.entities = JSON.parse(entityResult.content);
            } catch {
              results.entities = [];
            }
            break;

          case AnalysisType.SENTIMENT_ANALYSIS:
            const sentimentPrompt = `Analyze the sentiment and tone of this document. Consider the overall emotional tone, formality, and intent.

Document Content:
${text.substring(0, 2000)}...

Provide a JSON response with:
- overall: string (positive/negative/neutral)
- confidence: number (0-1)
- positive: number (0-1)
- neutral: number (0-1)
- negative: number (0-1)
- tone: string (formal/informal/professional/casual)
- reasoning: string`;

            const sentimentResult = await aiServices.reason(sentimentPrompt, analysisContext, {
              systemPrompt: 'You are an expert at analyzing sentiment and tone in documents.',
              temperature: 0.2,
              maxTokens: 800
            });

            try {
              results.sentiment = JSON.parse(sentimentResult.content);
            } catch {
              results.sentiment = {
                overall: 'neutral',
                confidence: sentimentResult.confidence,
                positive: 0.33,
                neutral: 0.34,
                negative: 0.33,
                tone: 'neutral',
                reasoning: sentimentResult.reasoning
              };
            }
            break;

          case AnalysisType.SUMMARIZATION:
            const summaryPrompt = `Create a comprehensive summary of this document. Include key points, main themes, and important details.

Document Content:
${text}

Provide a JSON response with:
- text: string (summary)
- keyPoints: string[] (main points)
- themes: string[] (main themes)
- confidence: number (0-1)
- wordCount: number`;

            const summaryResult = await aiServices.reason(summaryPrompt, analysisContext, {
              systemPrompt: 'You are an expert at creating comprehensive document summaries.',
              temperature: 0.4,
              maxTokens: 1500
            });

            try {
              results.summary = JSON.parse(summaryResult.content);
            } catch {
              results.summary = {
                text: summaryResult.content,
                keyPoints: [],
                themes: [],
                confidence: summaryResult.confidence,
                wordCount: summaryResult.content.split(' ').length
              };
            }
            break;

          default:
            logger.warn(`Unsupported analysis type: ${analysisType}`);
        }
      } catch (error) {
        logger.error(`Error performing ${analysisType} analysis`, { error, documentId });
        results[analysisType.toLowerCase()] = null;
      }
    }

    // Index document for RAG if text is substantial
    if (text.length > 500) {
      try {
        await ragService.indexDocument({
          documentId,
          content: text,
          metadata: {
            contentType,
            analysisTypes,
            analysisResults: results
          }
        });
        logger.info('Document indexed for RAG', { documentId });
      } catch (error) {
        logger.warn('Failed to index document for RAG', { documentId, error });
      }
    }

    return results;

  } catch (error) {
    logger.error('Enhanced AI analysis failed', { error, documentId });
    throw error;
  }
}

/**
 * Calculate overall confidence score
 */
function calculateOverallConfidence(results: any): number {
  const confidenceValues: number[] = [];

  if (results.classification?.confidence) confidenceValues.push(results.classification.confidence);
  if (results.sentiment?.confidence) confidenceValues.push(results.sentiment.confidence);
  if (results.language?.confidence) confidenceValues.push(results.language.confidence);
  if (results.entities) {
    const avgEntityConfidence = results.entities.reduce((sum: number, entity: any) => sum + entity.confidence, 0) / results.entities.length;
    confidenceValues.push(avgEntityConfidence);
  }
  if (results.keyPhrases) {
    const avgPhraseConfidence = results.keyPhrases.reduce((sum: number, phrase: any) => sum + phrase.confidence, 0) / results.keyPhrases.length;
    confidenceValues.push(avgPhraseConfidence);
  }

  return confidenceValues.length > 0 
    ? confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length 
    : 0.5;
}

/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readableStream.on("data", (data) => {
      chunks.push(data instanceof Buffer ? data : Buffer.from(data));
    });
    readableStream.on("end", () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on("error", reject);
  });
}

// Register functions
app.http('ai-document-analysis', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{id}/ai-analysis',
  handler: analyzeDocument
});
