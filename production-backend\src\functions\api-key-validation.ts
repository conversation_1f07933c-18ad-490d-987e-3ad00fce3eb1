/**
 * API Key Validation Function
 * Handles API key validation, rate limiting, and usage tracking
 * Migrated from old-arch/src/integration-service/api-key-validation/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { db } from '../shared/services/database';
import * as crypto from 'crypto';

// API key types and enums
enum ApiKeyStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  EXPIRED = 'expired',
  REVOKED = 'revoked'
}

enum ApiKeyScope {
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin',
  FULL = 'full'
}

// Validation schemas
const createApiKeySchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(500).optional(),
  organizationId: Joi.string().uuid().required(),
  scopes: Joi.array().items(Joi.string().valid(...Object.values(ApiKeyScope))).min(1).required(),
  expiresAt: Joi.string().isoDate().optional(),
  rateLimit: Joi.object({
    requestsPerMinute: Joi.number().min(1).max(10000).default(100),
    requestsPerHour: Joi.number().min(1).max(100000).default(1000),
    requestsPerDay: Joi.number().min(1).max(1000000).default(10000)
  }).optional(),
  allowedIPs: Joi.array().items(Joi.string().ip()).optional(),
  allowedDomains: Joi.array().items(Joi.string().domain()).optional()
});

const validateApiKeySchema = Joi.object({
  apiKey: Joi.string().required(),
  requestPath: Joi.string().optional(),
  requestMethod: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').optional(),
  clientIP: Joi.string().ip().optional(),
  userAgent: Joi.string().optional()
});

interface CreateApiKeyRequest {
  name: string;
  description?: string;
  organizationId: string;
  scopes: ApiKeyScope[];
  expiresAt?: string;
  rateLimit?: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
  allowedIPs?: string[];
  allowedDomains?: string[];
}

interface ValidateApiKeyRequest {
  apiKey: string;
  requestPath?: string;
  requestMethod?: string;
  clientIP?: string;
  userAgent?: string;
}

interface ApiKeyValidationResponse {
  valid: boolean;
  apiKeyId?: string;
  organizationId?: string;
  scopes?: ApiKeyScope[];
  rateLimit?: {
    remaining: number;
    resetTime: string;
    limit: number;
  };
  error?: string;
  errorCode?: string;
}

/**
 * Create API key handler
 */
export async function createApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create API key started", { correlationId });

  try {
    // For API key creation, we need proper authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Authorization header required' }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = createApiKeySchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const apiKeyRequest: CreateApiKeyRequest = value;

    // Generate API key
    const apiKey = generateApiKey();
    const apiKeyHash = hashApiKey(apiKey);
    const apiKeyId = uuidv4();
    const now = new Date().toISOString();

    // Create API key record
    const apiKeyRecord = {
      id: apiKeyId,
      name: apiKeyRequest.name,
      description: apiKeyRequest.description,
      keyHash: apiKeyHash,
      keyPrefix: apiKey.substring(0, 8) + '...',
      organizationId: apiKeyRequest.organizationId,
      scopes: apiKeyRequest.scopes,
      status: ApiKeyStatus.ACTIVE,
      expiresAt: apiKeyRequest.expiresAt,
      rateLimit: apiKeyRequest.rateLimit || {
        requestsPerMinute: 100,
        requestsPerHour: 1000,
        requestsPerDay: 10000
      },
      allowedIPs: apiKeyRequest.allowedIPs || [],
      allowedDomains: apiKeyRequest.allowedDomains || [],
      createdAt: now,
      lastUsedAt: null,
      usageStats: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        lastRequestAt: null
      },
      tenantId: apiKeyRequest.organizationId
    };

    await db.createItem('api-keys', apiKeyRecord);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "api_key_created",
      userId: 'system', // API key creation is system-level
      organizationId: apiKeyRequest.organizationId,
      timestamp: now,
      details: {
        apiKeyId,
        apiKeyName: apiKeyRequest.name,
        scopes: apiKeyRequest.scopes,
        hasExpiration: !!apiKeyRequest.expiresAt,
        rateLimit: apiKeyRecord.rateLimit
      },
      tenantId: apiKeyRequest.organizationId
    });

    logger.info("API key created successfully", {
      correlationId,
      apiKeyId,
      apiKeyName: apiKeyRequest.name,
      organizationId: apiKeyRequest.organizationId,
      scopes: apiKeyRequest.scopes
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: apiKeyId,
        name: apiKeyRequest.name,
        apiKey: apiKey, // Only returned once during creation
        keyPrefix: apiKeyRecord.keyPrefix,
        organizationId: apiKeyRequest.organizationId,
        scopes: apiKeyRequest.scopes,
        expiresAt: apiKeyRequest.expiresAt,
        rateLimit: apiKeyRecord.rateLimit,
        createdAt: now,
        message: "API key created successfully. Store this key securely as it won't be shown again."
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create API key failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Validate API key handler
 */
export async function validateApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Validate API key started", { correlationId });

  try {
    // Parse validation request
    let validationRequest: ValidateApiKeyRequest;

    if (request.method === 'GET') {
      // Extract from headers and query params
      const apiKey = request.headers.get('x-api-key') || request.query.get('api_key') || '';
      const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '';
      const userAgent = request.headers.get('user-agent') || '';

      validationRequest = {
        apiKey,
        requestPath: request.url,
        requestMethod: request.method,
        clientIP,
        userAgent
      };
    } else {
      // Parse from request body
      const body = await request.json();
      const { error, value } = validateApiKeySchema.validate(body);

      if (error) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: {
            error: 'Validation Error',
            message: error.details.map(d => d.message).join(', ')
          }
        }, request);
      }

      validationRequest = value;
    }

    if (!validationRequest.apiKey) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          valid: false,
          error: 'API key is required',
          errorCode: 'MISSING_API_KEY'
        }
      }, request);
    }

    // Validate API key
    const validationResult = await performApiKeyValidation(validationRequest);

    // Update usage statistics if key is valid
    if (validationResult.valid && validationResult.apiKeyId) {
      await updateApiKeyUsage(validationResult.apiKeyId, validationRequest);
    }

    logger.info("API key validation completed", {
      correlationId,
      valid: validationResult.valid,
      apiKeyId: validationResult.apiKeyId,
      organizationId: validationResult.organizationId,
      errorCode: validationResult.errorCode
    });

    return addCorsHeaders({
      status: validationResult.valid ? 200 : 401,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: validationResult
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Validate API key failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        valid: false,
        error: "Internal server error",
        errorCode: 'INTERNAL_ERROR'
      }
    }, request);
  }
}

/**
 * Generate API key
 */
function generateApiKey(): string {
  const prefix = 'dk_'; // DocuContext key prefix
  const randomBytes = crypto.randomBytes(32);
  const key = randomBytes.toString('hex');
  return `${prefix}${key}`;
}

/**
 * Hash API key for storage
 */
function hashApiKey(apiKey: string): string {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

/**
 * Perform API key validation
 */
async function performApiKeyValidation(request: ValidateApiKeyRequest): Promise<ApiKeyValidationResponse> {
  try {
    // Hash the provided API key
    const apiKeyHash = hashApiKey(request.apiKey);

    // Find API key in database
    const apiKeyQuery = 'SELECT * FROM c WHERE c.keyHash = @keyHash';
    const apiKeys = await db.queryItems('api-keys', apiKeyQuery, [apiKeyHash]);

    if (apiKeys.length === 0) {
      return {
        valid: false,
        error: 'Invalid API key',
        errorCode: 'INVALID_API_KEY'
      };
    }

    const apiKey = apiKeys[0] as any;

    // Check if API key is active
    if (apiKey.status !== ApiKeyStatus.ACTIVE) {
      return {
        valid: false,
        error: `API key is ${apiKey.status}`,
        errorCode: 'API_KEY_INACTIVE'
      };
    }

    // Check expiration
    if (apiKey.expiresAt && new Date(apiKey.expiresAt) < new Date()) {
      return {
        valid: false,
        error: 'API key has expired',
        errorCode: 'API_KEY_EXPIRED'
      };
    }

    // Check IP restrictions
    if (apiKey.allowedIPs && apiKey.allowedIPs.length > 0 && request.clientIP) {
      if (!apiKey.allowedIPs.includes(request.clientIP)) {
        return {
          valid: false,
          error: 'IP address not allowed',
          errorCode: 'IP_NOT_ALLOWED'
        };
      }
    }

    // Check rate limits
    const rateLimitCheck = await checkRateLimit(apiKey.id, apiKey.rateLimit);
    if (!rateLimitCheck.allowed) {
      return {
        valid: false,
        error: 'Rate limit exceeded',
        errorCode: 'RATE_LIMIT_EXCEEDED',
        rateLimit: rateLimitCheck.rateLimit
      };
    }

    return {
      valid: true,
      apiKeyId: apiKey.id,
      organizationId: apiKey.organizationId,
      scopes: apiKey.scopes,
      rateLimit: rateLimitCheck.rateLimit
    };

  } catch (error) {
    logger.error('API key validation failed', { error, apiKey: request.apiKey.substring(0, 8) + '...' });
    return {
      valid: false,
      error: 'Validation failed',
      errorCode: 'VALIDATION_ERROR'
    };
  }
}

/**
 * Check rate limits
 */
async function checkRateLimit(apiKeyId: string, rateLimit: any): Promise<{
  allowed: boolean;
  rateLimit: {
    remaining: number;
    resetTime: string;
    limit: number;
  };
}> {
  try {
    const now = new Date();
    const currentMinute = Math.floor(now.getTime() / (60 * 1000));

    // Get current usage for this minute
    const usageQuery = 'SELECT * FROM c WHERE c.apiKeyId = @apiKeyId AND c.timeWindow = @timeWindow';
    const usageRecords = await db.queryItems('api-key-usage', usageQuery, [apiKeyId, currentMinute]);

    let currentUsage = 0;
    if (usageRecords.length > 0) {
      currentUsage = (usageRecords[0] as any).requestCount || 0;
    }

    const remaining = Math.max(0, rateLimit.requestsPerMinute - currentUsage);
    const resetTime = new Date((currentMinute + 1) * 60 * 1000).toISOString();

    return {
      allowed: currentUsage < rateLimit.requestsPerMinute,
      rateLimit: {
        remaining,
        resetTime,
        limit: rateLimit.requestsPerMinute
      }
    };

  } catch (error) {
    logger.error('Rate limit check failed', { error, apiKeyId });
    // Allow request if rate limit check fails
    return {
      allowed: true,
      rateLimit: {
        remaining: rateLimit.requestsPerMinute,
        resetTime: new Date(Date.now() + 60000).toISOString(),
        limit: rateLimit.requestsPerMinute
      }
    };
  }
}

/**
 * Update API key usage statistics
 */
async function updateApiKeyUsage(apiKeyId: string, request: ValidateApiKeyRequest): Promise<void> {
  try {
    const now = new Date();
    const currentMinute = Math.floor(now.getTime() / (60 * 1000));

    // Update minute-level usage for rate limiting
    const usageQuery = 'SELECT * FROM c WHERE c.apiKeyId = @apiKeyId AND c.timeWindow = @timeWindow';
    const usageRecords = await db.queryItems('api-key-usage', usageQuery, [apiKeyId, currentMinute]);

    if (usageRecords.length > 0) {
      const usage = usageRecords[0] as any;
      const updatedUsage = {
        ...usage,
        requestCount: (usage.requestCount || 0) + 1,
        lastRequestAt: now.toISOString()
      };
      await db.updateItem('api-key-usage', updatedUsage);
    } else {
      const newUsage = {
        id: uuidv4(),
        apiKeyId,
        timeWindow: currentMinute,
        requestCount: 1,
        createdAt: now.toISOString(),
        lastRequestAt: now.toISOString(),
        tenantId: apiKeyId // Use apiKeyId as tenantId for partitioning
      };
      await db.createItem('api-key-usage', newUsage);
    }

    // Update API key last used timestamp
    const apiKey = await db.readItem('api-keys', apiKeyId, apiKeyId);
    if (apiKey) {
      const updatedApiKey = {
        ...(apiKey as any),
        lastUsedAt: now.toISOString(),
        usageStats: {
          ...(apiKey as any).usageStats,
          totalRequests: ((apiKey as any).usageStats?.totalRequests || 0) + 1,
          successfulRequests: ((apiKey as any).usageStats?.successfulRequests || 0) + 1,
          lastRequestAt: now.toISOString()
        }
      };
      await db.updateItem('api-keys', updatedApiKey);
    }

  } catch (error) {
    logger.error('Failed to update API key usage', { error, apiKeyId });
    // Don't throw error as this shouldn't block the request
  }
}

// Register functions
app.http('api-key-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'api-keys/create',
  handler: createApiKey
});

app.http('api-key-validate', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'anonymous', // API key validation should be accessible without auth
  route: 'api-keys/validate',
  handler: validateApiKey
});
