# PowerShell script to create remaining Service Bus subscriptions
# for enhanced workflow functions

$resourceGroup = "docucontext"
$namespace = "hepzbackend"

Write-Host "🔧 Creating remaining Service Bus subscriptions..." -ForegroundColor Green

# Approval Events Subscriptions
Write-Host "Creating approval-analytics subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name approval-events --name approval-analytics --max-delivery-count 10 --lock-duration PT1M --default-message-time-to-live P7D --enable-batched-operations true

# Signing Events Subscriptions
Write-Host "Creating signing-processor subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name signing-events --name signing-processor --max-delivery-count 5 --lock-duration PT1M --default-message-time-to-live P7D --enable-batched-operations true

Write-Host "Creating signing-notifications subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name signing-events --name signing-notifications --max-delivery-count 10 --lock-duration PT30S --default-message-time-to-live P7D --enable-batched-operations true

Write-Host "Creating signing-audit subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name signing-events --name signing-audit --max-delivery-count 10 --lock-duration PT1M --default-message-time-to-live P7D --enable-batched-operations true

# Workflow Events Subscriptions
Write-Host "Creating workflow-processor subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name workflow-events --name workflow-processor --max-delivery-count 10 --lock-duration PT1M --default-message-time-to-live P14D --enable-batched-operations true

Write-Host "Creating workflow-analytics subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name workflow-events --name workflow-analytics --max-delivery-count 10 --lock-duration PT1M --default-message-time-to-live P14D --enable-batched-operations true

Write-Host "Creating workflow-monitoring subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name workflow-events --name workflow-monitoring --max-delivery-count 10 --lock-duration PT1M --default-message-time-to-live P14D --enable-batched-operations true

# Real-time Events Subscriptions
Write-Host "Creating signalr-processor subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name real-time-events --name signalr-processor --max-delivery-count 10 --lock-duration PT30S --default-message-time-to-live P1D --enable-batched-operations true

Write-Host "Creating collaboration-sync subscription..." -ForegroundColor Yellow
az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name real-time-events --name collaboration-sync --max-delivery-count 10 --lock-duration PT30S --default-message-time-to-live P1D --enable-batched-operations true

# Check if monitoring-events system-monitor subscription exists
Write-Host "Checking monitoring-events system-monitor subscription..." -ForegroundColor Yellow
$monitoringSub = az servicebus topic subscription show --resource-group $resourceGroup --namespace-name $namespace --topic-name monitoring-events --name system-monitor 2>$null
if (-not $monitoringSub) {
    Write-Host "Creating system-monitor subscription..." -ForegroundColor Yellow
    az servicebus topic subscription create --resource-group $resourceGroup --namespace-name $namespace --topic-name monitoring-events --name system-monitor --max-delivery-count 10 --lock-duration PT1M --default-message-time-to-live P10675199DT2H48M5.4775807S --enable-batched-operations true
} else {
    Write-Host "✅ system-monitor subscription already exists" -ForegroundColor Green
}

Write-Host "✅ All Service Bus subscriptions created successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Summary of created subscriptions:" -ForegroundColor Cyan
Write-Host "approval-events:" -ForegroundColor White
Write-Host "  ✅ approval-processor" -ForegroundColor Green
Write-Host "  ✅ approval-notifications" -ForegroundColor Green
Write-Host "  ✅ approval-analytics" -ForegroundColor Green
Write-Host ""
Write-Host "signing-events:" -ForegroundColor White
Write-Host "  ✅ signing-processor" -ForegroundColor Green
Write-Host "  ✅ signing-notifications" -ForegroundColor Green
Write-Host "  ✅ signing-audit" -ForegroundColor Green
Write-Host ""
Write-Host "workflow-events:" -ForegroundColor White
Write-Host "  ✅ workflow-processor" -ForegroundColor Green
Write-Host "  ✅ workflow-analytics" -ForegroundColor Green
Write-Host "  ✅ workflow-monitoring" -ForegroundColor Green
Write-Host ""
Write-Host "real-time-events:" -ForegroundColor White
Write-Host "  ✅ signalr-processor" -ForegroundColor Green
Write-Host "  ✅ collaboration-sync" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Azure Service Bus is now fully configured for enhanced workflow functions!" -ForegroundColor Green
