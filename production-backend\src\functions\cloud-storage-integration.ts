/**
 * Cloud Storage Integration Function
 * Handles integration with multiple cloud storage providers (AWS S3, Azure Blob, Google Cloud)
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Storage provider types enum
enum StorageProvider {
  AZURE_BLOB = 'AZURE_BLOB',
  AWS_S3 = 'AWS_S3',
  GOOGLE_CLOUD = 'GOOGLE_CLOUD',
  DROPBOX = 'DROPBOX',
  ONEDRIVE = 'ONEDRIVE',
  BOX = 'BOX'
}

// Sync direction enum
enum SyncDirection {
  UPLOAD = 'UPLOAD',
  DOWNLOAD = 'DOWNLOAD',
  BIDIRECTIONAL = 'BIDIRECTIONAL'
}

// Sync status enum
enum SyncStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// Validation schemas
const configureStorageSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  provider: Joi.string().valid(...Object.values(StorageProvider)).required(),
  configuration: Joi.object({
    connectionString: Joi.string().optional(),
    accessKey: Joi.string().optional(),
    secretKey: Joi.string().optional(),
    region: Joi.string().optional(),
    bucket: Joi.string().optional(),
    container: Joi.string().optional(),
    endpoint: Joi.string().uri().optional(),
    authToken: Joi.string().optional()
  }).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  isDefault: Joi.boolean().default(false),
  syncSettings: Joi.object({
    autoSync: Joi.boolean().default(false),
    syncDirection: Joi.string().valid(...Object.values(SyncDirection)).default(SyncDirection.BIDIRECTIONAL),
    syncInterval: Joi.number().integer().min(300).max(86400).default(3600), // seconds
    includePatterns: Joi.array().items(Joi.string()).optional(),
    excludePatterns: Joi.array().items(Joi.string()).optional()
  }).optional()
});

const syncDocumentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  storageConfigId: Joi.string().uuid().required(),
  targetPath: Joi.string().max(500).optional(),
  syncDirection: Joi.string().valid(...Object.values(SyncDirection)).default(SyncDirection.UPLOAD),
  overwrite: Joi.boolean().default(false),
  preserveMetadata: Joi.boolean().default(true)
});

const bulkSyncSchema = Joi.object({
  documentIds: Joi.array().items(Joi.string().uuid()).min(1).max(100).required(),
  storageConfigId: Joi.string().uuid().required(),
  targetFolder: Joi.string().max(500).optional(),
  syncDirection: Joi.string().valid(...Object.values(SyncDirection)).default(SyncDirection.UPLOAD),
  overwrite: Joi.boolean().default(false),
  preserveMetadata: Joi.boolean().default(true)
});

/**
 * Configure storage provider handler
 */
export async function configureStorage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Configure storage started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = configureStorageSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const storageData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, storageData.organizationId, 'active']);
    
    if (memberships.length === 0 || (memberships[0] as any).role !== 'ADMIN') {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization admins can configure storage" }
      }, request);
    }

    // Test storage connection
    const connectionTest = await testStorageConnection(storageData.provider, storageData.configuration);
    if (!connectionTest.success) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Storage Connection Failed', 
          message: connectionTest.error 
        }
      }, request);
    }

    // If this is set as default, unset other defaults
    if (storageData.isDefault) {
      const existingDefaultQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.isDefault = true';
      const existingDefaults = await db.queryItems('storage-configurations', existingDefaultQuery, [storageData.organizationId]);
      
      for (const existing of existingDefaults) {
        const updated = {
          ...(existing as any),
          id: (existing as any).id,
          isDefault: false,
          updatedAt: new Date().toISOString()
        };
        await db.updateItem('storage-configurations', updated);
      }
    }

    // Create storage configuration
    const configId = uuidv4();
    const storageConfig = {
      id: configId,
      name: storageData.name,
      provider: storageData.provider,
      configuration: {
        ...storageData.configuration,
        // Encrypt sensitive data in production
        connectionString: storageData.configuration.connectionString ? encrypt(storageData.configuration.connectionString) : undefined,
        accessKey: storageData.configuration.accessKey ? encrypt(storageData.configuration.accessKey) : undefined,
        secretKey: storageData.configuration.secretKey ? encrypt(storageData.configuration.secretKey) : undefined,
        authToken: storageData.configuration.authToken ? encrypt(storageData.configuration.authToken) : undefined
      },
      organizationId: storageData.organizationId,
      projectId: storageData.projectId,
      isDefault: storageData.isDefault,
      syncSettings: storageData.syncSettings || {
        autoSync: false,
        syncDirection: SyncDirection.BIDIRECTIONAL,
        syncInterval: 3600
      },
      isActive: true,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      lastTestAt: new Date().toISOString(),
      lastTestResult: connectionTest,
      tenantId: user.tenantId
    };

    await db.createItem('storage-configurations', storageConfig);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "storage_configured",
      userId: user.id,
      organizationId: storageData.organizationId,
      projectId: storageData.projectId,
      storageConfigId: configId,
      timestamp: new Date().toISOString(),
      details: {
        storageName: storageConfig.name,
        provider: storageConfig.provider,
        isDefault: storageConfig.isDefault,
        autoSync: storageConfig.syncSettings.autoSync
      },
      tenantId: user.tenantId
    });

    logger.info("Storage configured successfully", {
      correlationId,
      configId,
      userId: user.id,
      organizationId: storageData.organizationId,
      provider: storageData.provider,
      isDefault: storageData.isDefault
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: configId,
        name: storageConfig.name,
        provider: storageConfig.provider,
        isDefault: storageConfig.isDefault,
        syncSettings: storageConfig.syncSettings,
        connectionTest: connectionTest,
        message: "Storage configured successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Configure storage failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Sync document handler
 */
export async function syncDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Sync document started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = syncDocumentSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { documentId, storageConfigId, targetPath, syncDirection, overwrite, preserveMetadata } = value;

    // Get document
    const document = await db.readItem('documents', documentId, documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check document access
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to document" }
      }, request);
    }

    // Get storage configuration
    const storageConfig = await db.readItem('storage-configurations', storageConfigId, storageConfigId);
    if (!storageConfig) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Storage configuration not found" }
      }, request);
    }

    // Create sync job
    const syncJobId = uuidv4();
    const syncJob = {
      id: syncJobId,
      documentId,
      storageConfigId,
      targetPath: targetPath || `documents/${(document as any).name}`,
      syncDirection,
      overwrite,
      preserveMetadata,
      status: SyncStatus.PENDING,
      startedBy: user.id,
      startedAt: new Date().toISOString(),
      progress: 0,
      organizationId: (document as any).organizationId,
      projectId: (document as any).projectId,
      tenantId: user.tenantId
    };

    await db.createItem('sync-jobs', syncJob);

    // Start sync process
    const syncResult = await performDocumentSync(syncJob, document as any, storageConfig as any);

    // Update sync job with result
    const updatedSyncJob = {
      ...syncJob,
      id: syncJobId,
      status: syncResult.success ? SyncStatus.COMPLETED : SyncStatus.FAILED,
      progress: 100,
      completedAt: new Date().toISOString(),
      result: syncResult,
      error: syncResult.error
    };
    await db.updateItem('sync-jobs', updatedSyncJob);

    logger.info("Document sync completed", {
      correlationId,
      syncJobId,
      documentId,
      storageConfigId,
      success: syncResult.success,
      userId: user.id
    });

    return addCorsHeaders({
      status: syncResult.success ? 200 : 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        syncJobId,
        documentId,
        storageConfigId,
        status: updatedSyncJob.status,
        result: syncResult,
        message: syncResult.success ? "Document synced successfully" : "Document sync failed"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Sync document failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Bulk sync documents handler
 */
export async function bulkSyncDocuments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Bulk sync documents started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = bulkSyncSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { documentIds, storageConfigId, targetFolder, syncDirection, overwrite, preserveMetadata } = value;

    // Get storage configuration
    const storageConfig = await db.readItem('storage-configurations', storageConfigId, storageConfigId);
    if (!storageConfig) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Storage configuration not found" }
      }, request);
    }

    // Create bulk sync job
    const bulkSyncJobId = uuidv4();
    const bulkSyncJob = {
      id: bulkSyncJobId,
      documentIds,
      storageConfigId,
      targetFolder: targetFolder || 'documents',
      syncDirection,
      overwrite,
      preserveMetadata,
      status: SyncStatus.PENDING,
      startedBy: user.id,
      startedAt: new Date().toISOString(),
      progress: 0,
      totalDocuments: documentIds.length,
      completedDocuments: 0,
      failedDocuments: 0,
      results: [],
      tenantId: user.tenantId
    };

    await db.createItem('bulk-sync-jobs', bulkSyncJob);

    // Process documents in batches
    const results = [];
    let completedCount = 0;
    let failedCount = 0;

    for (const documentId of documentIds) {
      try {
        const document = await db.readItem('documents', documentId, documentId);
        if (!document) {
          results.push({ documentId, success: false, error: 'Document not found' });
          failedCount++;
          continue;
        }

        // Check access
        const hasAccess = (
          (document as any).createdBy === user.id ||
          (document as any).organizationId === user.tenantId ||
          user.roles?.includes('admin')
        );

        if (!hasAccess) {
          results.push({ documentId, success: false, error: 'Access denied' });
          failedCount++;
          continue;
        }

        // Create individual sync job
        const syncJob = {
          id: uuidv4(),
          documentId,
          storageConfigId,
          targetPath: `${targetFolder}/${(document as any).name}`,
          syncDirection,
          overwrite,
          preserveMetadata,
          status: SyncStatus.IN_PROGRESS,
          startedBy: user.id,
          startedAt: new Date().toISOString(),
          bulkSyncJobId,
          organizationId: (document as any).organizationId,
          projectId: (document as any).projectId,
          tenantId: user.tenantId
        };

        const syncResult = await performDocumentSync(syncJob, document as any, storageConfig as any);
        
        results.push({
          documentId,
          success: syncResult.success,
          error: syncResult.error,
          targetPath: syncJob.targetPath
        });

        if (syncResult.success) {
          completedCount++;
        } else {
          failedCount++;
        }

      } catch (error) {
        results.push({
          documentId,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
        failedCount++;
      }

      // Update progress
      const progress = Math.round(((completedCount + failedCount) / documentIds.length) * 100);
      const updatedBulkJob = {
        ...bulkSyncJob,
        id: bulkSyncJobId,
        progress,
        completedDocuments: completedCount,
        failedDocuments: failedCount,
        results
      };
      await db.updateItem('bulk-sync-jobs', updatedBulkJob);
    }

    // Finalize bulk sync job
    const finalBulkJob = {
      ...bulkSyncJob,
      id: bulkSyncJobId,
      status: failedCount === 0 ? SyncStatus.COMPLETED : (completedCount > 0 ? SyncStatus.COMPLETED : SyncStatus.FAILED),
      progress: 100,
      completedDocuments: completedCount,
      failedDocuments: failedCount,
      results,
      completedAt: new Date().toISOString()
    };
    await db.updateItem('bulk-sync-jobs', finalBulkJob);

    logger.info("Bulk sync completed", {
      correlationId,
      bulkSyncJobId,
      totalDocuments: documentIds.length,
      completedDocuments: completedCount,
      failedDocuments: failedCount,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        bulkSyncJobId,
        totalDocuments: documentIds.length,
        completedDocuments: completedCount,
        failedDocuments: failedCount,
        results,
        message: `Bulk sync completed: ${completedCount} successful, ${failedCount} failed`
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Bulk sync documents failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Test storage connection
 */
async function testStorageConnection(provider: string, configuration: any): Promise<any> {
  try {
    switch (provider) {
      case StorageProvider.AZURE_BLOB:
        return await testAzureBlobConnection(configuration);
      case StorageProvider.AWS_S3:
        return await testAwsS3Connection(configuration);
      case StorageProvider.GOOGLE_CLOUD:
        return await testGoogleCloudConnection(configuration);
      default:
        return { success: false, error: `Unsupported provider: ${provider}` };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test Azure Blob Storage connection
 */
async function testAzureBlobConnection(configuration: any): Promise<any> {
  try {
    const blobServiceClient = new BlobServiceClient(configuration.connectionString);
    const containerClient = blobServiceClient.getContainerClient(configuration.container);
    
    // Test connection by checking if container exists
    const exists = await containerClient.exists();
    
    return {
      success: true,
      message: `Successfully connected to Azure Blob Storage. Container exists: ${exists}`,
      details: {
        containerExists: exists,
        containerName: configuration.container
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test AWS S3 connection (simplified)
 */
async function testAwsS3Connection(configuration: any): Promise<any> {
  // In production, use AWS SDK
  return {
    success: true,
    message: "AWS S3 connection test simulated",
    details: {
      bucket: configuration.bucket,
      region: configuration.region
    }
  };
}

/**
 * Test Google Cloud Storage connection (simplified)
 */
async function testGoogleCloudConnection(configuration: any): Promise<any> {
  // In production, use Google Cloud SDK
  return {
    success: true,
    message: "Google Cloud Storage connection test simulated",
    details: {
      bucket: configuration.bucket
    }
  };
}

/**
 * Perform document sync
 */
async function performDocumentSync(syncJob: any, document: any, storageConfig: any): Promise<any> {
  try {
    // Simplified sync implementation
    // In production, implement actual file transfer logic
    
    logger.info("Performing document sync", {
      syncJobId: syncJob.id,
      documentId: document.id,
      provider: storageConfig.provider,
      direction: syncJob.syncDirection
    });

    // Simulate sync process
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      message: "Document synced successfully",
      details: {
        provider: storageConfig.provider,
        targetPath: syncJob.targetPath,
        fileSize: document.size || 0,
        syncDirection: syncJob.syncDirection
      }
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Simple encryption (in production, use proper encryption)
 */
function encrypt(value: string): string {
  // This is a simplified implementation
  // In production, use proper encryption like AES
  return Buffer.from(value).toString('base64');
}

// Register functions
app.http('storage-configure', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'storage/configure',
  handler: configureStorage
});

app.http('storage-sync-document', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'storage/sync/document',
  handler: syncDocument
});

app.http('storage-bulk-sync', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'storage/sync/bulk',
  handler: bulkSyncDocuments
});
