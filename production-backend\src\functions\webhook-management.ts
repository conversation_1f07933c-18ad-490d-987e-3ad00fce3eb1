/**
 * Webhook Management Function
 * Handles webhook registration, management, and delivery
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Webhook event types enum
enum WebhookEventType {
  DOCUMENT_CREATED = 'document.created',
  DOCUMENT_UPDATED = 'document.updated',
  DOCUMENT_DELETED = 'document.deleted',
  DOCUMENT_SHARED = 'document.shared',
  WORKFLOW_STARTED = 'workflow.started',
  WORKFLOW_COMPLETED = 'workflow.completed',
  WORKFLOW_FAILED = 'workflow.failed',
  USER_CREATED = 'user.created',
  ORGANIZATION_CREATED = 'organization.created',
  PROJECT_CREATED = 'project.created',
  PERMISSION_GRANTED = 'permission.granted',
  AUDIT_EVENT = 'audit.event'
}

// Validation schemas
const createWebhookSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  url: Joi.string().uri().required(),
  events: Joi.array().items(Joi.string().valid(...Object.values(WebhookEventType))).min(1).required(),
  secret: Joi.string().min(8).max(100).optional(),
  headers: Joi.object().pattern(Joi.string(), Joi.string()).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  isActive: Joi.boolean().default(true),
  retryConfig: Joi.object({
    maxRetries: Joi.number().integer().min(0).max(10).default(3),
    retryDelay: Joi.number().integer().min(1000).max(300000).default(5000),
    backoffMultiplier: Joi.number().min(1).max(5).default(2)
  }).optional()
});

const updateWebhookSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  url: Joi.string().uri().optional(),
  events: Joi.array().items(Joi.string().valid(...Object.values(WebhookEventType))).min(1).optional(),
  secret: Joi.string().min(8).max(100).optional(),
  headers: Joi.object().pattern(Joi.string(), Joi.string()).optional(),
  isActive: Joi.boolean().optional(),
  retryConfig: Joi.object({
    maxRetries: Joi.number().integer().min(0).max(10).optional(),
    retryDelay: Joi.number().integer().min(1000).max(300000).optional(),
    backoffMultiplier: Joi.number().min(1).max(5).optional()
  }).optional()
});

const testWebhookSchema = Joi.object({
  webhookId: Joi.string().uuid().required(),
  eventType: Joi.string().valid(...Object.values(WebhookEventType)).required(),
  testData: Joi.object().optional()
});

/**
 * Create webhook handler
 */
export async function createWebhook(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create webhook started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createWebhookSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const webhookData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, webhookData.organizationId, 'active']);

    if (memberships.length === 0 || (memberships[0] as any).role !== 'ADMIN') {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization admins can create webhooks" }
      }, request);
    }

    // Create webhook
    const webhookId = uuidv4();
    const webhook = {
      id: webhookId,
      name: webhookData.name,
      url: webhookData.url,
      events: webhookData.events,
      secret: webhookData.secret || generateWebhookSecret(),
      headers: webhookData.headers || {},
      organizationId: webhookData.organizationId,
      projectId: webhookData.projectId,
      isActive: webhookData.isActive,
      retryConfig: webhookData.retryConfig || {
        maxRetries: 3,
        retryDelay: 5000,
        backoffMultiplier: 2
      },
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      statistics: {
        totalDeliveries: 0,
        successfulDeliveries: 0,
        failedDeliveries: 0,
        lastDeliveryAt: null,
        lastSuccessAt: null,
        lastFailureAt: null
      },
      tenantId: user.tenantId
    };

    await db.createItem('webhooks', webhook);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "webhook_created",
      userId: user.id,
      organizationId: webhookData.organizationId,
      projectId: webhookData.projectId,
      webhookId,
      timestamp: new Date().toISOString(),
      details: {
        webhookName: webhook.name,
        url: webhook.url,
        events: webhook.events,
        isActive: webhook.isActive
      },
      tenantId: user.tenantId
    });

    logger.info("Webhook created successfully", {
      correlationId,
      webhookId,
      userId: user.id,
      organizationId: webhookData.organizationId,
      events: webhookData.events
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: webhookId,
        name: webhook.name,
        url: webhook.url,
        events: webhook.events,
        isActive: webhook.isActive,
        secret: webhook.secret,
        message: "Webhook created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create webhook failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * List webhooks handler
 */
export async function listWebhooks(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List webhooks started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;
    const organizationId = request.query.get('organizationId');

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Organization ID is required' }
      }, request);
    }

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Get webhooks for organization
    const webhookQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId ORDER BY c.createdAt DESC';
    const webhooks = await db.queryItems('webhooks', webhookQuery, [organizationId]);

    // Remove sensitive information for non-admins
    const isAdmin = (memberships[0] as any).role === 'ADMIN';
    const sanitizedWebhooks = webhooks.map((webhook: any) => {
      const sanitized = { ...webhook };
      if (!isAdmin) {
        delete sanitized.secret;
        delete sanitized.headers;
      }
      return sanitized;
    });

    logger.info("Webhooks listed successfully", {
      correlationId,
      userId: user.id,
      organizationId,
      count: webhooks.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        organizationId,
        webhooks: sanitizedWebhooks,
        total: webhooks.length
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List webhooks failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Test webhook handler
 */
export async function testWebhook(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Test webhook started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = testWebhookSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { webhookId, eventType, testData } = value;

    // Get webhook
    const webhook = await db.readItem('webhooks', webhookId, webhookId);
    if (!webhook) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Webhook not found" }
      }, request);
    }

    // Check access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, (webhook as any).organizationId, 'active']);

    if (memberships.length === 0 || (memberships[0] as any).role !== 'ADMIN') {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Only organization admins can test webhooks" }
      }, request);
    }

    // Create test payload
    const testPayload = {
      id: uuidv4(),
      event: eventType,
      timestamp: new Date().toISOString(),
      data: testData || {
        test: true,
        message: "This is a test webhook delivery",
        triggeredBy: user.id
      },
      webhook: {
        id: webhookId,
        name: (webhook as any).name
      }
    };

    // Attempt webhook delivery
    const deliveryResult = await deliverWebhook(webhook as any, testPayload, true);

    // Create delivery record
    await db.createItem('webhook-deliveries', {
      id: uuidv4(),
      webhookId,
      eventType,
      payload: testPayload,
      deliveryAttempt: 1,
      isTest: true,
      success: deliveryResult.success,
      statusCode: deliveryResult.statusCode,
      responseBody: deliveryResult.responseBody,
      errorMessage: deliveryResult.errorMessage,
      deliveredAt: new Date().toISOString(),
      organizationId: (webhook as any).organizationId,
      tenantId: user.tenantId
    });

    logger.info("Webhook test completed", {
      correlationId,
      webhookId,
      eventType,
      success: deliveryResult.success,
      statusCode: deliveryResult.statusCode,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        webhookId,
        eventType,
        success: deliveryResult.success,
        statusCode: deliveryResult.statusCode,
        responseTime: deliveryResult.responseTime,
        message: deliveryResult.success ? "Webhook test successful" : "Webhook test failed",
        details: deliveryResult.errorMessage || deliveryResult.responseBody
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Test webhook failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Deliver webhook payload
 */
async function deliverWebhook(webhook: any, payload: any, isTest: boolean = false): Promise<any> {
  const startTime = Date.now();

  try {
    // Prepare headers
    const headers: any = {
      'Content-Type': 'application/json',
      'User-Agent': 'HEPZ-Webhooks/1.0',
      'X-Webhook-ID': webhook.id,
      'X-Webhook-Event': payload.event,
      'X-Webhook-Timestamp': payload.timestamp,
      ...webhook.headers
    };

    // Add signature if secret is provided
    if (webhook.secret) {
      const signature = generateWebhookSignature(JSON.stringify(payload), webhook.secret);
      headers['X-Webhook-Signature'] = signature;
    }

    if (isTest) {
      headers['X-Webhook-Test'] = 'true';
    }

    // Production HTTP request using fetch
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const response = await fetch(webhook.url, {
      method: 'POST',
      headers,
      body: JSON.stringify(payload),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    const responseTime = Date.now() - startTime;
    const responseBody = await response.text();

    const isSuccess = response.status >= 200 && response.status < 300;

    logger.info('Webhook delivery completed', {
      url: webhook.url,
      statusCode: response.status,
      success: isSuccess,
      responseTime,
      responseBodyLength: responseBody.length
    });

    return {
      success: isSuccess,
      statusCode: response.status,
      responseBody: responseBody.substring(0, 1000), // Limit response body size
      responseTime,
      errorMessage: response.status >= 400 ? `HTTP ${response.status}: ${response.statusText}` : null
    };

  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      success: false,
      statusCode: 0,
      responseBody: null,
      responseTime,
      errorMessage: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Generate webhook secret
 */
function generateWebhookSecret(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Generate webhook signature using HMAC-SHA256
 */
function generateWebhookSignature(payload: string, secret: string): string {
  try {
    // Production HMAC-SHA256 signature generation
    const crypto = require('crypto');
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(payload, 'utf8');
    const signature = hmac.digest('hex');

    logger.info('Webhook signature generated', {
      payloadLength: payload.length,
      signatureLength: signature.length
    });

    return `sha256=${signature}`;
  } catch (error) {
    logger.error('Failed to generate webhook signature', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw new Error('Webhook signature generation failed');
  }
}



/**
 * Combined webhooks handler
 */
async function handleWebhooks(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const method = request.method.toUpperCase();

  switch (method) {
    case 'POST':
      return await createWebhook(request, context);
    case 'GET':
      return await listWebhooks(request, context);
    case 'OPTIONS':
      return handlePreflight(request) || { status: 200 };
    default:
      return addCorsHeaders({
        status: 405,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Method not allowed' }
      }, request);
  }
}

// Register functions
app.http('webhooks', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'webhooks',
  handler: handleWebhooks
});

app.http('webhook-test', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'webhooks/test',
  handler: testWebhook
});
