/**
 * Audit Logging Function
 * Handles comprehensive audit logging and compliance tracking
 * Enhanced from existing audit-log.ts with additional features
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Enhanced audit types and enums
enum AuditEventType {
  // Authentication & Authorization
  USER_LOGIN = 'USER_LOGIN',
  USER_LOGOUT = 'USER_LOGOUT',
  USER_LOGIN_FAILED = 'USER_LOGIN_FAILED',
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PERMISSION_REVOKED = 'PERMISSION_REVOKED',
  ROLE_ASSIGNED = 'ROLE_ASSIGNED',
  ROLE_REMOVED = 'ROLE_REMOVED',

  // Document Operations
  DOCUMENT_CREATED = 'DOCUMENT_CREATED',
  DOCUMENT_VIEWED = 'DOCUMENT_VIEWED',
  DOCUMENT_UPDATED = 'DOCUMENT_UPDATED',
  DOCUMENT_DELETED = 'DOCUMENT_DELETED',
  DOCUMENT_SHARED = 'DOCUMENT_SHARED',
  DOCUMENT_DOWNLOADED = 'DOCUMENT_DOWNLOADED',
  DOCUMENT_APPROVED = 'DOCUMENT_APPROVED',
  DOCUMENT_REJECTED = 'DOCUMENT_REJECTED',

  // System Operations
  SYSTEM_CONFIG_CHANGED = 'SYSTEM_CONFIG_CHANGED',
  BACKUP_CREATED = 'BACKUP_CREATED',
  BACKUP_RESTORED = 'BACKUP_RESTORED',
  DATA_EXPORTED = 'DATA_EXPORTED',
  DATA_IMPORTED = 'DATA_IMPORTED',

  // Security Events
  SECURITY_BREACH_DETECTED = 'SECURITY_BREACH_DETECTED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  PASSWORD_CHANGED = 'PASSWORD_CHANGED',
  TWO_FACTOR_ENABLED = 'TWO_FACTOR_ENABLED',
  TWO_FACTOR_DISABLED = 'TWO_FACTOR_DISABLED',

  // Compliance Events
  GDPR_REQUEST = 'GDPR_REQUEST',
  DATA_RETENTION_APPLIED = 'DATA_RETENTION_APPLIED',
  COMPLIANCE_VIOLATION = 'COMPLIANCE_VIOLATION'
}

enum AuditSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

enum ComplianceFramework {
  GDPR = 'GDPR',
  HIPAA = 'HIPAA',
  SOX = 'SOX',
  ISO27001 = 'ISO27001',
  PCI_DSS = 'PCI_DSS'
}

// Validation schemas
const createAuditLogSchema = Joi.object({
  eventType: Joi.string().valid(...Object.values(AuditEventType)).required(),
  description: Joi.string().min(1).max(1000).required(),
  severity: Joi.string().valid(...Object.values(AuditSeverity)).default(AuditSeverity.MEDIUM),
  resourceType: Joi.string().max(50).optional(),
  resourceId: Joi.string().max(100).optional(),
  details: Joi.object().optional(),
  complianceFrameworks: Joi.array().items(Joi.string().valid(...Object.values(ComplianceFramework))).optional(),
  metadata: Joi.object().optional()
});

const getAuditLogsSchema = Joi.object({
  eventType: Joi.string().valid(...Object.values(AuditEventType)).optional(),
  severity: Joi.string().valid(...Object.values(AuditSeverity)).optional(),
  userId: Joi.string().uuid().optional(),
  organizationId: Joi.string().uuid().optional(),
  resourceType: Joi.string().optional(),
  resourceId: Joi.string().optional(),
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
  complianceFramework: Joi.string().valid(...Object.values(ComplianceFramework)).optional(),
  page: Joi.number().min(1).default(1),
  limit: Joi.number().min(1).max(1000).default(50),
  sortBy: Joi.string().valid('timestamp', 'severity', 'eventType').default('timestamp'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

interface CreateAuditLogRequest {
  eventType: AuditEventType;
  description: string;
  severity?: AuditSeverity;
  resourceType?: string;
  resourceId?: string;
  details?: any;
  complianceFrameworks?: ComplianceFramework[];
  metadata?: any;
}

interface AuditLogEntry {
  id: string;
  eventType: AuditEventType;
  userId: string;
  organizationId?: string;
  description: string;
  severity: AuditSeverity;
  resourceType?: string;
  resourceId?: string;
  details: any;
  complianceFrameworks: ComplianceFramework[];
  metadata: any;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  sessionId?: string;
  correlationId?: string;
  tenantId: string;
}

/**
 * Create audit log entry handler
 */
export async function createAuditLog(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create audit log started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createAuditLogSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const auditRequest: CreateAuditLogRequest = value;

    // Extract request metadata
    const ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const sessionId = request.headers.get('x-session-id');

    // Create audit log entry
    const auditLogId = uuidv4();
    const now = new Date().toISOString();

    const auditLogEntry: AuditLogEntry = {
      id: auditLogId,
      eventType: auditRequest.eventType,
      userId: user.id,
      organizationId: user.organizationId || user.tenantId || user.id,
      description: auditRequest.description,
      severity: auditRequest.severity || AuditSeverity.MEDIUM,
      resourceType: auditRequest.resourceType,
      resourceId: auditRequest.resourceId,
      details: auditRequest.details || {},
      complianceFrameworks: auditRequest.complianceFrameworks || [],
      metadata: {
        ...auditRequest.metadata,
        userEmail: user.email,
        userName: user.name || user.email
      },
      timestamp: now,
      ipAddress,
      userAgent,
      sessionId: sessionId || undefined,
      correlationId,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('audit-logs', auditLogEntry);

    // Check for compliance violations
    await checkComplianceViolations(auditLogEntry);

    // Check for security alerts
    await checkSecurityAlerts(auditLogEntry, user);

    // Publish domain event
    await eventService.publishEvent({
      type: 'AuditLogCreated',
      aggregateId: auditLogId,
      aggregateType: 'AuditLog',
      version: 1,
      data: {
        auditLog: auditLogEntry,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: user.organizationId || user.tenantId || user.id,
      tenantId: user.tenantId || user.id
    });

    logger.info("Audit log created successfully", {
      correlationId,
      auditLogId,
      eventType: auditRequest.eventType,
      severity: auditRequest.severity,
      userId: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: auditLogId,
        eventType: auditRequest.eventType,
        severity: auditRequest.severity,
        timestamp: now,
        message: "Audit log created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create audit log failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get audit logs handler
 */
export async function getAuditLogs(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Get audit logs started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check if user has audit access
    const hasAccess = await checkAuditAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to audit logs" }
      }, request);
    }

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      eventType: url.searchParams.get('eventType') || undefined,
      severity: url.searchParams.get('severity') || undefined,
      userId: url.searchParams.get('userId') || undefined,
      organizationId: url.searchParams.get('organizationId') || undefined,
      resourceType: url.searchParams.get('resourceType') || undefined,
      resourceId: url.searchParams.get('resourceId') || undefined,
      startDate: url.searchParams.get('startDate') || undefined,
      endDate: url.searchParams.get('endDate') || undefined,
      complianceFramework: url.searchParams.get('complianceFramework') || undefined,
      page: parseInt(url.searchParams.get('page') || '1'),
      limit: parseInt(url.searchParams.get('limit') || '50'),
      sortBy: url.searchParams.get('sortBy') || 'timestamp',
      sortOrder: url.searchParams.get('sortOrder') || 'desc'
    };

    // Validate query parameters
    const { error, value } = getAuditLogsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const filters = value;

    // Build query
    const auditLogs = await getAuditLogsWithFilters(filters, user);

    // Calculate pagination
    const totalCount = auditLogs.length;
    const startIndex = (filters.page - 1) * filters.limit;
    const endIndex = startIndex + filters.limit;
    const paginatedLogs = auditLogs.slice(startIndex, endIndex);

    logger.info("Audit logs retrieved successfully", {
      correlationId,
      totalCount,
      page: filters.page,
      limit: filters.limit,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        auditLogs: paginatedLogs,
        pagination: {
          page: filters.page,
          limit: filters.limit,
          totalCount,
          totalPages: Math.ceil(totalCount / filters.limit),
          hasNext: endIndex < totalCount,
          hasPrevious: filters.page > 1
        },
        filters
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get audit logs failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkAuditAccess(user: any): Promise<boolean> {
  try {
    // Check if user has admin role or audit access
    if (user.roles?.includes('admin') || user.roles?.includes('auditor')) {
      return true;
    }

    // Check organization-level audit permissions
    if (user.organizationId) {
      const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [user.organizationId, user.id, 'ACTIVE']);

      if (memberships.length > 0) {
        const membership = memberships[0] as any;
        return membership.role === 'OWNER' || membership.role === 'ADMIN';
      }
    }

    return false;
  } catch (error) {
    logger.error('Failed to check audit access', { error, userId: user.id });
    return false;
  }
}

async function getAuditLogsWithFilters(filters: any, user: any): Promise<any[]> {
  try {
    let query = 'SELECT * FROM c WHERE 1=1';
    const parameters: any[] = [];

    // Apply filters
    if (filters.eventType) {
      query += ' AND c.eventType = @eventType';
      parameters.push(filters.eventType);
    }

    if (filters.severity) {
      query += ' AND c.severity = @severity';
      parameters.push(filters.severity);
    }

    if (filters.userId) {
      query += ' AND c.userId = @userId';
      parameters.push(filters.userId);
    }

    if (filters.organizationId) {
      query += ' AND c.organizationId = @organizationId';
      parameters.push(filters.organizationId);
    } else if (!user.roles?.includes('admin')) {
      // Non-admin users can only see their organization's logs
      query += ' AND c.organizationId = @userOrgId';
      parameters.push(user.organizationId);
    }

    if (filters.resourceType) {
      query += ' AND c.resourceType = @resourceType';
      parameters.push(filters.resourceType);
    }

    if (filters.resourceId) {
      query += ' AND c.resourceId = @resourceId';
      parameters.push(filters.resourceId);
    }

    if (filters.startDate) {
      query += ' AND c.timestamp >= @startDate';
      parameters.push(filters.startDate);
    }

    if (filters.endDate) {
      query += ' AND c.timestamp <= @endDate';
      parameters.push(filters.endDate);
    }

    if (filters.complianceFramework) {
      query += ' AND ARRAY_CONTAINS(c.complianceFrameworks, @complianceFramework)';
      parameters.push(filters.complianceFramework);
    }

    // Add sorting
    query += ` ORDER BY c.${filters.sortBy} ${filters.sortOrder.toUpperCase()}`;

    return await db.queryItems('audit-logs', query, parameters);

  } catch (error) {
    logger.error('Failed to get audit logs with filters', { error, filters });
    throw error;
  }
}

async function checkComplianceViolations(auditLog: AuditLogEntry): Promise<void> {
  try {
    // Check for potential compliance violations based on event type and patterns
    const violations: string[] = [];

    // GDPR violations
    if (auditLog.eventType === AuditEventType.DATA_EXPORTED &&
        !auditLog.details?.gdprCompliant) {
      violations.push('Potential GDPR violation: Data export without proper consent');
    }

    // Security violations
    if (auditLog.eventType === AuditEventType.USER_LOGIN_FAILED) {
      const recentFailures = await getRecentLoginFailures(auditLog.userId, auditLog.ipAddress);
      if (recentFailures >= 5) {
        violations.push('Security violation: Multiple failed login attempts detected');
      }
    }

    // Create compliance violation records
    for (const violation of violations) {
      await db.createItem('compliance-violations', {
        id: uuidv4(),
        auditLogId: auditLog.id,
        violation,
        severity: AuditSeverity.HIGH,
        detectedAt: new Date().toISOString(),
        status: 'OPEN',
        tenantId: auditLog.tenantId
      });
    }

  } catch (error) {
    logger.error('Failed to check compliance violations', { error, auditLogId: auditLog.id });
  }
}

async function checkSecurityAlerts(auditLog: AuditLogEntry, user: any): Promise<void> {
  try {
    // Check for security-related events that require alerts
    const securityEvents = [
      AuditEventType.SECURITY_BREACH_DETECTED,
      AuditEventType.SUSPICIOUS_ACTIVITY,
      AuditEventType.USER_LOGIN_FAILED
    ];

    if (securityEvents.includes(auditLog.eventType)) {
      // Create security alert
      await db.createItem('security-alerts', {
        id: uuidv4(),
        auditLogId: auditLog.id,
        alertType: auditLog.eventType,
        severity: auditLog.severity,
        userId: user.id,
        organizationId: auditLog.organizationId,
        description: auditLog.description,
        ipAddress: auditLog.ipAddress,
        createdAt: new Date().toISOString(),
        status: 'OPEN',
        tenantId: auditLog.tenantId
      });
    }

  } catch (error) {
    logger.error('Failed to check security alerts', { error, auditLogId: auditLog.id });
  }
}

async function getRecentLoginFailures(userId: string, ipAddress: string): Promise<number> {
  try {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const query = 'SELECT VALUE COUNT(1) FROM c WHERE c.eventType = @eventType AND (c.userId = @userId OR c.ipAddress = @ipAddress) AND c.timestamp >= @since';
    const result = await db.queryItems('audit-logs', query, [AuditEventType.USER_LOGIN_FAILED, userId, ipAddress, oneHourAgo]);
    return Number(result[0]) || 0;
  } catch (error) {
    logger.error('Failed to get recent login failures', { error, userId, ipAddress });
    return 0;
  }
}

// Register functions
app.http('audit-log-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'audit/logs/create',
  handler: createAuditLog
});

app.http('audit-logs-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'audit/logs/get',
  handler: getAuditLogs
});
