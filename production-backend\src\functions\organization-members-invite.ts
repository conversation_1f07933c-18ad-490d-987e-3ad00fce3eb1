/**
 * Organization Members Invite Function
 * Handles inviting new members to organizations
 * Migrated from old-arch/src/organization-service/members/invite/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Organization roles
enum OrganizationRole {
  OWNER = 'OWNER',
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
  VIEWER = 'VIEWER',
  GUEST = 'GUEST'
}

// Validation schema
const inviteMemberSchema = Joi.object({
  email: Joi.string().email().required(),
  role: Joi.string().valid(...Object.values(OrganizationRole)).default(OrganizationRole.MEMBER),
  permissions: Joi.array().items(Joi.string()).optional(),
  message: Joi.string().max(500).optional(),
  expiresInDays: Joi.number().min(1).max(30).default(7)
});

interface InviteMemberRequest {
  email: string;
  role: OrganizationRole;
  permissions?: string[];
  message?: string;
  expiresInDays: number;
}

/**
 * Invite organization member handler
 */
export async function inviteOrganizationMember(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const organizationId = request.params.organizationId;

  logger.info("Invite organization member started", { correlationId, organizationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization ID is required" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = inviteMemberSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const inviteRequest: InviteMemberRequest = value;

    // Get organization
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user has permission to invite members
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'ACTIVE']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    const userMembership = memberships[0] as any;
    const canInvite = userMembership.role === 'OWNER' || userMembership.role === 'ADMIN';

    if (!canInvite) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to invite members" }
      }, request);
    }

    // Check if user is already a member or has pending invitation
    const existingMemberQuery = 'SELECT * FROM c WHERE c.email = @email AND c.organizationId = @orgId';
    const existingMembers = await db.queryItems('organization-members', existingMemberQuery, [inviteRequest.email, organizationId]);

    if (existingMembers.length > 0) {
      const existingMember = existingMembers[0] as any;
      if (existingMember.status === 'ACTIVE') {
        return addCorsHeaders({
          status: 409,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "User is already a member of this organization" }
        }, request);
      }
    }

    const existingInviteQuery = 'SELECT * FROM c WHERE c.email = @email AND c.organizationId = @orgId AND c.status = @status';
    const existingInvites = await db.queryItems('organization-invitations', existingInviteQuery, [inviteRequest.email, organizationId, 'PENDING']);

    if (existingInvites.length > 0) {
      return addCorsHeaders({
        status: 409,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "User already has a pending invitation to this organization" }
      }, request);
    }

    // Check organization member limits
    const orgData = organization as any;
    if (orgData.billing?.limits?.maxActiveUsers && orgData.billing.limits.maxActiveUsers > 0) {
      const currentMemberCount = await getCurrentMemberCount(organizationId);
      if (currentMemberCount >= orgData.billing.limits.maxActiveUsers) {
        return addCorsHeaders({
          status: 403,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: {
            error: "Member limit reached for this organization tier",
            limit: orgData.billing.limits.maxActiveUsers,
            current: currentMemberCount
          }
        }, request);
      }
    }

    // Create invitation
    const invitationId = uuidv4();
    const now = new Date().toISOString();
    const expiresAt = new Date(Date.now() + inviteRequest.expiresInDays * 24 * 60 * 60 * 1000).toISOString();

    const invitation = {
      id: invitationId,
      organizationId,
      email: inviteRequest.email,
      role: inviteRequest.role,
      permissions: inviteRequest.permissions || [],
      invitedBy: user.id,
      invitedAt: now,
      expiresAt,
      status: 'PENDING',
      message: inviteRequest.message,
      tenantId: user.tenantId
    };

    await db.createItem('organization-invitations', invitation);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "organization_member_invited",
      userId: user.id,
      organizationId,
      timestamp: now,
      details: {
        invitedEmail: inviteRequest.email,
        role: inviteRequest.role,
        invitationId,
        organizationName: orgData.name
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'OrganizationMemberInvited',
      aggregateId: organizationId,
      aggregateType: 'Organization',
      version: 1,
      data: {
        invitation,
        invitedBy: user.id,
        organizationName: orgData.name
      },
      userId: user.id,
      organizationId,
      tenantId: user.tenantId
    });

    // Send invitation notification (email would be sent here in production)
    const inviteUrl = `${process.env.FRONTEND_BASE_URL || 'https://app.docucontext.com'}/invitations/${invitationId}`;

    // For now, we'll create an in-app notification for the inviter
    await notificationService.sendNotification({
      userId: user.id,
      type: 'ORGANIZATION_INVITATION_SENT',
      title: 'Invitation sent successfully',
      message: `Invitation sent to ${inviteRequest.email} to join ${orgData.name} as ${inviteRequest.role}.`,
      priority: 'normal',
      metadata: {
        invitationId,
        invitedEmail: inviteRequest.email,
        organizationId,
        organizationName: orgData.name,
        role: inviteRequest.role,
        inviteUrl
      },
      organizationId
    });

    logger.info("Organization member invitation created successfully", {
      correlationId,
      organizationId,
      invitationId,
      invitedEmail: inviteRequest.email,
      role: inviteRequest.role,
      invitedBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        invitationId,
        email: inviteRequest.email,
        role: inviteRequest.role,
        organizationId,
        organizationName: orgData.name,
        expiresAt,
        inviteUrl,
        message: "Invitation sent successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Invite organization member failed", {
      correlationId,
      organizationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get current member count for organization
 */
async function getCurrentMemberCount(organizationId: string): Promise<number> {
  try {
    const memberQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status';
    const result = await db.queryItems('organization-members', memberQuery, [organizationId, 'ACTIVE']);
    return Number(result[0]) || 0;
  } catch (error) {
    logger.error('Failed to get member count', { error, organizationId });
    return 0;
  }
}

// Register functions
app.http('organization-members-invite', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}/members/invite',
  handler: inviteOrganizationMember
});
