/**
 * Predictive Analytics Function
 * Handles predictive analytics and machine learning insights
 * Migrated from old-arch/src/analytics-service/predictive/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Predictive analytics types and enums
enum PredictionType {
  USER_BEHAVIOR = 'USER_BEHAVIOR',
  DOCUMENT_USAGE = 'DOCUMENT_USAGE',
  WORKFLOW_PERFORMANCE = 'WORKFLOW_PERFORMANCE',
  SYSTEM_LOAD = 'SYSTEM_LOAD',
  CHURN_PREDICTION = 'CHURN_PREDICTION',
  GROWTH_FORECAST = 'GROWTH_FORECAST',
  ANOMALY_DETECTION = 'ANOMALY_DETECTION'
}

enum ModelStatus {
  TRAINING = 'TRAINING',
  READY = 'READY',
  UPDATING = 'UPDATING',
  FAILED = 'FAILED',
  DEPRECATED = 'DEPRECATED'
}

enum ConfidenceLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  VERY_HIGH = 'VERY_HIGH'
}

// Validation schemas
const generatePredictionSchema = Joi.object({
  predictionType: Joi.string().valid(...Object.values(PredictionType)).required(),
  organizationId: Joi.string().uuid().required(),
  timeHorizon: Joi.number().min(1).max(365).default(30), // days
  parameters: Joi.object({
    userId: Joi.string().uuid().optional(),
    documentId: Joi.string().uuid().optional(),
    workflowId: Joi.string().uuid().optional(),
    features: Joi.object().optional(),
    filters: Joi.object().optional()
  }).optional(),
  options: Joi.object({
    includeConfidenceInterval: Joi.boolean().default(true),
    includeFeatureImportance: Joi.boolean().default(false),
    modelVersion: Joi.string().optional()
  }).optional()
});

const trainModelSchema = Joi.object({
  modelName: Joi.string().min(2).max(100).required(),
  predictionType: Joi.string().valid(...Object.values(PredictionType)).required(),
  organizationId: Joi.string().uuid().required(),
  trainingData: Joi.object({
    startDate: Joi.string().isoDate().required(),
    endDate: Joi.string().isoDate().required(),
    features: Joi.array().items(Joi.string()).min(1).required(),
    target: Joi.string().required()
  }).required(),
  hyperparameters: Joi.object({
    algorithm: Joi.string().valid('linear_regression', 'random_forest', 'neural_network', 'time_series').default('random_forest'),
    maxDepth: Joi.number().min(1).max(20).optional(),
    nEstimators: Joi.number().min(10).max(1000).optional(),
    learningRate: Joi.number().min(0.001).max(1).optional()
  }).optional()
});

interface GeneratePredictionRequest {
  predictionType: PredictionType;
  organizationId: string;
  timeHorizon?: number;
  parameters?: {
    userId?: string;
    documentId?: string;
    workflowId?: string;
    features?: any;
    filters?: any;
  };
  options?: {
    includeConfidenceInterval?: boolean;
    includeFeatureImportance?: boolean;
    modelVersion?: string;
  };
}

interface PredictionResult {
  id: string;
  predictionType: PredictionType;
  organizationId: string;
  predictions: Array<{
    timestamp: string;
    value: number;
    confidence: number;
    confidenceLevel: ConfidenceLevel;
    confidenceInterval?: {
      lower: number;
      upper: number;
    };
  }>;
  metadata: {
    modelVersion: string;
    modelAccuracy: number;
    featureImportance?: { [feature: string]: number };
    timeHorizon: number;
    generatedAt: string;
    expiresAt: string;
  };
  insights: Array<{
    type: string;
    message: string;
    impact: string;
    confidence: number;
  }>;
}

interface PredictiveModel {
  id: string;
  name: string;
  predictionType: PredictionType;
  status: ModelStatus;
  organizationId: string;
  version: string;
  algorithm: string;
  features: string[];
  target: string;
  performance: {
    accuracy: number;
    precision: number;
    recall: number;
    f1Score: number;
    mse?: number;
    mae?: number;
  };
  trainingData: {
    startDate: string;
    endDate: string;
    recordCount: number;
    features: string[];
  };
  hyperparameters: any;
  createdBy: string;
  createdAt: string;
  lastTrained: string;
  tenantId: string;
}

/**
 * Generate prediction handler
 */
export async function generatePrediction(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Generate prediction started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = generatePredictionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const predictionRequest: GeneratePredictionRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(predictionRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check analytics permissions
    const hasAnalyticsAccess = await checkAnalyticsAccess(user, predictionRequest.organizationId);
    if (!hasAnalyticsAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to predictive analytics" }
      }, request);
    }

    // Get or create model
    const model = await getOrCreateModel(predictionRequest.predictionType, predictionRequest.organizationId);
    if (!model || model.status !== ModelStatus.READY) {
      return addCorsHeaders({
        status: 503,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Prediction model not available", status: model?.status }
      }, request);
    }

    // Generate prediction
    const prediction = await generatePredictionResult(predictionRequest, model, user.id);

    // Store prediction result
    await storePredictionResult(prediction);

    // Update model usage statistics
    await updateModelUsage(model.id);

    const duration = Date.now() - startTime;

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "prediction_generated",
      userId: user.id,
      organizationId: predictionRequest.organizationId,
      timestamp: new Date().toISOString(),
      details: {
        predictionId: prediction.id,
        predictionType: predictionRequest.predictionType,
        timeHorizon: predictionRequest.timeHorizon,
        modelVersion: model.version,
        processingTime: duration
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'PredictionGenerated',
      aggregateId: prediction.id,
      aggregateType: 'Prediction',
      version: 1,
      data: {
        prediction: {
          ...prediction,
          predictions: prediction.predictions.slice(0, 5) // Limit data in event
        },
        generatedBy: user.id
      },
      userId: user.id,
      organizationId: predictionRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Prediction generated successfully", {
      correlationId,
      predictionId: prediction.id,
      predictionType: predictionRequest.predictionType,
      timeHorizon: predictionRequest.timeHorizon,
      modelVersion: model.version,
      duration,
      generatedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        prediction,
        processingTime: duration,
        message: "Prediction generated successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Generate prediction failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Train model handler
 */
export async function trainModel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Train model started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkAnalyticsAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to model training" }
      }, request);
    }

    // Validate request body
    const body = await request.json();
    const { error, value } = trainModelSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const trainingRequest = value;

    // Start model training
    const trainingJob = await startModelTraining(trainingRequest, user.id);

    logger.info("Model training started successfully", {
      correlationId,
      trainingJobId: trainingJob.id,
      modelName: trainingRequest.modelName,
      predictionType: trainingRequest.predictionType,
      startedBy: user.id
    });

    return addCorsHeaders({
      status: 202,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        trainingJobId: trainingJob.id,
        modelName: trainingRequest.modelName,
        predictionType: trainingRequest.predictionType,
        status: 'TRAINING',
        estimatedDuration: trainingJob.estimatedDuration,
        startedAt: trainingJob.startedAt,
        message: "Model training started successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Train model failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkAnalyticsAccess(user: any, organizationId?: string): Promise<boolean> {
  try {
    // Check if user has admin or analytics role
    if (user.roles?.includes('admin') || user.roles?.includes('analytics_admin')) {
      return true;
    }

    // For organization-specific access, check organization membership
    if (organizationId) {
      const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);

      if (memberships.length > 0) {
        const membership = memberships[0] as any;
        return membership.role === 'OWNER' || membership.role === 'ADMIN';
      }
    }

    return false;
  } catch (error) {
    logger.error('Failed to check analytics access', { error, userId: user.id, organizationId });
    return false;
  }
}

async function getOrCreateModel(predictionType: PredictionType, organizationId: string): Promise<PredictiveModel | null> {
  try {
    // Try to find existing model
    const modelQuery = 'SELECT * FROM c WHERE c.predictionType = @type AND c.organizationId = @orgId AND c.status = @status';
    const models = await db.queryItems('predictive-models', modelQuery, [predictionType, organizationId, ModelStatus.READY]);

    if (models.length > 0) {
      return models[0] as PredictiveModel;
    }

    // Create default model if none exists
    return await createDefaultModel(predictionType, organizationId);

  } catch (error) {
    logger.error('Failed to get or create model', { error, predictionType, organizationId });
    return null;
  }
}

async function createDefaultModel(predictionType: PredictionType, organizationId: string): Promise<PredictiveModel> {
  const modelId = uuidv4();
  const now = new Date().toISOString();

  const model: PredictiveModel = {
    id: modelId,
    name: `Default ${predictionType} Model`,
    predictionType,
    status: ModelStatus.READY,
    organizationId,
    version: '1.0.0',
    algorithm: 'random_forest',
    features: getDefaultFeatures(predictionType),
    target: getDefaultTarget(predictionType),
    performance: {
      accuracy: 0.85,
      precision: 0.82,
      recall: 0.88,
      f1Score: 0.85,
      mse: 0.15,
      mae: 0.12
    },
    trainingData: {
      startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
      endDate: now,
      recordCount: 10000,
      features: getDefaultFeatures(predictionType)
    },
    hyperparameters: {
      nEstimators: 100,
      maxDepth: 10,
      minSamplesSplit: 2
    },
    createdBy: 'system',
    createdAt: now,
    lastTrained: now,
    tenantId: organizationId
  };

  await db.createItem('predictive-models', model);
  return model;
}

function getDefaultFeatures(predictionType: PredictionType): string[] {
  const featureMap: { [key in PredictionType]: string[] } = {
    [PredictionType.USER_BEHAVIOR]: ['login_frequency', 'document_views', 'workflow_usage', 'session_duration'],
    [PredictionType.DOCUMENT_USAGE]: ['document_type', 'file_size', 'creation_date', 'last_modified'],
    [PredictionType.WORKFLOW_PERFORMANCE]: ['step_count', 'complexity_score', 'user_count', 'execution_history'],
    [PredictionType.SYSTEM_LOAD]: ['cpu_usage', 'memory_usage', 'request_count', 'response_time'],
    [PredictionType.CHURN_PREDICTION]: ['last_login', 'activity_score', 'feature_usage', 'support_tickets'],
    [PredictionType.GROWTH_FORECAST]: ['user_growth', 'document_growth', 'usage_trends', 'subscription_changes'],
    [PredictionType.ANOMALY_DETECTION]: ['baseline_metrics', 'deviation_score', 'pattern_analysis', 'threshold_breaches']
  };

  return featureMap[predictionType] || ['default_feature'];
}

function getDefaultTarget(predictionType: PredictionType): string {
  const targetMap: { [key in PredictionType]: string } = {
    [PredictionType.USER_BEHAVIOR]: 'user_engagement_score',
    [PredictionType.DOCUMENT_USAGE]: 'document_popularity',
    [PredictionType.WORKFLOW_PERFORMANCE]: 'execution_time',
    [PredictionType.SYSTEM_LOAD]: 'resource_utilization',
    [PredictionType.CHURN_PREDICTION]: 'churn_probability',
    [PredictionType.GROWTH_FORECAST]: 'growth_rate',
    [PredictionType.ANOMALY_DETECTION]: 'anomaly_score'
  };

  return targetMap[predictionType] || 'default_target';
}

async function generatePredictionResult(request: GeneratePredictionRequest, model: PredictiveModel, userId: string): Promise<PredictionResult> {
  const predictionId = uuidv4();
  const now = new Date();
  const timeHorizon = request.timeHorizon || 30;

  // Generate mock predictions (in production, this would use actual ML models)
  const predictions = [];
  const baseValue = Math.random() * 100;

  for (let i = 0; i < timeHorizon; i++) {
    const timestamp = new Date(now.getTime() + i * 24 * 60 * 60 * 1000).toISOString();
    const trend = Math.sin(i * 0.1) * 10;
    const noise = (Math.random() - 0.5) * 5;
    const value = Math.max(0, baseValue + trend + noise);
    const confidence = Math.random() * 0.3 + 0.7; // 0.7 to 1.0

    predictions.push({
      timestamp,
      value: Math.round(value * 100) / 100,
      confidence: Math.round(confidence * 100) / 100,
      confidenceLevel: getConfidenceLevel(confidence),
      confidenceInterval: request.options?.includeConfidenceInterval ? {
        lower: Math.round((value * 0.9) * 100) / 100,
        upper: Math.round((value * 1.1) * 100) / 100
      } : undefined
    });
  }

  // Generate insights
  const insights = generateInsights(request.predictionType, predictions);

  return {
    id: predictionId,
    predictionType: request.predictionType,
    organizationId: request.organizationId,
    predictions,
    metadata: {
      modelVersion: model.version,
      modelAccuracy: model.performance.accuracy,
      featureImportance: request.options?.includeFeatureImportance ? generateFeatureImportance(model.features) : undefined,
      timeHorizon,
      generatedAt: now.toISOString(),
      expiresAt: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
    },
    insights
  };
}

function getConfidenceLevel(confidence: number): ConfidenceLevel {
  if (confidence >= 0.9) return ConfidenceLevel.VERY_HIGH;
  if (confidence >= 0.8) return ConfidenceLevel.HIGH;
  if (confidence >= 0.6) return ConfidenceLevel.MEDIUM;
  return ConfidenceLevel.LOW;
}

function generateInsights(predictionType: PredictionType, predictions: any[]): any[] {
  const insights = [];
  const trend = predictions[predictions.length - 1].value - predictions[0].value;
  const avgConfidence = predictions.reduce((sum, p) => sum + p.confidence, 0) / predictions.length;

  if (trend > 0) {
    insights.push({
      type: 'trend',
      message: `Positive trend detected with ${Math.round(trend * 100) / 100} increase over the forecast period`,
      impact: 'positive',
      confidence: avgConfidence
    });
  } else if (trend < 0) {
    insights.push({
      type: 'trend',
      message: `Negative trend detected with ${Math.round(Math.abs(trend) * 100) / 100} decrease over the forecast period`,
      impact: 'negative',
      confidence: avgConfidence
    });
  }

  if (avgConfidence > 0.8) {
    insights.push({
      type: 'confidence',
      message: 'High confidence prediction based on strong historical patterns',
      impact: 'informational',
      confidence: avgConfidence
    });
  }

  return insights;
}

function generateFeatureImportance(features: string[]): { [feature: string]: number } {
  const importance: { [feature: string]: number } = {};
  let remaining = 1.0;

  for (let i = 0; i < features.length; i++) {
    if (i === features.length - 1) {
      importance[features[i]] = remaining;
    } else {
      const value = Math.random() * remaining * 0.5;
      importance[features[i]] = Math.round(value * 100) / 100;
      remaining -= value;
    }
  }

  return importance;
}

async function storePredictionResult(prediction: PredictionResult): Promise<void> {
  try {
    await db.createItem('predictions', prediction);

    // Cache prediction for quick access
    const cacheKey = `prediction:${prediction.id}`;
    await redis.setex(cacheKey, 86400, JSON.stringify(prediction)); // 24 hours

  } catch (error) {
    logger.error('Failed to store prediction result', { error, predictionId: prediction.id });
  }
}

async function updateModelUsage(modelId: string): Promise<void> {
  try {
    const usageKey = `model_usage:${modelId}`;
    await redis.hincrby(usageKey, 'prediction_count', 1);
    await redis.hset(usageKey, 'last_used', new Date().toISOString());
    await redis.expire(usageKey, 86400 * 30); // 30 days

  } catch (error) {
    logger.error('Failed to update model usage', { error, modelId });
  }
}

async function startModelTraining(trainingRequest: any, userId: string): Promise<any> {
  try {
    const trainingJobId = uuidv4();
    const now = new Date().toISOString();

    const trainingJob = {
      id: trainingJobId,
      modelName: trainingRequest.modelName,
      predictionType: trainingRequest.predictionType,
      organizationId: trainingRequest.organizationId,
      status: 'TRAINING',
      startedAt: now,
      estimatedDuration: '30 minutes',
      startedBy: userId
    };

    // In production, this would start actual model training
    logger.info('Model training job started', trainingJob);

    return trainingJob;

  } catch (error) {
    logger.error('Failed to start model training', { error, trainingRequest });
    throw error;
  }
}

// Register functions
app.http('prediction-generate', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'analytics/predictions',
  handler: generatePrediction
});

app.http('model-train', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'management/analytics/models/train',
  handler: trainModel
});
