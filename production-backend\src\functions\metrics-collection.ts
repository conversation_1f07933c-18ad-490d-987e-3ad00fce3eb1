/**
 * Metrics Collection Function
 * Handles comprehensive metrics collection and aggregation
 * Migrated from old-arch/src/monitoring-service/metrics/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';

// Metrics types and enums
enum MetricCategory {
  PERFORMANCE = 'PERFORMANCE',
  BUSINESS = 'BUSINESS',
  SYSTEM = 'SYSTEM',
  USER = 'USER',
  SECURITY = 'SECURITY',
  CUSTOM = 'CUSTOM'
}

enum AggregationType {
  SUM = 'SUM',
  AVERAGE = 'AVERAGE',
  COUNT = 'COUNT',
  MIN = 'MIN',
  MAX = 'MAX',
  PERCENTILE = 'PERCENTILE'
}

// Validation schemas
const collectMetricsSchema = Joi.object({
  metrics: Joi.array().items(Joi.object({
    name: Joi.string().min(2).max(100).required(),
    value: Joi.number().required(),
    category: Joi.string().valid(...Object.values(MetricCategory)).required(),
    unit: Joi.string().max(20).optional(),
    tags: Joi.object().optional(),
    timestamp: Joi.string().isoDate().optional()
  })).min(1).max(100).required(),
  organizationId: Joi.string().uuid().optional(),
  userId: Joi.string().uuid().optional(),
  source: Joi.string().max(100).required(),
  metadata: Joi.object().optional()
});

const queryMetricsSchema = Joi.object({
  metricNames: Joi.array().items(Joi.string()).optional(),
  category: Joi.string().valid(...Object.values(MetricCategory)).optional(),
  organizationId: Joi.string().uuid().optional(),
  userId: Joi.string().uuid().optional(),
  timeRange: Joi.object({
    startTime: Joi.string().isoDate().required(),
    endTime: Joi.string().isoDate().required()
  }).required(),
  aggregation: Joi.string().valid(...Object.values(AggregationType)).default(AggregationType.AVERAGE),
  interval: Joi.string().valid('1m', '5m', '15m', '1h', '1d').default('1h'),
  tags: Joi.object().optional(),
  limit: Joi.number().min(1).max(10000).default(1000)
});

interface MetricPoint {
  name: string;
  value: number;
  category: MetricCategory;
  unit?: string;
  tags: { [key: string]: string };
  timestamp: string;
}

interface CollectMetricsRequest {
  metrics: MetricPoint[];
  organizationId?: string;
  userId?: string;
  source: string;
  metadata?: any;
}

interface MetricSeries {
  name: string;
  category: MetricCategory;
  unit?: string;
  dataPoints: Array<{
    timestamp: string;
    value: number;
    tags?: { [key: string]: string };
  }>;
  aggregation: AggregationType;
  interval: string;
  statistics: {
    count: number;
    sum: number;
    average: number;
    min: number;
    max: number;
  };
}

/**
 * Collect metrics handler
 */
export async function collectMetrics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Collect metrics started", { correlationId });

  try {
    // Validate request body
    const body = await request.json();
    const { error, value } = collectMetricsSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const metricsRequest: CollectMetricsRequest = value;

    // Store metrics
    const metricIds = await storeMetrics(metricsRequest);

    // Update real-time aggregations
    await updateRealTimeAggregations(metricsRequest.metrics);

    // Update metric statistics
    await updateMetricStatistics(metricsRequest.metrics);

    const duration = Date.now() - startTime;

    logger.info("Metrics collected successfully", {
      correlationId,
      metricCount: metricsRequest.metrics.length,
      source: metricsRequest.source,
      organizationId: metricsRequest.organizationId,
      duration
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        metricIds,
        metricCount: metricsRequest.metrics.length,
        source: metricsRequest.source,
        collectedAt: new Date().toISOString(),
        processingTime: duration,
        message: "Metrics collected successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Collect metrics failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Query metrics handler
 */
export async function queryMetrics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const startTime = Date.now();
  logger.info("Query metrics started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Parse query parameters
    const url = new URL(request.url);
    const queryParams = {
      metricNames: url.searchParams.get('metricNames')?.split(','),
      category: url.searchParams.get('category') as MetricCategory,
      organizationId: url.searchParams.get('organizationId'),
      userId: url.searchParams.get('userId'),
      timeRange: {
        startTime: url.searchParams.get('startTime') || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        endTime: url.searchParams.get('endTime') || new Date().toISOString()
      },
      aggregation: url.searchParams.get('aggregation') as AggregationType || AggregationType.AVERAGE,
      interval: url.searchParams.get('interval') || '1h',
      limit: parseInt(url.searchParams.get('limit') || '1000')
    };

    // Validate query parameters
    const { error, value } = queryMetricsSchema.validate(queryParams);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const queryRequest = value;

    // Check access permissions
    const hasAccess = await checkMetricsAccess(user, queryRequest.organizationId, queryRequest.userId);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to metrics" }
      }, request);
    }

    // Query metrics
    const metricSeries = await executeMetricsQuery(queryRequest);

    const duration = Date.now() - startTime;

    logger.info("Metrics queried successfully", {
      correlationId,
      seriesCount: metricSeries.length,
      timeRange: queryRequest.timeRange,
      aggregation: queryRequest.aggregation,
      duration,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        series: metricSeries,
        query: {
          timeRange: queryRequest.timeRange,
          aggregation: queryRequest.aggregation,
          interval: queryRequest.interval
        },
        metadata: {
          seriesCount: metricSeries.length,
          totalDataPoints: metricSeries.reduce((sum, series) => sum + series.dataPoints.length, 0),
          queryTime: duration
        },
        queriedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Query metrics failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get metrics summary handler
 */
export async function getMetricsSummary(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Get metrics summary started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Check admin access
    const hasAccess = await checkMetricsAccess(user);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to metrics summary" }
      }, request);
    }

    // Get metrics summary
    const summary = await getSystemMetricsSummary();

    logger.info("Metrics summary retrieved successfully", {
      correlationId,
      requestedBy: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        summary,
        generatedAt: new Date().toISOString()
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get metrics summary failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkMetricsAccess(user: any, organizationId?: string, userId?: string): Promise<boolean> {
  try {
    // Check if user has admin or metrics role
    if (user.roles?.includes('admin') || user.roles?.includes('metrics_admin')) {
      return true;
    }

    // For organization metrics, check organization access
    if (organizationId) {
      const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
      return memberships.length > 0;
    }

    // For user metrics, check if it's the same user
    if (userId) {
      return user.id === userId;
    }

    return false;
  } catch (error) {
    logger.error('Failed to check metrics access', { error, userId: user.id, organizationId, targetUserId: userId });
    return false;
  }
}

async function storeMetrics(metricsRequest: CollectMetricsRequest): Promise<string[]> {
  try {
    const metricIds: string[] = [];
    const batchSize = 25; // Cosmos DB batch limit

    // Process metrics in batches
    for (let i = 0; i < metricsRequest.metrics.length; i += batchSize) {
      const batch = metricsRequest.metrics.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (metric) => {
        const metricId = uuidv4();
        const timestamp = metric.timestamp || new Date().toISOString();

        const metricRecord = {
          id: metricId,
          name: metric.name,
          value: metric.value,
          category: metric.category,
          unit: metric.unit,
          tags: metric.tags || {},
          timestamp,
          organizationId: metricsRequest.organizationId,
          userId: metricsRequest.userId,
          source: metricsRequest.source,
          metadata: metricsRequest.metadata || {},
          tenantId: metricsRequest.organizationId || 'system'
        };

        await db.createItem('metrics', metricRecord);
        return metricId;
      });

      const batchIds = await Promise.all(batchPromises);
      metricIds.push(...batchIds);
    }

    return metricIds;

  } catch (error) {
    logger.error('Failed to store metrics', { error, metricCount: metricsRequest.metrics.length });
    throw error;
  }
}

async function updateRealTimeAggregations(metrics: MetricPoint[]): Promise<void> {
  try {
    const now = new Date();
    const currentMinute = Math.floor(now.getTime() / 60000) * 60000;
    const currentHour = Math.floor(now.getTime() / 3600000) * 3600000;

    for (const metric of metrics) {
      // Update minute-level aggregations
      const minuteKey = `metrics:agg:${metric.name}:1m:${currentMinute}`;
      await redis.hincrby(minuteKey, 'count', 1);
      await redis.hincrbyfloat(minuteKey, 'sum', metric.value);
      await redis.hset(minuteKey, 'last_value', metric.value.toString());
      await redis.expire(minuteKey, 3600); // 1 hour

      // Update hour-level aggregations
      const hourKey = `metrics:agg:${metric.name}:1h:${currentHour}`;
      await redis.hincrby(hourKey, 'count', 1);
      await redis.hincrbyfloat(hourKey, 'sum', metric.value);
      await redis.hset(hourKey, 'last_value', metric.value.toString());
      await redis.expire(hourKey, 86400); // 24 hours

      // Update min/max values
      const currentMin = await redis.hget(minuteKey, 'min');
      const currentMax = await redis.hget(minuteKey, 'max');
      
      if (!currentMin || metric.value < parseFloat(currentMin)) {
        await redis.hset(minuteKey, 'min', metric.value.toString());
      }
      if (!currentMax || metric.value > parseFloat(currentMax)) {
        await redis.hset(minuteKey, 'max', metric.value.toString());
      }
    }

  } catch (error) {
    logger.error('Failed to update real-time aggregations', { error });
  }
}

async function updateMetricStatistics(metrics: MetricPoint[]): Promise<void> {
  try {
    for (const metric of metrics) {
      const statsKey = `metrics:stats:${metric.name}`;
      
      await redis.hincrby(statsKey, 'total_count', 1);
      await redis.hincrbyfloat(statsKey, 'total_sum', metric.value);
      await redis.hset(statsKey, 'last_value', metric.value.toString());
      await redis.hset(statsKey, 'last_updated', new Date().toISOString());
      await redis.expire(statsKey, 86400 * 30); // 30 days

      // Update category statistics
      const categoryStatsKey = `metrics:category_stats:${metric.category}`;
      await redis.hincrby(categoryStatsKey, 'count', 1);
      await redis.hset(categoryStatsKey, 'last_updated', new Date().toISOString());
      await redis.expire(categoryStatsKey, 86400 * 7); // 7 days
    }

  } catch (error) {
    logger.error('Failed to update metric statistics', { error });
  }
}

async function executeMetricsQuery(queryRequest: any): Promise<MetricSeries[]> {
  try {
    // Build query
    let query = 'SELECT * FROM c WHERE c.timestamp >= @startTime AND c.timestamp <= @endTime';
    const parameters = [queryRequest.timeRange.startTime, queryRequest.timeRange.endTime];

    if (queryRequest.metricNames && queryRequest.metricNames.length > 0) {
      query += ' AND c.name IN (@metricNames)';
      parameters.push(queryRequest.metricNames);
    }

    if (queryRequest.category) {
      query += ' AND c.category = @category';
      parameters.push(queryRequest.category);
    }

    if (queryRequest.organizationId) {
      query += ' AND c.organizationId = @orgId';
      parameters.push(queryRequest.organizationId);
    }

    if (queryRequest.userId) {
      query += ' AND c.userId = @userId';
      parameters.push(queryRequest.userId);
    }

    query += ' ORDER BY c.timestamp ASC';

    // Execute query
    const metrics = await db.queryItems('metrics', query, parameters);

    // Group by metric name and aggregate
    const groupedMetrics = groupMetricsByName(metrics);
    const aggregatedSeries = aggregateMetrics(groupedMetrics, queryRequest.aggregation, queryRequest.interval);

    return aggregatedSeries;

  } catch (error) {
    logger.error('Failed to execute metrics query', { error, queryRequest });
    return [];
  }
}

function groupMetricsByName(metrics: any[]): { [metricName: string]: any[] } {
  const grouped: { [metricName: string]: any[] } = {};
  
  for (const metric of metrics) {
    if (!grouped[metric.name]) {
      grouped[metric.name] = [];
    }
    grouped[metric.name].push(metric);
  }
  
  return grouped;
}

function aggregateMetrics(groupedMetrics: { [metricName: string]: any[] }, aggregation: AggregationType, interval: string): MetricSeries[] {
  const series: MetricSeries[] = [];
  
  for (const [metricName, metrics] of Object.entries(groupedMetrics)) {
    if (metrics.length === 0) continue;
    
    const firstMetric = metrics[0];
    const intervalMs = parseInterval(interval);
    const buckets: { [timestamp: string]: any[] } = {};
    
    // Group metrics into time buckets
    for (const metric of metrics) {
      const timestamp = new Date(metric.timestamp);
      const bucketTime = Math.floor(timestamp.getTime() / intervalMs) * intervalMs;
      const bucketKey = new Date(bucketTime).toISOString();
      
      if (!buckets[bucketKey]) {
        buckets[bucketKey] = [];
      }
      buckets[bucketKey].push(metric);
    }
    
    // Aggregate each bucket
    const dataPoints = [];
    let sum = 0;
    let count = 0;
    let min = Number.MAX_VALUE;
    let max = Number.MIN_VALUE;
    
    for (const [timestamp, bucketMetrics] of Object.entries(buckets)) {
      const values = bucketMetrics.map(m => m.value);
      let aggregatedValue: number;
      
      switch (aggregation) {
        case AggregationType.SUM:
          aggregatedValue = values.reduce((sum, val) => sum + val, 0);
          break;
        case AggregationType.AVERAGE:
          aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
          break;
        case AggregationType.COUNT:
          aggregatedValue = values.length;
          break;
        case AggregationType.MIN:
          aggregatedValue = Math.min(...values);
          break;
        case AggregationType.MAX:
          aggregatedValue = Math.max(...values);
          break;
        default:
          aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
      }
      
      dataPoints.push({
        timestamp,
        value: aggregatedValue,
        tags: bucketMetrics[0].tags
      });
      
      // Update overall statistics
      sum += aggregatedValue;
      count++;
      min = Math.min(min, aggregatedValue);
      max = Math.max(max, aggregatedValue);
    }
    
    series.push({
      name: metricName,
      category: firstMetric.category,
      unit: firstMetric.unit,
      dataPoints: dataPoints.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()),
      aggregation,
      interval,
      statistics: {
        count,
        sum,
        average: count > 0 ? sum / count : 0,
        min: min === Number.MAX_VALUE ? 0 : min,
        max: max === Number.MIN_VALUE ? 0 : max
      }
    });
  }
  
  return series;
}

function parseInterval(interval: string): number {
  const intervalMap: { [key: string]: number } = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000
  };
  
  return intervalMap[interval] || intervalMap['1h'];
}

async function getSystemMetricsSummary(): Promise<any> {
  try {
    // Get summary statistics from Redis
    const categories = Object.values(MetricCategory);
    const summary: any = {
      totalMetrics: 0,
      categorySummary: {},
      topMetrics: [],
      recentActivity: {}
    };

    for (const category of categories) {
      const categoryStatsKey = `metrics:category_stats:${category}`;
      const categoryStats = await redis.hgetall(categoryStatsKey);
      
      summary.categorySummary[category] = {
        count: parseInt(categoryStats.count || '0'),
        lastUpdated: categoryStats.last_updated
      };
      
      summary.totalMetrics += parseInt(categoryStats.count || '0');
    }

    // Get top metrics by activity
    const metricKeys = await redis.keys('metrics:stats:*');
    const topMetrics = [];
    
    for (const key of metricKeys.slice(0, 10)) {
      const stats = await redis.hgetall(key);
      const metricName = key.replace('metrics:stats:', '');
      
      topMetrics.push({
        name: metricName,
        count: parseInt(stats.total_count || '0'),
        lastValue: parseFloat(stats.last_value || '0'),
        lastUpdated: stats.last_updated
      });
    }
    
    summary.topMetrics = topMetrics.sort((a, b) => b.count - a.count).slice(0, 10);

    return summary;

  } catch (error) {
    logger.error('Failed to get system metrics summary', { error });
    return {
      totalMetrics: 0,
      categorySummary: {},
      topMetrics: [],
      recentActivity: {}
    };
  }
}

// Register functions
app.http('metrics-collect', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'metrics/collect',
  handler: collectMetrics
});

app.http('metrics-query', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'metrics/query',
  handler: queryMetrics
});

app.http('metrics-summary', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'metrics/summary',
  handler: getMetricsSummary
});
