/**
 * Manual Event Grid Test Script
 * Simple test to verify Event Grid configuration and functionality
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Load configuration from local.settings.json
const localSettingsPath = path.join(__dirname, '..', 'local.settings.json');
let config = {};

try {
  const localSettings = JSON.parse(fs.readFileSync(localSettingsPath, 'utf8'));
  config = {
    eventGridEndpoint: localSettings.Values.EVENT_GRID_TOPIC_ENDPOINT,
    eventGridKey: localSettings.Values.EVENT_GRID_TOPIC_KEY,
    functionAppUrl: 'https://localhost:7071'
  };
} catch (error) {
  console.error('❌ Failed to load configuration:', error.message);
  process.exit(1);
}

console.log('🧪 Manual Event Grid Test');
console.log('========================');
console.log(`Event Grid Endpoint: ${config.eventGridEndpoint}`);
console.log(`Function App URL: ${config.functionAppUrl}`);
console.log('');

/**
 * Test 1: Verify Event Grid endpoint is accessible
 */
async function testEventGridEndpoint() {
  console.log('🔍 Test 1: Verifying Event Grid endpoint...');
  
  if (!config.eventGridEndpoint || !config.eventGridKey) {
    console.log('❌ Event Grid configuration missing');
    return false;
  }

  try {
    const url = new URL(config.eventGridEndpoint);
    console.log(`✅ Event Grid endpoint configured: ${url.hostname}`);
    return true;
  } catch (error) {
    console.log('❌ Invalid Event Grid endpoint URL');
    return false;
  }
}

/**
 * Test 2: Test webhook endpoint (if function app is running)
 */
async function testWebhookEndpoint() {
  console.log('🔍 Test 2: Testing webhook endpoint...');
  
  const webhookUrl = `${config.functionAppUrl}/api/eventgrid/webhook`;
  
  // Create a validation event
  const validationEvent = {
    id: `validation-${Date.now()}`,
    eventType: 'Microsoft.EventGrid.SubscriptionValidationEvent',
    subject: '',
    eventTime: new Date().toISOString(),
    data: {
      validationCode: 'test-validation-code-12345'
    },
    dataVersion: '1.0'
  };

  const postData = JSON.stringify([validationEvent]);

  const options = {
    hostname: 'localhost',
    port: 7071,
    path: '/api/eventgrid/webhook',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'aeg-event-type': 'SubscriptionValidation'
    },
    timeout: 5000
  };

  return new Promise((resolve) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ Webhook endpoint is accessible');
          console.log(`   Response: ${data}`);
          resolve(true);
        } else {
          console.log(`⚠️ Webhook endpoint returned status: ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log('⚠️ Webhook endpoint not accessible (function app may not be running)');
      console.log(`   Error: ${error.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('⚠️ Webhook endpoint timeout (function app may not be running)');
      req.destroy();
      resolve(false);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * Test 3: Verify Event Grid handlers are properly registered
 */
async function testEventHandlers() {
  console.log('🔍 Test 3: Verifying Event Grid handlers...');
  
  const handlersFile = path.join(__dirname, '..', 'src', 'functions', 'event-grid-handlers.ts');
  
  try {
    const content = fs.readFileSync(handlersFile, 'utf8');
    
    // Check for key components
    const checks = [
      { name: 'Event Grid imports', pattern: /import.*EventGrid/i },
      { name: 'Event types enum', pattern: /enum EventType/i },
      { name: 'Publish function', pattern: /publishEvent/i },
      { name: 'Webhook handler', pattern: /handleEventGridWebhook/i },
      { name: 'HTTP registration', pattern: /app\.http.*event-grid-webhook/i },
      { name: 'Native triggers', pattern: /app\.eventGrid/i }
    ];

    let allPassed = true;
    for (const check of checks) {
      if (check.pattern.test(content)) {
        console.log(`   ✅ ${check.name} found`);
      } else {
        console.log(`   ❌ ${check.name} missing`);
        allPassed = false;
      }
    }

    return allPassed;
  } catch (error) {
    console.log('❌ Event Grid handlers file not found');
    return false;
  }
}

/**
 * Test 4: Verify Event Grid integration service
 */
async function testIntegrationService() {
  console.log('🔍 Test 4: Verifying Event Grid integration service...');
  
  const serviceFile = path.join(__dirname, '..', 'src', 'shared', 'services', 'event-grid-integration.ts');
  
  try {
    const content = fs.readFileSync(serviceFile, 'utf8');
    
    const checks = [
      { name: 'EventGridIntegrationService class', pattern: /class EventGridIntegrationService/i },
      { name: 'Publish event method', pattern: /publishEvent.*async/i },
      { name: 'Batch publishing', pattern: /publishEvents.*async/i },
      { name: 'Health check', pattern: /healthCheck.*async/i },
      { name: 'Metrics tracking', pattern: /metrics/i }
    ];

    let allPassed = true;
    for (const check of checks) {
      if (check.pattern.test(content)) {
        console.log(`   ✅ ${check.name} found`);
      } else {
        console.log(`   ❌ ${check.name} missing`);
        allPassed = false;
      }
    }

    return allPassed;
  } catch (error) {
    console.log('❌ Event Grid integration service file not found');
    return false;
  }
}

/**
 * Test 5: Check configuration completeness
 */
async function testConfiguration() {
  console.log('🔍 Test 5: Checking configuration completeness...');
  
  const requiredConfig = [
    'EVENT_GRID_TOPIC_ENDPOINT',
    'EVENT_GRID_TOPIC_KEY',
    'EVENT_GRID_TOPIC_NAME',
    'EVENT_GRID_RETRY_ATTEMPTS',
    'EVENT_GRID_TIMEOUT_MS'
  ];

  let allConfigured = true;
  
  try {
    const localSettings = JSON.parse(fs.readFileSync(localSettingsPath, 'utf8'));
    
    for (const configKey of requiredConfig) {
      if (localSettings.Values[configKey]) {
        console.log(`   ✅ ${configKey} configured`);
      } else {
        console.log(`   ❌ ${configKey} missing`);
        allConfigured = false;
      }
    }
  } catch (error) {
    console.log('❌ Failed to read configuration');
    return false;
  }

  return allConfigured;
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Event Grid tests...\n');

  const results = {
    endpoint: await testEventGridEndpoint(),
    webhook: await testWebhookEndpoint(),
    handlers: await testEventHandlers(),
    service: await testIntegrationService(),
    config: await testConfiguration()
  };

  console.log('\n📋 Test Results Summary:');
  console.log('========================');
  console.log(`Event Grid Endpoint: ${results.endpoint ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Webhook Endpoint: ${results.webhook ? '✅ PASS' : '⚠️ SKIP (Function app not running)'}`);
  console.log(`Event Handlers: ${results.handlers ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Integration Service: ${results.service ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Configuration: ${results.config ? '✅ PASS' : '❌ FAIL'}`);

  const passCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);

  if (passCount >= 4) { // Allow webhook test to fail if function app not running
    console.log('🎉 Event Grid configuration is ready!');
    console.log('\n📝 Next Steps:');
    console.log('1. Start your Azure Functions: func start');
    console.log('2. Deploy to Azure for full Event Grid integration');
    console.log('3. Set up Event Grid subscriptions in Azure Portal');
    console.log('4. Test with real events');
  } else {
    console.log('⚠️ Some Event Grid components need attention.');
    console.log('Please review the failed tests and fix any issues.');
  }
}

// Run tests
runTests().catch(console.error);
