# LemonSqueezy Webhook Integration

## Overview

This document describes the refactored LemonSqueezy webhook integration that handles all LemonSqueezy events with proper signature verification, database storage, and event publishing.

## Features

### ✅ Complete Event Coverage
- **Order Events**: `order_created`, `order_refunded`
- **Subscription Events**: `subscription_created`, `subscription_updated`, `subscription_cancelled`, `subscription_resumed`, `subscription_expired`, `subscription_paused`, `subscription_unpaused`
- **Payment Events**: `subscription_payment_success`, `subscription_payment_failed`, `subscription_payment_recovered`, `subscription_payment_refunded`
- **License Events**: `license_key_created`, `license_key_updated`
- **Affiliate Events**: `affiliate_activated`

### ✅ Security Features
- **HMAC-SHA256 signature verification** using timing-safe comparison
- **Idempotency protection** to prevent duplicate processing
- **Comprehensive input validation** and sanitization
- **Correlation ID tracking** for audit trails

### ✅ Database Integration
- **Automatic data storage** for orders, subscriptions, invoices, license keys, and affiliates
- **User subscription status updates** linked to email addresses
- **Audit trail** with webhook event logging
- **Safe update operations** with error handling

### ✅ Event Publishing
- **Internal Event Grid integration** for system-wide notifications
- **Structured event data** for downstream processing
- **Correlation tracking** across services

## Configuration

### Environment Variables

```json
{
  "LEMONSQUEEZY_API_KEY": "your-api-key",
  "LEMONSQUEEZY_STORE_ID": "your-store-id",
  "LEMONSQUEEZY_WEBHOOK_SECRET": "your-webhook-secret",
  "LEMONSQUEEZY_WEBHOOK_URL": "https://your-domain.com/api/lemonsqueezy-webhooks"
}
```

### Database Collections

The webhook handler creates and manages these database collections:

- **`orders`**: Order information and payment details
- **`subscriptions`**: Subscription lifecycle and billing data
- **`subscription-invoices`**: Payment and billing records
- **`license-keys`**: Software license management
- **`affiliates`**: Affiliate program data
- **`lemonsqueezy-webhook-events`**: Audit trail and idempotency
- **`users`**: User subscription status updates

## API Endpoint

### POST `/api/lemonsqueezy-webhooks`

**Headers Required:**
- `Content-Type: application/json`
- `X-Signature: <hmac-sha256-signature>`

**Response Codes:**
- `200`: Webhook processed successfully
- `400`: Invalid payload or missing signature
- `401`: Invalid signature
- `405`: Method not allowed
- `500`: Internal server error

## Event Processing

### Order Events

#### Order Created
```json
{
  "meta": {
    "event_name": "order_created",
    "test_mode": false,
    "webhook_id": "webhook-123"
  },
  "data": {
    "type": "orders",
    "id": "order-123",
    "attributes": {
      "user_email": "<EMAIL>",
      "total": 2999,
      "currency": "USD",
      "status": "paid"
    }
  }
}
```

**Actions:**
- Store order in database
- Update user subscription status to "active"
- Publish internal event

#### Order Refunded
**Actions:**
- Update order refund status
- Update user subscription status to "cancelled"
- Publish internal event

### Subscription Events

#### Subscription Created
**Actions:**
- Store subscription details
- Update user subscription status
- Set up billing information

#### Subscription Status Changes
- **Cancelled**: Set end date, update user status
- **Resumed**: Reactivate subscription
- **Expired**: Mark as expired
- **Paused/Unpaused**: Update status accordingly

### Payment Events

#### Payment Success
**Actions:**
- Store invoice record
- Confirm subscription active status

#### Payment Failed
**Actions:**
- Store failed payment record
- Update subscription to "past_due"

#### Payment Recovered
**Actions:**
- Store recovery record
- Reactivate subscription

## Testing

### Manual Testing

Run the test script to verify webhook functionality:

```bash
node scripts/test-lemonsqueezy-webhook.js
```

### Test Cases Covered

1. **Valid Order Created Event**
2. **Valid Subscription Created Event**
3. **Invalid Signature Rejection**
4. **Duplicate Event Handling**
5. **Malformed Payload Handling**

### LemonSqueezy Dashboard Testing

1. Go to LemonSqueezy Dashboard → Settings → Webhooks
2. Add webhook URL: `https://your-domain.com/api/lemonsqueezy-webhooks`
3. Select events to monitor
4. Test with sample events

## Monitoring and Logging

### Log Levels

- **INFO**: Successful webhook processing
- **WARN**: Non-critical issues (duplicate events, missing users)
- **ERROR**: Processing failures, signature validation errors

### Correlation Tracking

Each webhook request gets a unique correlation ID for tracking across:
- Database operations
- Event publishing
- Error logging
- Audit trails

### Metrics

Monitor these key metrics:
- Webhook processing success rate
- Signature validation failures
- Database operation latencies
- Event publishing success rate

## Error Handling

### Retry Logic

- **Signature failures**: No retry (security)
- **Database failures**: Logged but processing continues
- **Event publishing failures**: Logged but processing continues
- **Validation failures**: Return 400 error

### Idempotency

Duplicate webhooks are detected using:
```
eventId = webhookId + dataId + eventName
```

Duplicate events return `200 OK` without reprocessing.

## Security Considerations

1. **Signature Verification**: Always verify HMAC-SHA256 signatures
2. **Timing-Safe Comparison**: Prevent timing attacks
3. **Input Validation**: Validate all incoming data
4. **Audit Logging**: Track all webhook events
5. **Error Information**: Don't leak sensitive data in error responses

## Troubleshooting

### Common Issues

1. **Invalid Signature**
   - Check webhook secret configuration
   - Verify payload hasn't been modified

2. **Database Errors**
   - Check database connectivity
   - Verify container names and permissions

3. **Missing Events**
   - Check LemonSqueezy webhook configuration
   - Verify endpoint URL accessibility

### Debug Mode

Enable detailed logging by setting:
```json
{
  "LOG_LEVEL": "debug"
}
```

## Integration Examples

### Frontend Integration

```javascript
// Check user subscription status
const response = await fetch('/api/users/subscription-status');
const { subscriptionStatus } = await response.json();

if (subscriptionStatus === 'active') {
  // Show premium features
}
```

### Backend Integration

```javascript
// Listen for subscription events
app.on('subscription_updated', (event) => {
  const { userId, status } = event.data;
  updateUserPermissions(userId, status);
});
```
