/**
 * AI Smart Form Processing Function
 * Handles intelligent form processing and field extraction
 * Migrated from old-arch/src/ai-service/smart-form-processing/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { DocumentAnalysisClient, AzureKeyCredential } from '@azure/ai-form-recognizer';
import { v4 as uuidv4 } from "uuid";
import Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { eventService } from '../shared/services/event';

// Form processing types and enums
enum FormType {
  INVOICE = 'INVOICE',
  RECEIPT = 'RECEIPT',
  CONTRACT = 'CONTRACT',
  APPLICATION = 'APPLICATION',
  SURVEY = 'SURVEY',
  TAX_FORM = 'TAX_FORM',
  INSURANCE_CLAIM = 'INSURANCE_CLAIM',
  MEDICAL_FORM = 'MEDICAL_FORM',
  CUSTOM = 'CUSTOM'
}

enum FieldType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  ADDRESS = 'ADDRESS',
  CURRENCY = 'CURRENCY',
  PERCENTAGE = 'PERCENTAGE',
  SIGNATURE = 'SIGNATURE'
}

enum ProcessingStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

// Validation schemas
const processFormSchema = Joi.object({
  documentId: Joi.string().uuid().optional(),
  documentUrl: Joi.string().uri().optional(),
  documentContent: Joi.string().base64().optional(),
  formType: Joi.string().valid(...Object.values(FormType)).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  configuration: Joi.object({
    extractTables: Joi.boolean().default(true),
    extractSignatures: Joi.boolean().default(true),
    validateFields: Joi.boolean().default(true),
    confidenceThreshold: Joi.number().min(0).max(1).default(0.7),
    customFields: Joi.array().items(Joi.object({
      name: Joi.string().required(),
      type: Joi.string().valid(...Object.values(FieldType)).required(),
      required: Joi.boolean().default(false),
      pattern: Joi.string().optional(),
      description: Joi.string().optional()
    })).optional(),
    templateId: Joi.string().uuid().optional()
  }).optional(),
  metadata: Joi.object().optional()
}).xor('documentId', 'documentUrl', 'documentContent');

interface ProcessFormRequest {
  documentId?: string;
  documentUrl?: string;
  documentContent?: string;
  formType: FormType;
  organizationId: string;
  projectId?: string;
  configuration?: {
    extractTables?: boolean;
    extractSignatures?: boolean;
    validateFields?: boolean;
    confidenceThreshold?: number;
    customFields?: Array<{
      name: string;
      type: FieldType;
      required?: boolean;
      pattern?: string;
      description?: string;
    }>;
    templateId?: string;
  };
  metadata?: any;
}

interface ExtractedField {
  name: string;
  value: any;
  type: FieldType;
  confidence: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  page?: number;
  validated?: boolean;
  validationErrors?: string[];
}

interface FormProcessingResponse {
  processingId: string;
  documentId?: string;
  formType: FormType;
  status: ProcessingStatus;
  extractedFields: ExtractedField[];
  tables?: Array<{
    headers: string[];
    rows: string[][];
    confidence: number;
    page: number;
  }>;
  signatures?: Array<{
    type: 'signature' | 'initial';
    confidence: number;
    boundingBox: any;
    page: number;
  }>;
  validation: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    completeness: number;
  };
  metadata: {
    processingTime: number;
    modelUsed: string;
    confidence: number;
    pageCount: number;
  };
}

/**
 * Process form handler
 */
export async function processForm(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Process form started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = processFormSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const formRequest: ProcessFormRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(formRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check form processing limits
    const canProcess = await checkFormProcessingLimits(formRequest.organizationId);
    if (!canProcess.allowed) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: canProcess.reason }
      }, request);
    }

    // Get document content
    let documentBuffer: Buffer;
    let documentMetadata: any = {};

    if (formRequest.documentId) {
      const document = await db.readItem('documents', formRequest.documentId, formRequest.documentId);
      if (!document) {
        return addCorsHeaders({
          status: 404,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Document not found" }
        }, request);
      }

      const documentData = document as any;
      documentMetadata = {
        name: documentData.name,
        contentType: documentData.contentType,
        size: documentData.size
      };

      // Download document from blob storage
      const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
      const containerClient = blobServiceClient.getContainerClient("documents");
      const blobClient = containerClient.getBlobClient(`${formRequest.documentId}/content`);

      const downloadResponse = await blobClient.download();
      const chunks: Buffer[] = [];

      if (downloadResponse.readableStreamBody) {
        for await (const chunk of downloadResponse.readableStreamBody) {
          chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
        }
      }

      documentBuffer = Buffer.concat(chunks);
    } else if (formRequest.documentUrl) {
      const response = await fetch(formRequest.documentUrl);
      const arrayBuffer = await response.arrayBuffer();
      documentBuffer = Buffer.from(arrayBuffer);
      documentMetadata = {
        name: formRequest.documentUrl.split('/').pop() || 'unknown',
        contentType: response.headers.get('content-type') || 'application/octet-stream',
        size: arrayBuffer.byteLength
      };
    } else if (formRequest.documentContent) {
      documentBuffer = Buffer.from(formRequest.documentContent, 'base64');
      documentMetadata = {
        name: 'uploaded-form',
        contentType: 'application/pdf',
        size: documentBuffer.length
      };
    } else {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "No document content provided" }
      }, request);
    }

    // Create processing record
    const processingId = uuidv4();
    const now = new Date().toISOString();
    const startTime = Date.now();

    const processingRecord = {
      id: processingId,
      documentId: formRequest.documentId,
      formType: formRequest.formType,
      status: ProcessingStatus.PROCESSING,
      organizationId: formRequest.organizationId,
      projectId: formRequest.projectId,
      configuration: formRequest.configuration || {},
      metadata: {
        ...formRequest.metadata,
        document: documentMetadata
      },
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId
    };

    await db.createItem('form-processing', processingRecord);

    // Process form with AI
    const processingResult = await processFormWithAI(
      documentBuffer,
      formRequest.formType,
      formRequest.configuration || {}
    );

    const processingTime = Date.now() - startTime;

    // Create response
    const response: FormProcessingResponse = {
      processingId,
      documentId: formRequest.documentId,
      formType: formRequest.formType,
      status: ProcessingStatus.COMPLETED,
      extractedFields: processingResult.fields,
      tables: processingResult.tables,
      signatures: processingResult.signatures,
      validation: processingResult.validation,
      metadata: {
        processingTime,
        modelUsed: processingResult.modelUsed,
        confidence: processingResult.confidence,
        pageCount: processingResult.pageCount
      }
    };

    // Update processing record
    const updatedRecord = {
      ...processingRecord,
      id: processingId,
      status: ProcessingStatus.COMPLETED,
      results: response,
      completedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('form-processing', updatedRecord);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "form_processed",
      userId: user.id,
      organizationId: formRequest.organizationId,
      projectId: formRequest.projectId,
      timestamp: now,
      details: {
        processingId,
        formType: formRequest.formType,
        fieldsExtracted: processingResult.fields.length,
        tablesExtracted: processingResult.tables?.length || 0,
        signaturesFound: processingResult.signatures?.length || 0,
        processingTime,
        confidence: processingResult.confidence
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'FormProcessed',
      aggregateId: processingId,
      aggregateType: 'FormProcessing',
      version: 1,
      data: {
        processing: updatedRecord,
        results: response,
        processedBy: user.id
      },
      userId: user.id,
      organizationId: formRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Form processed successfully", {
      correlationId,
      processingId,
      formType: formRequest.formType,
      fieldsExtracted: processingResult.fields.length,
      processingTime,
      confidence: processingResult.confidence,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Process form failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkFormProcessingLimits(organizationId: string): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Get organization to check tier
    const organization = await db.readItem('organizations', organizationId, organizationId);
    if (!organization) {
      return { allowed: false, reason: 'Organization not found' };
    }

    const orgData = organization as any;
    const tier = orgData.tier || 'FREE';

    // Define tier limits
    const limits: { [key: string]: { maxFormsPerMonth: number } } = {
      'FREE': { maxFormsPerMonth: 50 },
      'PROFESSIONAL': { maxFormsPerMonth: 1000 },
      'ENTERPRISE': { maxFormsPerMonth: -1 } // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];

    if (limit.maxFormsPerMonth === -1) {
      return { allowed: true };
    }

    // Check monthly usage
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const monthlyUsageQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
    const usageResult = await db.queryItems('form-processing', monthlyUsageQuery, [organizationId, startOfMonth.toISOString()]);
    const monthlyUsage = Number(usageResult[0]) || 0;

    if (monthlyUsage >= limit.maxFormsPerMonth) {
      return {
        allowed: false,
        reason: `Monthly form processing limit reached (${limit.maxFormsPerMonth})`
      };
    }

    return { allowed: true };

  } catch (error) {
    logger.error('Failed to check form processing limits', { error, organizationId });
    return { allowed: false, reason: 'Failed to check limits' };
  }
}

async function processFormWithAI(documentBuffer: Buffer, formType: FormType, configuration: any): Promise<any> {
  try {
    // Production Azure Document Intelligence integration
    const endpoint = process.env.AI_DOCUMENT_INTELLIGENCE_ENDPOINT;
    const key = process.env.AI_DOCUMENT_INTELLIGENCE_KEY;

    if (!endpoint || !key) {
      throw new Error('Azure Document Intelligence credentials not configured');
    }

    const client = new DocumentAnalysisClient(endpoint, new AzureKeyCredential(key));

    // Determine the appropriate model based on form type
    let modelId = 'prebuilt-layout';
    switch (formType) {
      case FormType.INVOICE:
        modelId = 'prebuilt-invoice';
        break;
      case FormType.RECEIPT:
        modelId = 'prebuilt-receipt';
        break;
      case FormType.CONTRACT:
        modelId = 'prebuilt-contract';
        break;
      case FormType.TAX_FORM:
        modelId = 'prebuilt-tax.us.w2';
        break;
      case FormType.INSURANCE_CLAIM:
      case FormType.APPLICATION:
      case FormType.SURVEY:
      case FormType.MEDICAL_FORM:
        modelId = 'prebuilt-document';
        break;
      case FormType.CUSTOM:
        modelId = configuration.templateId || 'prebuilt-layout';
        break;
      default:
        modelId = 'prebuilt-layout';
    }

    logger.info('Starting Azure Document Intelligence analysis', {
      formType,
      modelId,
      documentSize: documentBuffer.length,
      confidenceThreshold: configuration.confidenceThreshold || 0.7
    });

    // Analyze document with Azure Document Intelligence
    const poller = await client.beginAnalyzeDocument(modelId, documentBuffer);
    const result = await poller.pollUntilDone();

    if (!result) {
      throw new Error('No analysis result received from Azure Document Intelligence');
    }

    // Extract fields from the analysis result
    const extractedFields: ExtractedField[] = [];

    if (result.documents && result.documents.length > 0) {
      const document = result.documents[0];

      // Process document fields
      if (document.fields) {
        for (const [fieldName, field] of Object.entries(document.fields)) {
          const fieldValue = (field as any).content || (field as any).value;
          const fieldConfidence = (field as any).confidence || 0;

          if (fieldValue !== undefined && fieldConfidence >= (configuration.confidenceThreshold || 0.7)) {
            const extractedField: ExtractedField = {
              name: fieldName,
              value: fieldValue,
              type: mapFieldType((field as any).kind || 'string'),
              confidence: fieldConfidence,
              page: (field as any).boundingRegions?.[0]?.pageNumber || 1,
              validated: fieldConfidence >= 0.8
            };

            // Add bounding box if available
            if ((field as any).boundingRegions && (field as any).boundingRegions.length > 0) {
              const region = (field as any).boundingRegions[0];
              if (region.polygon && region.polygon.length >= 4) {
                const points = region.polygon;
                const xCoords = points.map((point: any) => point.x);
                const yCoords = points.map((point: any) => point.y);
                extractedField.boundingBox = {
                  x: Math.min(...xCoords),
                  y: Math.min(...yCoords),
                  width: Math.max(...xCoords) - Math.min(...xCoords),
                  height: Math.max(...yCoords) - Math.min(...yCoords)
                };
              }
            }

            extractedFields.push(extractedField);
          }
        }
      }
    }

    // Extract tables if requested
    const extractedTables: any[] = [];
    if (configuration.extractTables && result.tables) {
      for (const table of result.tables) {
        if (table.cells && table.rowCount && table.columnCount) {
          // Build table structure
          const headers: string[] = [];
          const rows: string[][] = [];

          // Initialize rows array
          for (let i = 0; i < table.rowCount; i++) {
            rows[i] = new Array(table.columnCount).fill('');
          }

          // Fill table data
          for (const cell of table.cells) {
            const content = cell.content || '';
            if (cell.rowIndex === 0) {
              headers[cell.columnIndex] = content;
            }
            if (cell.rowIndex < table.rowCount && cell.columnIndex < table.columnCount) {
              rows[cell.rowIndex][cell.columnIndex] = content;
            }
          }

          extractedTables.push({
            headers: headers.length > 0 ? headers : rows[0] || [],
            rows: headers.length > 0 ? rows.slice(1) : rows.slice(1),
            confidence: table.boundingRegions?.[0] ? 0.85 : 0.7, // Estimate confidence
            page: table.boundingRegions?.[0]?.pageNumber || 1
          });
        }
      }
    }

    // Extract signatures if requested (simplified detection)
    const extractedSignatures: any[] = [];
    if (configuration.extractSignatures) {
      // Note: Azure Document Intelligence doesn't have built-in signature detection
      // This would require custom model training or additional image analysis
      logger.info('Signature extraction requested but not implemented with current model');
    }

    // Calculate overall confidence
    const overallConfidence = extractedFields.length > 0
      ? extractedFields.reduce((sum, field) => sum + field.confidence, 0) / extractedFields.length
      : 0.5;

    // Validate extracted data
    const validation = validateExtractedData(extractedFields, configuration);

    const processingResult = {
      fields: extractedFields,
      tables: extractedTables,
      signatures: extractedSignatures,
      validation,
      modelUsed: modelId,
      confidence: overallConfidence,
      pageCount: result.pages?.length || 1
    };

    logger.info('Azure Document Intelligence processing completed', {
      formType,
      modelId,
      fieldsExtracted: extractedFields.length,
      tablesExtracted: extractedTables.length,
      confidence: overallConfidence,
      pageCount: processingResult.pageCount
    });

    return processingResult;

  } catch (error) {
    logger.error('Azure Document Intelligence processing failed', {
      error: error instanceof Error ? error.message : String(error),
      formType
    });

    // Fallback to basic processing
    return {
      fields: [],
      tables: [],
      signatures: [],
      validation: {
        isValid: false,
        errors: ['AI processing failed'],
        warnings: [],
        completeness: 0
      },
      modelUsed: 'fallback',
      confidence: 0,
      pageCount: 1
    };
  }
}

/**
 * Map Azure Document Intelligence field types to our FieldType enum
 */
function mapFieldType(kind: string): FieldType {
  switch (kind) {
    case 'string':
      return FieldType.TEXT;
    case 'number':
      return FieldType.NUMBER;
    case 'date':
      return FieldType.DATE;
    case 'boolean':
      return FieldType.BOOLEAN;
    case 'phoneNumber':
      return FieldType.PHONE;
    case 'currency':
      return FieldType.CURRENCY;
    case 'percentage':
      return FieldType.PERCENTAGE;
    case 'address':
      return FieldType.ADDRESS;
    default:
      return FieldType.TEXT;
  }
}

/**
 * Validate extracted data based on configuration
 */
function validateExtractedData(fields: ExtractedField[], configuration: any): any {
  const errors: string[] = [];
  const warnings: string[] = [];
  let validFieldCount = 0;

  // Check required custom fields
  if (configuration.customFields) {
    for (const customField of configuration.customFields) {
      if (customField.required) {
        const foundField = fields.find(f => f.name === customField.name);
        if (!foundField) {
          errors.push(`Required field '${customField.name}' not found`);
        } else if (!foundField.value || foundField.value === '') {
          errors.push(`Required field '${customField.name}' is empty`);
        } else {
          validFieldCount++;
        }
      }
    }
  }

  // Check field patterns
  for (const field of fields) {
    const customField = configuration.customFields?.find((cf: any) => cf.name === field.name);
    if (customField?.pattern) {
      try {
        const regex = new RegExp(customField.pattern);
        if (!regex.test(String(field.value))) {
          warnings.push(`Field '${field.name}' does not match expected pattern`);
        }
      } catch (error) {
        warnings.push(`Invalid pattern for field '${field.name}'`);
      }
    }

    // Check confidence threshold
    if (field.confidence < (configuration.confidenceThreshold || 0.7)) {
      warnings.push(`Low confidence (${field.confidence.toFixed(2)}) for field '${field.name}'`);
    } else {
      validFieldCount++;
    }
  }

  const totalExpectedFields = (configuration.customFields?.length || 0) + fields.length;
  const completeness = totalExpectedFields > 0 ? validFieldCount / totalExpectedFields : 0;

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    completeness: Math.min(completeness, 1.0)
  };
}

// Register functions
app.http('ai-form-process', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/forms/process',
  handler: processForm
});
