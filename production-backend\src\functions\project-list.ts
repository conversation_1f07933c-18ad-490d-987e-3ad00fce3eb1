/**
 * Project List Function
 * Handles listing projects for the authenticated user
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Validation schema
const listProjectsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  organizationId: Joi.string().uuid().optional(),
  search: Joi.string().max(100).optional(),
  visibility: Joi.string().valid('PRIVATE', 'ORGANIZATION', 'PUBLIC').optional(),
  tags: Joi.string().optional() // Comma-separated tags
});

/**
 * List projects handler
 */
export async function listProjects(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List projects started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listProjectsSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, organizationId, search, visibility, tags } = value;

    // Build query to get projects where user is a member
    let queryText = 'SELECT * FROM c WHERE ARRAY_CONTAINS(c.memberIds, @userId)';
    const parameters: any[] = [user.id];

    // Add tenant isolation
    if (user.tenantId) {
      queryText += ' AND (c.tenantId = @tenantId OR c.createdBy = @userId2)';
      parameters.push(user.tenantId, user.id);
    }

    // Add organization filter if provided
    if (organizationId) {
      queryText += ' AND c.organizationId = @organizationId';
      parameters.push(organizationId);
    }

    // Add visibility filter if provided
    if (visibility) {
      queryText += ' AND c.visibility = @visibility';
      parameters.push(visibility);
    }

    // Add search filter if provided
    if (search) {
      queryText += ' AND (CONTAINS(LOWER(c.name), LOWER(@search)) OR CONTAINS(LOWER(c.description), LOWER(@search)))';
      parameters.push(search);
    }

    // Add tags filter if provided
    if (tags) {
      const tagList = tags.split(',').map((tag: string) => tag.trim());
      queryText += ' AND EXISTS(SELECT VALUE t FROM t IN c.tags WHERE t IN (@tagList))';
      parameters.push(tagList);
    }

    // Add ordering
    queryText += ' ORDER BY c.createdAt DESC';

    // Get total count for pagination
    const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
    const countResult = await db.queryItems('projects', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    // Add pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;

    // Execute query
    const projects = await db.queryItems('projects', paginatedQuery, parameters);

    // Enrich projects with computed fields
    const enrichedProjects = await Promise.all(
      projects.map(async (project: any) => {
        // Get document count
        const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
        const documentCountResult = await db.queryItems('documents', documentCountQuery, [project.id]);
        const documentCount = Number(documentCountResult[0]) || 0;

        // Get workflow count
        const workflowCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
        const workflowCountResult = await db.queryItems('workflows', workflowCountQuery, [project.id]);
        const workflowCount = Number(workflowCountResult[0]) || 0;

        // Get member count
        const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
        const memberCountResult = await db.queryItems('project-members', memberCountQuery, [project.id]);
        const memberCount = Number(memberCountResult[0]) || 0;

        // Get organization name
        let organizationName = 'Unknown';
        try {
          const organization = await db.readItem('organizations', project.organizationId, project.organizationId);
          if (organization) {
            organizationName = (organization as any).name;
          }
        } catch (error) {
          // Organization might not exist or user might not have access
        }

        // Calculate actual storage usage
        const storageUsed = await calculateProjectStorageUsage(project.id);

        return {
          ...project,
          documentCount,
          workflowCount,
          memberCount,
          organizationName,
          storageUsed
        };
      })
    );

    logger.info("Projects listed successfully", {
      correlationId,
      userId: user.id,
      count: projects.length,
      page,
      limit,
      organizationId
    });

    // Create response
    const response = {
      items: enrichedProjects,
      total,
      page,
      limit,
      hasMore: page * limit < total
    };

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List projects failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Calculate actual storage usage for a project
 */
async function calculateProjectStorageUsage(projectId: string): Promise<number> {
  try {
    // Get all documents for the project
    const documentsQuery = 'SELECT c.fileSize, c.blobName FROM c WHERE c.projectId = @projectId';
    const documents = await db.queryItems('documents', documentsQuery, [projectId]);

    let totalSize = 0;

    // Sum up file sizes from document metadata
    for (const doc of documents) {
      const docData = doc as any;
      if (docData.fileSize && typeof docData.fileSize === 'number') {
        totalSize += docData.fileSize;
      } else if (docData.blobName) {
        // If fileSize is not available, try to get it from blob storage
        try {
          const blobSize = await getBlobSize(docData.blobName);
          totalSize += blobSize;
        } catch (error) {
          logger.warn('Failed to get blob size', {
            projectId,
            blobName: docData.blobName,
            error
          });
        }
      }
    }

    // Also include thumbnail storage
    const thumbnailsQuery = 'SELECT c.thumbnailBlobName FROM c WHERE c.projectId = @projectId AND c.thumbnailBlobName IS NOT NULL';
    const thumbnails = await db.queryItems('documents', thumbnailsQuery, [projectId]);

    for (const thumb of thumbnails) {
      const thumbData = thumb as any;
      if (thumbData.thumbnailBlobName) {
        try {
          const thumbSize = await getBlobSize(thumbData.thumbnailBlobName);
          totalSize += thumbSize;
        } catch (error) {
          // Thumbnail might not exist, continue
        }
      }
    }

    return totalSize;

  } catch (error) {
    logger.error('Failed to calculate project storage usage', {
      projectId,
      error: error instanceof Error ? error.message : String(error)
    });
    return 0;
  }
}

/**
 * Get blob size from Azure Storage
 */
async function getBlobSize(blobName: string): Promise<number> {
  try {
    const { BlobServiceClient } = require('@azure/storage-blob');

    const blobServiceClient = BlobServiceClient.fromConnectionString(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ''
    );
    const containerClient = blobServiceClient.getContainerClient('documents');
    const blobClient = containerClient.getBlobClient(blobName);

    const properties = await blobClient.getProperties();
    return properties.contentLength || 0;

  } catch (error) {
    logger.warn('Failed to get blob size from storage', {
      blobName,
      error: error instanceof Error ? error.message : String(error)
    });
    return 0;
  }
}

// Note: Registration moved to project-create.ts for combined handling
// app.http('project-list', {
//   methods: ['GET', 'OPTIONS'],
//   authLevel: 'function',
//   route: 'projects',
//   handler: listProjects
// });
