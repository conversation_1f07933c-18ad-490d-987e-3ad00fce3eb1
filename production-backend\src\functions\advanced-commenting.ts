/**
 * Advanced Commenting Function
 * Handles advanced commenting system with threading, mentions, and reactions
 * Migrated from old-arch/src/collaboration-service/comments/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService, NotificationChannelType } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// Comment types and enums
enum CommentType {
  GENERAL = 'GENERAL',
  SUGGESTION = 'SUGGESTION',
  QUESTION = 'QUESTION',
  ISSUE = 'ISSUE',
  APPROVAL = 'APPROVAL',
  REJECTION = 'REJECTION'
}

enum CommentStatus {
  ACTIVE = 'ACTIVE',
  RESOLVED = 'RESOLVED',
  DELETED = 'DELETED',
  HIDDEN = 'HIDDEN'
}

enum ReactionType {
  LIKE = 'LIKE',
  DISLIKE = 'DISLIKE',
  LOVE = 'LOVE',
  LAUGH = 'LAUGH',
  CONFUSED = 'CONFUSED',
  THUMBS_UP = 'THUMBS_UP',
  THUMBS_DOWN = 'THUMBS_DOWN'
}

// Validation schemas
const createCommentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  content: Joi.string().min(1).max(5000).required(),
  type: Joi.string().valid(...Object.values(CommentType)).default(CommentType.GENERAL),
  parentCommentId: Joi.string().uuid().optional(),
  position: Joi.object({
    page: Joi.number().min(1).optional(),
    x: Joi.number().min(0).optional(),
    y: Joi.number().min(0).optional(),
    startOffset: Joi.number().min(0).optional(),
    endOffset: Joi.number().min(0).optional(),
    selectedText: Joi.string().max(500).optional()
  }).optional(),
  mentions: Joi.array().items(Joi.string().uuid()).optional(),
  attachments: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    url: Joi.string().uri().required(),
    type: Joi.string().required(),
    size: Joi.number().optional()
  })).optional(),
  metadata: Joi.object().optional()
});

const updateCommentSchema = Joi.object({
  commentId: Joi.string().uuid().required(),
  content: Joi.string().min(1).max(5000).optional(),
  status: Joi.string().valid(...Object.values(CommentStatus)).optional(),
  metadata: Joi.object().optional()
});

const addReactionSchema = Joi.object({
  commentId: Joi.string().uuid().required(),
  reactionType: Joi.string().valid(...Object.values(ReactionType)).required()
});

interface CreateCommentRequest {
  documentId: string;
  content: string;
  type?: CommentType;
  parentCommentId?: string;
  position?: {
    page?: number;
    x?: number;
    y?: number;
    startOffset?: number;
    endOffset?: number;
    selectedText?: string;
  };
  mentions?: string[];
  attachments?: Array<{
    name: string;
    url: string;
    type: string;
    size?: number;
  }>;
  metadata?: any;
}

interface Comment {
  id: string;
  documentId: string;
  content: string;
  type: CommentType;
  status: CommentStatus;
  parentCommentId?: string;
  position?: any;
  mentions: string[];
  attachments: any[];
  metadata: any;
  createdBy: string;
  createdAt: string;
  updatedBy?: string;
  updatedAt?: string;
  resolvedBy?: string;
  resolvedAt?: string;
  reactions: Array<{
    type: ReactionType;
    userId: string;
    createdAt: string;
  }>;
  replies: Comment[];
  organizationId: string;
  tenantId: string;
}

/**
 * Create comment handler
 */
export async function createComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create comment started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createCommentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const commentRequest: CreateCommentRequest = value;

    // Get document and check access
    const document = await db.readItem('documents', commentRequest.documentId, commentRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;

    // Check document access
    const hasAccess = await checkDocumentAccess(documentData, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to document" }
      }, request);
    }

    // Validate parent comment if provided
    if (commentRequest.parentCommentId) {
      const parentComment = await db.readItem('comments', commentRequest.parentCommentId, commentRequest.parentCommentId);
      if (!parentComment) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Parent comment not found" }
        }, request);
      }

      const parentCommentData = parentComment as any;
      if (parentCommentData.documentId !== commentRequest.documentId) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Parent comment belongs to different document" }
        }, request);
      }
    }

    // Validate mentions
    if (commentRequest.mentions && commentRequest.mentions.length > 0) {
      const validMentions = await validateMentions(commentRequest.mentions, documentData.organizationId);
      if (validMentions.length !== commentRequest.mentions.length) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Some mentioned users are not valid" }
        }, request);
      }
    }

    // Create comment
    const commentId = uuidv4();
    const now = new Date().toISOString();

    const comment: Comment = {
      id: commentId,
      documentId: commentRequest.documentId,
      content: commentRequest.content,
      type: commentRequest.type || CommentType.GENERAL,
      status: CommentStatus.ACTIVE,
      parentCommentId: commentRequest.parentCommentId,
      position: commentRequest.position,
      mentions: commentRequest.mentions || [],
      attachments: commentRequest.attachments || [],
      metadata: commentRequest.metadata || {},
      createdBy: user.id,
      createdAt: now,
      reactions: [],
      replies: [],
      organizationId: documentData.organizationId,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('comments', comment);

    // Update document comment count
    await updateDocumentCommentCount(commentRequest.documentId, 1);

    // Send notifications for mentions
    if (commentRequest.mentions && commentRequest.mentions.length > 0) {
      await sendMentionNotifications(comment, commentRequest.mentions, user, documentData);
    }

    // Send notification to document owner if not the commenter
    if (documentData.createdBy !== user.id) {
      await sendCommentNotification(comment, documentData.createdBy, user, documentData);
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "comment_created",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      documentId: commentRequest.documentId,
      timestamp: now,
      details: {
        commentId,
        commentType: comment.type,
        documentName: documentData.name,
        isReply: !!commentRequest.parentCommentId,
        mentionCount: commentRequest.mentions?.length || 0,
        hasAttachments: (commentRequest.attachments?.length || 0) > 0
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'CommentCreated',
      aggregateId: commentId,
      aggregateType: 'Comment',
      version: 1,
      data: {
        comment,
        document: documentData,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Comment created successfully", {
      correlationId,
      commentId,
      documentId: commentRequest.documentId,
      documentName: documentData.name,
      commentType: comment.type,
      isReply: !!commentRequest.parentCommentId,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        comment: {
          id: commentId,
          content: comment.content,
          type: comment.type,
          status: comment.status,
          position: comment.position,
          mentions: comment.mentions,
          attachments: comment.attachments,
          createdBy: user.id,
          createdAt: now,
          reactions: [],
          replyCount: 0
        },
        message: "Comment created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create comment failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Add reaction to comment handler
 */
export async function addReactionToComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Add reaction to comment started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = addReactionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const reactionRequest = value;

    // Get comment
    const comment = await db.readItem('comments', reactionRequest.commentId, reactionRequest.commentId);
    if (!comment) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Comment not found" }
      }, request);
    }

    const commentData = comment as any;

    // Check if comment is active
    if (commentData.status !== CommentStatus.ACTIVE) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Cannot react to inactive comment" }
      }, request);
    }

    // Check document access
    const document = await db.readItem('documents', commentData.documentId, commentData.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;
    const hasAccess = await checkDocumentAccess(documentData, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to document" }
      }, request);
    }

    // Check if user already reacted with this type
    const existingReaction = commentData.reactions.find((r: any) =>
      r.userId === user.id && r.type === reactionRequest.reactionType
    );

    if (existingReaction) {
      // Remove existing reaction (toggle)
      commentData.reactions = commentData.reactions.filter((r: any) =>
        !(r.userId === user.id && r.type === reactionRequest.reactionType)
      );
    } else {
      // Remove any other reaction from this user and add new one
      commentData.reactions = commentData.reactions.filter((r: any) => r.userId !== user.id);
      commentData.reactions.push({
        type: reactionRequest.reactionType,
        userId: user.id,
        createdAt: new Date().toISOString()
      });
    }

    // Update comment
    const updatedComment = {
      ...commentData,
      id: reactionRequest.commentId,
      updatedAt: new Date().toISOString(),
      updatedBy: user.id
    };

    await db.updateItem('comments', updatedComment);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: existingReaction ? "comment_reaction_removed" : "comment_reaction_added",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      documentId: commentData.documentId,
      timestamp: new Date().toISOString(),
      details: {
        commentId: reactionRequest.commentId,
        reactionType: reactionRequest.reactionType,
        documentName: documentData.name
      },
      tenantId: user.tenantId
    });

    // Notify comment author if not the reactor
    if (commentData.createdBy !== user.id && !existingReaction) {
      await sendReactionNotification(commentData, reactionRequest.reactionType, user, documentData);
    }

    logger.info("Comment reaction updated successfully", {
      correlationId,
      commentId: reactionRequest.commentId,
      reactionType: reactionRequest.reactionType,
      action: existingReaction ? 'removed' : 'added',
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        commentId: reactionRequest.commentId,
        reactionType: reactionRequest.reactionType,
        action: existingReaction ? 'removed' : 'added',
        reactions: updatedComment.reactions,
        message: `Reaction ${existingReaction ? 'removed' : 'added'} successfully`
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Add reaction to comment failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkDocumentAccess(document: any, userId: string): Promise<boolean> {
  try {
    // Check if user is the owner
    if (document.createdBy === userId) {
      return true;
    }

    // Check organization membership
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);

    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check document access', { error, documentId: document.id, userId });
    return false;
  }
}

async function validateMentions(mentions: string[], organizationId: string): Promise<string[]> {
  try {
    const validMentions: string[] = [];

    for (const userId of mentions) {
      const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);

      if (memberships.length > 0) {
        validMentions.push(userId);
      }
    }

    return validMentions;
  } catch (error) {
    logger.error('Failed to validate mentions', { error, mentions, organizationId });
    return [];
  }
}

async function updateDocumentCommentCount(documentId: string, increment: number): Promise<void> {
  try {
    const document = await db.readItem('documents', documentId, documentId);
    if (document) {
      const documentData = document as any;
      const updatedDocument = {
        ...documentData,
        id: documentId,
        commentCount: (documentData.commentCount || 0) + increment,
        updatedAt: new Date().toISOString()
      };
      await db.updateItem('documents', updatedDocument);
    }
  } catch (error) {
    logger.error('Failed to update document comment count', { error, documentId, increment });
  }
}

async function sendMentionNotifications(comment: Comment, mentions: string[], user: any, document: any): Promise<void> {
  try {
    for (const mentionedUserId of mentions) {
      await notificationService.sendNotification({
        userId: mentionedUserId,
        type: 'comment_mention',
        title: `You were mentioned in a comment`,
        message: `${user.name || user.email} mentioned you in a comment on "${document.name}"`,
        resourceId: comment.documentId,
        resourceType: 'document',
        metadata: {
          commentId: comment.id,
          documentId: comment.documentId,
          documentName: document.name,
          commentContent: comment.content.substring(0, 100) + (comment.content.length > 100 ? '...' : ''),
          mentionedBy: user.id
        },
        channels: [NotificationChannelType.EMAIL, NotificationChannelType.IN_APP],
        priority: 'normal'
      });
    }
  } catch (error) {
    logger.error('Failed to send mention notifications', { error, commentId: comment.id });
  }
}

async function sendCommentNotification(comment: Comment, recipientId: string, user: any, document: any): Promise<void> {
  try {
    await notificationService.sendNotification({
      userId: recipientId,
      type: 'new_comment',
      title: `New comment on your document`,
      message: `${user.name || user.email} commented on "${document.name}"`,
      resourceId: comment.documentId,
      resourceType: 'document',
      metadata: {
        commentId: comment.id,
        documentId: comment.documentId,
        documentName: document.name,
        commentContent: comment.content.substring(0, 100) + (comment.content.length > 100 ? '...' : ''),
        commentedBy: user.id
      },
      channels: [NotificationChannelType.EMAIL, NotificationChannelType.IN_APP],
      priority: 'normal'
    });
  } catch (error) {
    logger.error('Failed to send comment notification', { error, commentId: comment.id });
  }
}

async function sendReactionNotification(comment: any, reactionType: ReactionType, user: any, document: any): Promise<void> {
  try {
    await notificationService.sendNotification({
      userId: comment.createdBy,
      type: 'comment_reaction',
      title: `Someone reacted to your comment`,
      message: `${user.name || user.email} reacted with ${reactionType} to your comment on "${document.name}"`,
      resourceId: comment.documentId,
      resourceType: 'document',
      metadata: {
        commentId: comment.id,
        documentId: comment.documentId,
        documentName: document.name,
        reactionType,
        reactedBy: user.id
      },
      channels: [NotificationChannelType.IN_APP],
      priority: 'low'
    });
  } catch (error) {
    logger.error('Failed to send reaction notification', { error, commentId: comment.id });
  }
}

// Register functions
app.http('comment-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'comments',
  handler: createComment
});

app.http('comment-reaction-add', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'comments/reactions',
  handler: addReactionToComment
});
