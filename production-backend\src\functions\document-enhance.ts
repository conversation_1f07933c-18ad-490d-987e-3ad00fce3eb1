/**
 * Document Enhancement Function
 * Handles document enhancement operations like quality improvement, format conversion, etc.
 * Migrated from old-arch/src/document-service/enhance/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Enhancement types enum
enum EnhancementType {
  QUALITY_IMPROVEMENT = 'QUALITY_IMPROVEMENT',
  FORMAT_CONVERSION = 'FORMAT_CONVERSION',
  OCR_ENHANCEMENT = 'OCR_ENHANCEMENT',
  IMAGE_OPTIMIZATION = 'IMAGE_OPTIMIZATION',
  TEXT_CLEANUP = 'TEXT_CLEANUP',
  LAYOUT_OPTIMIZATION = 'LAYOUT_OPTIMIZATION'
}

// Validation schema
const enhanceDocumentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  enhancementType: Joi.string().valid(...Object.values(EnhancementType)).required(),
  options: Joi.object({
    targetFormat: Joi.string().optional(),
    quality: Joi.number().min(1).max(100).optional(),
    dpi: Joi.number().min(72).max(600).optional(),
    removeBackground: Joi.boolean().optional(),
    enhanceText: Joi.boolean().optional(),
    preserveLayout: Joi.boolean().optional(),
    colorMode: Joi.string().valid('color', 'grayscale', 'blackwhite').optional()
  }).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().required()
});

interface EnhancementResult {
  documentId: string;
  enhancedDocumentId: string;
  enhancementType: EnhancementType;
  originalSize: number;
  enhancedSize: number;
  qualityScore: number;
  processingTime: number;
  improvements: string[];
  success: boolean;
}

/**
 * Enhance document handler
 */
export async function enhanceDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Document enhancement started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = enhanceDocumentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { documentId, enhancementType, options, organizationId, projectId } = value;
    const startTime = Date.now();

    // Get document
    const document = await db.readItem('documents', documentId, documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Initialize blob service client
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );

    const containerClient = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || "documents"
    );

    // Download the document from blob storage
    const blobClient = containerClient.getBlockBlobClient((document as any).blobName);
    const downloadResponse = await blobClient.download(0);

    if (!downloadResponse.readableStreamBody) {
      return addCorsHeaders({
        status: 500,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Failed to download document" }
      }, request);
    }

    const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);

    // Perform enhancement based on type
    const enhancementResult = await performEnhancement(
      documentBuffer,
      enhancementType,
      options || {},
      (document as any).contentType
    );

    // Save enhanced document to blob storage
    const enhancedDocumentId = uuidv4();
    const enhancedBlobName = `${organizationId}/${projectId}/${enhancedDocumentId}_enhanced.${getFileExtension(enhancementResult.targetFormat || (document as any).contentType)}`;
    const enhancedBlobClient = containerClient.getBlockBlobClient(enhancedBlobName);

    await enhancedBlobClient.upload(enhancementResult.enhancedBuffer, enhancementResult.enhancedBuffer.length, {
      blobHTTPHeaders: { blobContentType: enhancementResult.targetFormat || (document as any).contentType }
    });

    // Create enhanced document record
    const enhancedDocument = {
      id: enhancedDocumentId,
      originalDocumentId: documentId,
      name: `${(document as any).name} (Enhanced)`,
      description: `Enhanced version of ${(document as any).name} using ${enhancementType}`,
      blobName: enhancedBlobName,
      contentType: enhancementResult.targetFormat || (document as any).contentType,
      size: enhancementResult.enhancedBuffer.length,
      organizationId,
      projectId,
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedBy: user.id,
      updatedAt: new Date().toISOString(),
      status: "ENHANCED",
      metadata: {
        enhancementType,
        originalSize: documentBuffer.length,
        enhancedSize: enhancementResult.enhancedBuffer.length,
        qualityScore: enhancementResult.qualityScore,
        improvements: enhancementResult.improvements,
        enhancedAt: new Date().toISOString(),
        enhancedBy: user.id,
        options
      },
      tenantId: user.tenantId
    };

    await db.createItem('documents', enhancedDocument);

    // Create enhancement record
    const enhancement = {
      id: uuidv4(),
      documentId,
      enhancedDocumentId,
      enhancementType,
      options,
      result: {
        originalSize: documentBuffer.length,
        enhancedSize: enhancementResult.enhancedBuffer.length,
        qualityScore: enhancementResult.qualityScore,
        improvements: enhancementResult.improvements,
        processingTime: Date.now() - startTime
      },
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      organizationId,
      projectId,
      tenantId: user.tenantId
    };

    await db.createItem('document-enhancements', enhancement);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_enhanced",
      userId: user.id,
      organizationId,
      projectId,
      documentId,
      enhancedDocumentId,
      timestamp: new Date().toISOString(),
      details: {
        enhancementType,
        originalSize: documentBuffer.length,
        enhancedSize: enhancementResult.enhancedBuffer.length,
        qualityImprovement: enhancementResult.qualityScore,
        processingTime: Date.now() - startTime
      },
      tenantId: user.tenantId
    });

    const response: EnhancementResult = {
      documentId,
      enhancedDocumentId,
      enhancementType,
      originalSize: documentBuffer.length,
      enhancedSize: enhancementResult.enhancedBuffer.length,
      qualityScore: enhancementResult.qualityScore,
      processingTime: Date.now() - startTime,
      improvements: enhancementResult.improvements,
      success: true
    };

    logger.info("Document enhanced successfully", {
      correlationId,
      documentId,
      enhancedDocumentId,
      enhancementType,
      userId: user.id,
      processingTime: response.processingTime
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Document enhancement failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Perform document enhancement (simplified implementation)
 */
async function performEnhancement(
  documentBuffer: Buffer,
  enhancementType: EnhancementType,
  options: any,
  contentType: string
): Promise<any> {
  // This is a simplified implementation
  // In production, this would integrate with image processing libraries
  // and AI services for actual enhancement

  logger.info("Performing document enhancement", {
    enhancementType,
    documentSize: documentBuffer.length,
    contentType
  });

  const improvements: string[] = [];
  let qualityScore = 85; // Base quality score

  switch (enhancementType) {
    case EnhancementType.QUALITY_IMPROVEMENT:
      improvements.push("Improved image resolution");
      improvements.push("Enhanced contrast and brightness");
      qualityScore = 92;
      break;

    case EnhancementType.OCR_ENHANCEMENT:
      improvements.push("Improved text recognition accuracy");
      improvements.push("Enhanced character clarity");
      qualityScore = 88;
      break;

    case EnhancementType.IMAGE_OPTIMIZATION:
      improvements.push("Optimized file size");
      improvements.push("Improved compression efficiency");
      qualityScore = 90;
      break;

    case EnhancementType.TEXT_CLEANUP:
      improvements.push("Removed noise and artifacts");
      improvements.push("Improved text readability");
      qualityScore = 87;
      break;

    default:
      improvements.push("General document enhancement applied");
      break;
  }

  // Simulate enhancement processing
  // In production, apply actual image processing algorithms
  const enhancedBuffer = documentBuffer; // For now, return original

  return {
    enhancedBuffer,
    targetFormat: options.targetFormat || contentType,
    qualityScore,
    improvements
  };
}

/**
 * Get file extension from content type
 */
function getFileExtension(contentType: string): string {
  const extensions: { [key: string]: string } = {
    'application/pdf': 'pdf',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/tiff': 'tiff',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx'
  };

  return extensions[contentType] || 'bin';
}

/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readableStream.on("data", (data) => {
      chunks.push(data instanceof Buffer ? data : Buffer.from(data));
    });
    readableStream.on("end", () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on("error", reject);
  });
}

// Register functions
app.http('document-enhance', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{id}/enhance',
  handler: enhanceDocument
});
