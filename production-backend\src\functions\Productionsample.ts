/**
 * Production Sample Function
 * A simple test function to verify the Azure Functions setup is working
 */
import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";

export async function Productionsample(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    context.log(`Http function processed request for url "${request.url}"`);

    const name = request.query.get('name') || await request.text() || 'world';

    return {
        status: 200,
        headers: {
            "Content-Type": "application/json"
        },
        jsonBody: {
            message: `Hello, ${name}!`,
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development'
        }
    };
}

app.http('Production-sample', {
    methods: ['GET', 'POST'],
    authLevel: 'anonymous',
    route: 'sample',
    handler: Productionsample
});
