/**
 * Simple Event Grid Setup Script
 * Creates essential Event Grid subscriptions for your application
 */

const { execSync } = require('child_process');

console.log('🚀 Simple Event Grid Setup Starting...\n');

// Configuration
const config = {
  resourceGroup: 'docucontext',
  functionAppName: 'hepzlogic',
  eventGridTopicName: 'hepzeg',
  storageAccountName: 'stdocucontex900520441468',
  location: 'eastus'
};

/**
 * Execute command with error handling
 */
function runCommand(command, description) {
  try {
    console.log(`📋 ${description}...`);
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description} - SUCCESS`);
    return true;
  } catch (error) {
    console.log(`⚠️ ${description} - FAILED (may already exist)`);
    return false;
  }
}

/**
 * Get subscription ID
 */
function getSubscriptionId() {
  try {
    const result = execSync('az account show --query id --output tsv', { encoding: 'utf8' });
    return result.trim();
  } catch (error) {
    console.error('❌ Failed to get subscription ID');
    process.exit(1);
  }
}

/**
 * Get Function App URL
 */
function getFunctionAppUrl() {
  try {
    const command = `az functionapp show --name ${config.functionAppName} --resource-group ${config.resourceGroup} --query defaultHostName --output tsv`;
    const result = execSync(command, { encoding: 'utf8' });
    return result.trim();
  } catch (error) {
    console.error('❌ Failed to get Function App URL');
    process.exit(1);
  }
}

// Main setup
try {
  // Get basic info
  const subscriptionId = getSubscriptionId();
  const functionAppUrl = getFunctionAppUrl();
  
  console.log(`📋 Subscription: ${subscriptionId}`);
  console.log(`📋 Function App: https://${functionAppUrl}\n`);

  // Resource IDs
  const storageAccountId = `/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.Storage/storageAccounts/${config.storageAccountName}`;
  const eventGridTopicId = `/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.EventGrid/topics/${config.eventGridTopicName}`;
  const storageSystemTopic = `${config.storageAccountName}-events`;

  console.log('🔧 Creating Event Grid Configuration...\n');

  // 1. Create Storage System Topic
  runCommand(
    `az eventgrid system-topic create --name "${storageSystemTopic}" --resource-group "${config.resourceGroup}" --source "${storageAccountId}" --topic-type "Microsoft.Storage.StorageAccounts" --location "${config.location}"`,
    'Creating Storage System Topic'
  );

  // 2. Storage Events to Function App
  runCommand(
    `az eventgrid event-subscription create --name "storage-to-function" --source-resource-id "/subscriptions/${subscriptionId}/resourceGroups/${config.resourceGroup}/providers/Microsoft.EventGrid/systemTopics/${storageSystemTopic}" --endpoint "https://${functionAppUrl}/api/eventgrid/webhook" --endpoint-type webhook --included-event-types "Microsoft.Storage.BlobCreated" "Microsoft.Storage.BlobDeleted" --max-delivery-attempts 3 --event-ttl 1440`,
    'Creating Storage Events subscription'
  );

  // 3. Custom Events to Function App
  runCommand(
    `az eventgrid event-subscription create --name "custom-events-to-function" --source-resource-id "${eventGridTopicId}" --endpoint "https://${functionAppUrl}/api/eventgrid/webhook" --endpoint-type webhook --max-delivery-attempts 3 --event-ttl 1440`,
    'Creating Custom Events subscription'
  );

  // 4. Create Dead Letter Container
  runCommand(
    `az storage container create --name "event-dead-letters" --account-name "${config.storageAccountName}" --auth-mode login`,
    'Creating Dead Letter Storage'
  );

  console.log('\n🎉 Event Grid Setup Complete!\n');
  
  console.log('📊 Configuration Summary:');
  console.log(`   ✅ Storage System Topic: ${storageSystemTopic}`);
  console.log(`   ✅ Storage Events → Function App`);
  console.log(`   ✅ Custom Events → Function App`);
  console.log(`   ✅ Dead Letter Storage: event-dead-letters`);
  console.log(`   ✅ Function App Webhook: https://${functionAppUrl}/api/eventgrid/webhook\n`);
  
  console.log('🔄 Next Steps:');
  console.log('   1. Deploy your Function App to Azure');
  console.log('   2. Test with: node scripts/test-event-grid.js');
  console.log('   3. Monitor events in Azure Portal');

} catch (error) {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
}
